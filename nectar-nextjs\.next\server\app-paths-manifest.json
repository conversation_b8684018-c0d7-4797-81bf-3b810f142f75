{"/_not-found/page": "app/_not-found/page.js", "/api/appointment-blocks/route": "app/api/appointment-blocks/route.js", "/api/appointments/[id]/route": "app/api/appointments/[id]/route.js", "/api/appointments/route": "app/api/appointments/route.js", "/api/clinic-settings/route": "app/api/clinic-settings/route.js", "/api/healthcare-professionals/route": "app/api/healthcare-professionals/route.js", "/api/medical-records/route": "app/api/medical-records/route.js", "/api/patient-attachments/route": "app/api/patient-attachments/route.js", "/api/patients/[id]/route": "app/api/patients/[id]/route.js", "/api/patients/route": "app/api/patients/route.js", "/api/profile/route": "app/api/profile/route.js", "/api/settings/profile/route": "app/api/settings/profile/route.js", "/api/settings/route": "app/api/settings/route.js", "/auth/page": "app/auth/page.js", "/dashboard/agenda/page": "app/dashboard/agenda/page.js", "/dashboard/configuracoes/page": "app/dashboard/configuracoes/page.js", "/dashboard/pacientes/[id]/page": "app/dashboard/pacientes/[id]/page.js", "/dashboard/pacientes/page": "app/dashboard/pacientes/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/prontuario/[patientId]/page": "app/dashboard/prontuario/[patientId]/page.js", "/page": "app/page.js"}