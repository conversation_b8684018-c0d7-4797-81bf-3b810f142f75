module.exports = {

"[project]/.next-internal/server/app/api/settings/profile/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/supabase/server.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
;
;
async function createClient() {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createServerClient"])(("TURBOPACK compile-time value", "https://zmwdnemlzndjavlriyrc.supabase.co"), ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inptd2RuZW1sem5kamF2bHJpeXJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4MTE5NTksImV4cCI6MjA2NzM4Nzk1OX0.XNRQjZmMZ7s4aKrJVSQFlu9ASryGJc5fBX6iNnjOPEM"), {
        cookies: {
            getAll () {
                return cookieStore.getAll();
            },
            setAll (cookiesToSet) {
                try {
                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
}}),
"[project]/src/lib/permissions.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DEFAULT_PERMISSIONS": (()=>DEFAULT_PERMISSIONS),
    "canAccessHealthcareProfessional": (()=>canAccessHealthcareProfessional),
    "getUserPermissions": (()=>getUserPermissions),
    "hasPermission": (()=>hasPermission),
    "hasRole": (()=>hasRole),
    "isAdmin": (()=>isAdmin),
    "requirePermission": (()=>requirePermission),
    "requireRole": (()=>requireRole)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://zmwdnemlzndjavlriyrc.supabase.co");
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
// Create a Supabase client with service role key for server-side operations
const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseServiceKey);
async function hasPermission(userId, resource, action) {
    try {
        // Get user role from simplified system
        const { data: user, error: userError } = await supabaseAdmin.from('users').select('role').eq('id', userId).single();
        if (userError || !user) {
            return false;
        }
        // Admin has all permissions
        if (user.role === 'admin') {
            return true;
        }
        // Healthcare professionals have full permissions for their own resources
        if (user.role === 'healthcare_professional') {
            return hasHealthcareProfessionalPermission(resource, action);
        }
        // For secretaries, check user associations to see what they can access
        if (user.role === 'secretary') {
            return await hasSecretaryPermission(userId, resource, action);
        }
        // Assistants have read-only access to basic resources
        if (user.role === 'assistant') {
            return hasAssistantPermission(resource, action);
        }
        return false;
    } catch (error) {
        console.error('Error in hasPermission:', error);
        return false;
    }
}
/**
 * Check secretary permissions based on user associations
 */ async function hasSecretaryPermission(userId, resource, action) {
    try {
        // Secretaries can create, read, and update appointments and patients for doctors they're associated with
        if (resource === 'appointments' && [
            'create',
            'read',
            'update'
        ].includes(action)) {
            return true;
        }
        if (resource === 'patients' && [
            'create',
            'read',
            'update'
        ].includes(action)) {
            return true;
        }
        return false;
    } catch (error) {
        console.error('Error checking secretary permissions:', error);
        return false;
    }
}
/**
 * Healthcare professional permissions
 */ function hasHealthcareProfessionalPermission(resource, action) {
    // Healthcare professionals have full access to their own data
    const allowedResources = [
        'appointments',
        'patients',
        'medical_records'
    ];
    return allowedResources.includes(resource);
}
/**
 * Assistant permissions (read-only)
 */ function hasAssistantPermission(resource, action) {
    // Assistants have read-only access
    return action === 'read' && [
        'appointments',
        'patients'
    ].includes(resource);
}
async function hasRole(userId, roles) {
    try {
        const { data: user, error } = await supabaseAdmin.from('users').select('role').eq('id', userId).single();
        if (error || !user) {
            console.error('Error checking roles:', error);
            return false;
        }
        return roles.includes(user.role);
    } catch (error) {
        console.error('Error in hasRole:', error);
        return false;
    }
}
async function getUserPermissions(userId) {
    try {
        // Get user role from simplified system
        const { data: user, error: userError } = await supabaseAdmin.from('users').select('role').eq('id', userId).single();
        if (userError || !user) {
            return [];
        }
        // Return permissions based on role
        return DEFAULT_PERMISSIONS[user.role] || [];
    } catch (error) {
        console.error('Error in getUserPermissions:', error);
        return [];
    }
}
async function canAccessHealthcareProfessional(userId, healthcareProfessionalId) {
    try {
        // Get user role
        const { data: user, error: userError } = await supabaseAdmin.from('users').select('role').eq('id', userId).single();
        if (userError || !user) {
            return false;
        }
        // Admin can access all
        if (user.role === 'admin') {
            return true;
        }
        // Healthcare professionals can access their own data
        if (user.role === 'healthcare_professional') {
            const { data: professional, error: profError } = await supabaseAdmin.from('healthcare_professionals').select('id').eq('user_id', userId).eq('id', healthcareProfessionalId).single();
            return !profError && !!professional;
        }
        // Secretaries can access healthcare professionals they're associated with
        if (user.role === 'secretary') {
            const { data: professional, error: profError } = await supabaseAdmin.from('healthcare_professionals').select('user_id').eq('id', healthcareProfessionalId).single();
            if (profError || !professional) {
                return false;
            }
            const { data: association, error: assocError } = await supabaseAdmin.from('user_associations').select('id').eq('accessor_user_id', userId).eq('target_user_id', professional.user_id).eq('association_type', 'secretary_doctor').eq('is_active', true).single();
            return !assocError && !!association;
        }
        return false;
    } catch (error) {
        console.error('Error checking healthcare professional access:', error);
        return false;
    }
}
async function isAdmin(userId) {
    return hasRole(userId, [
        'admin'
    ]);
}
function requirePermission(resource, action) {
    return async (userId)=>{
        return hasPermission(userId, resource, action);
    };
}
function requireRole(roles) {
    return async (userId)=>{
        return hasRole(userId, roles);
    };
}
const DEFAULT_PERMISSIONS = {
    admin: [
        // Full access to everything
        {
            resource: 'appointments',
            action: 'create'
        },
        {
            resource: 'appointments',
            action: 'read'
        },
        {
            resource: 'appointments',
            action: 'update'
        },
        {
            resource: 'appointments',
            action: 'delete'
        },
        {
            resource: 'patients',
            action: 'create'
        },
        {
            resource: 'patients',
            action: 'read'
        },
        {
            resource: 'patients',
            action: 'update'
        },
        {
            resource: 'patients',
            action: 'delete'
        },
        {
            resource: 'healthcare_professionals',
            action: 'create'
        },
        {
            resource: 'healthcare_professionals',
            action: 'read'
        },
        {
            resource: 'healthcare_professionals',
            action: 'update'
        },
        {
            resource: 'healthcare_professionals',
            action: 'delete'
        },
        {
            resource: 'users',
            action: 'create'
        },
        {
            resource: 'users',
            action: 'read'
        },
        {
            resource: 'users',
            action: 'update'
        },
        {
            resource: 'users',
            action: 'delete'
        },
        {
            resource: 'user_associations',
            action: 'create'
        },
        {
            resource: 'user_associations',
            action: 'read'
        },
        {
            resource: 'user_associations',
            action: 'update'
        },
        {
            resource: 'user_associations',
            action: 'delete'
        },
        {
            resource: 'medical_records',
            action: 'create'
        },
        {
            resource: 'medical_records',
            action: 'read'
        },
        {
            resource: 'medical_records',
            action: 'update'
        },
        {
            resource: 'medical_records',
            action: 'delete'
        },
        {
            resource: 'settings',
            action: 'read'
        },
        {
            resource: 'settings',
            action: 'update'
        }
    ],
    healthcare_professional: [
        // Can manage their own appointments, patients and medical records
        {
            resource: 'appointments',
            action: 'create'
        },
        {
            resource: 'appointments',
            action: 'read'
        },
        {
            resource: 'appointments',
            action: 'update'
        },
        {
            resource: 'appointments',
            action: 'delete'
        },
        {
            resource: 'patients',
            action: 'create'
        },
        {
            resource: 'patients',
            action: 'read'
        },
        {
            resource: 'patients',
            action: 'update'
        },
        {
            resource: 'patients',
            action: 'delete'
        },
        {
            resource: 'medical_records',
            action: 'create'
        },
        {
            resource: 'medical_records',
            action: 'read'
        },
        {
            resource: 'medical_records',
            action: 'update'
        },
        {
            resource: 'medical_records',
            action: 'delete'
        }
    ],
    secretary: [
        // Can manage appointments and patients for associated doctors
        // Note: actual access is controlled by user_associations table
        {
            resource: 'appointments',
            action: 'create'
        },
        {
            resource: 'appointments',
            action: 'read'
        },
        {
            resource: 'appointments',
            action: 'update'
        },
        {
            resource: 'patients',
            action: 'create'
        },
        {
            resource: 'patients',
            action: 'read'
        },
        {
            resource: 'patients',
            action: 'update'
        },
        {
            resource: 'healthcare_professionals',
            action: 'read'
        }
    ],
    assistant: [
        // Read-only access to appointments and patients
        {
            resource: 'appointments',
            action: 'read'
        },
        {
            resource: 'patients',
            action: 'read'
        },
        {
            resource: 'healthcare_professionals',
            action: 'read'
        }
    ]
};
}}),
"[project]/src/lib/api-utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createApiResponse": (()=>createApiResponse),
    "handleApiError": (()=>handleApiError),
    "withAuth": (()=>withAuth),
    "withAuthAndPermission": (()=>withAuthAndPermission)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/server.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$permissions$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/permissions.ts [app-route] (ecmascript)");
;
;
;
function createApiResponse(data, message, status = 200) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        data,
        message
    }, {
        status
    });
}
async function withAuth(request, handler) {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        console.log('🔐 Auth check:', {
            hasUser: !!user,
            userId: user?.id,
            email: user?.email,
            error: authError?.message
        });
        if (authError || !user) {
            console.error('❌ Auth failed:', authError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        return await handler(user.id, supabase);
    } catch (error) {
        console.error('API Error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: error instanceof Error ? error.message : 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function withAuthAndPermission(request, resource, action, handler) {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
            return createApiResponse(undefined, 'Unauthorized', 401);
        }
        // Check permissions
        const hasAccess = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$permissions$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["hasPermission"])(user.id, resource, action);
        if (!hasAccess) {
            return createApiResponse(undefined, 'Forbidden: Insufficient permissions', 403);
        }
        return await handler(user.id, supabase);
    } catch (error) {
        console.error('API Error:', error);
        return createApiResponse(undefined, error instanceof Error ? error.message : 'Internal server error', 500);
    }
}
function handleApiError(error) {
    console.error('API Error:', error);
    if (error?.code === 'PGRST116') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Resource not found'
        }, {
            status: 404
        });
    }
    if (error?.code === '23505') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Resource already exists'
        }, {
            status: 409
        });
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        error: error?.message || 'Internal server error'
    }, {
        status: 500
    });
}
}}),
"[project]/src/lib/debug-utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Debug utility for conditional logging
__turbopack_context__.s({
    "debugLog": (()=>debugLog),
    "perfMon": (()=>perfMon)
});
const DEBUG_MODE = ("TURBOPACK compile-time value", "development") === 'development' && process.env.NEXT_PUBLIC_DEBUG === 'true';
const debugLog = {
    info: (...args)=>{
        if (DEBUG_MODE) {
            console.log(...args);
        }
    },
    error: (...args)=>{
        if (DEBUG_MODE) {
            console.error(...args);
        }
    },
    warn: (...args)=>{
        if (DEBUG_MODE) {
            console.warn(...args);
        }
    },
    // Always log critical errors
    critical: (...args)=>{
        console.error(...args);
    }
};
const perfMon = {
    mark: (name)=>{
        if (DEBUG_MODE && performance.mark) {
            performance.mark(name);
        }
    },
    measure: (name, startMark, endMark)=>{
        if (DEBUG_MODE && performance.measure) {
            try {
                performance.measure(name, startMark, endMark);
                const measures = performance.getEntriesByName(name);
                const lastMeasure = measures[measures.length - 1];
                console.log(`⏱️ ${name}: ${lastMeasure.duration.toFixed(2)}ms`);
            } catch (e) {
            // Ignore performance measurement errors
            }
        }
    }
};
}}),
"[project]/src/app/api/settings/profile/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PATCH": (()=>PATCH)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-utils.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/debug-utils.ts [app-route] (ecmascript)");
;
;
async function PATCH(request) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withAuth"])(request, async (userId, supabase)=>{
        try {
            const body = await request.json();
            // Check if a specific user_id or healthcare_professional_id is provided (for admin/secretary access)
            const url = new URL(request.url);
            const targetUserId = url.searchParams.get('user_id');
            const healthcareProfessionalId = url.searchParams.get('healthcare_professional_id');
            let finalUserId = userId;
            // Get current user's role for permission checking
            const { data: currentUser, error: currentUserError } = await supabase.from('users').select('role').eq('id', userId).single();
            if (currentUserError) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["debugLog"].error('❌ Error fetching current user role:', currentUserError);
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleApiError"])(new Error('Failed to verify user permissions'));
            }
            // If healthcare_professional_id is provided, get the corresponding user_id
            if (healthcareProfessionalId) {
                const { data: hpData, error: hpError } = await supabase.from('healthcare_professionals').select('user_id').eq('id', healthcareProfessionalId).single();
                if (hpError) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["debugLog"].error('❌ Error finding healthcare professional by ID:', hpError);
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleApiError"])(new Error('Healthcare professional not found'));
                }
                finalUserId = hpData.user_id;
                // If user is a secretary, check if they have access to this healthcare professional
                if (currentUser.role === 'secretary' && finalUserId !== userId) {
                    const { data: association, error: associationError } = await supabase.from('user_associations').select('id').eq('accessor_user_id', userId).eq('target_user_id', finalUserId).eq('association_type', 'secretary_to_doctor').eq('is_active', true).single();
                    if (associationError || !association) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["debugLog"].error('❌ Secretary access denied for profile update');
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createApiResponse"])(undefined, 'Access denied: You do not have permission to update this healthcare professional profile', 403);
                    }
                }
            } else if (targetUserId) {
                finalUserId = targetUserId;
                // If user is a secretary trying to update another user's profile, validate access
                if (currentUser.role === 'secretary' && finalUserId !== userId) {
                    const { data: association, error: associationError } = await supabase.from('user_associations').select('id').eq('accessor_user_id', userId).eq('target_user_id', finalUserId).eq('association_type', 'secretary_to_doctor').eq('is_active', true).single();
                    if (associationError || !association) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["debugLog"].error('❌ Secretary access denied for profile update');
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createApiResponse"])(undefined, 'Access denied: You do not have permission to update this user profile', 403);
                    }
                }
            }
            // Get user role to determine where to save data
            const { data: userData, error: userError } = await supabase.from('users').select('role').eq('id', finalUserId).single();
            if (userError) {
                throw new Error('Failed to fetch user data');
            }
            // Update users table
            const { error: userUpdateError } = await supabase.from('users').update({
                name: body.name,
                email: body.email,
                phone: body.phone,
                updated_at: new Date().toISOString()
            }).eq('id', finalUserId);
            if (userUpdateError) {
                throw new Error('Failed to update user profile');
            }
            // If user is a healthcare professional, update healthcare_professionals table
            if (userData.role === 'healthcare_professional') {
                // Check if healthcare professional record exists
                const { data: hpData, error: hpCheckError } = await supabase.from('healthcare_professionals').select('id').eq('user_id', finalUserId).single();
                if (hpCheckError && hpCheckError.code !== 'PGRST116') {
                    throw new Error('Failed to check healthcare professional record');
                }
                const hpUpdateData = {
                    name: body.name,
                    email: body.email,
                    phone: body.phone,
                    specialty: body.specialty || null,
                    crm: body.crm || null,
                    address: body.address || null,
                    cpf: body.cpf || null,
                    updated_at: new Date().toISOString()
                };
                if (hpData) {
                    // Update existing record
                    const { error: hpUpdateError } = await supabase.from('healthcare_professionals').update(hpUpdateData).eq('user_id', finalUserId);
                    if (hpUpdateError) {
                        throw new Error('Failed to update healthcare professional profile');
                    }
                } else {
                    // Create new record
                    const { error: hpCreateError } = await supabase.from('healthcare_professionals').insert({
                        user_id: finalUserId,
                        ...hpUpdateData
                    });
                    if (hpCreateError) {
                        throw new Error('Failed to create healthcare professional profile');
                    }
                }
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createApiResponse"])({
                message: 'Profile updated successfully',
                profile: body
            });
        } catch (error) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleApiError"])(error);
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__023a19f2._.js.map