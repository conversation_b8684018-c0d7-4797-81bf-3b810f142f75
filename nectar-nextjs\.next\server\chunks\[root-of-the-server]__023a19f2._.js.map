{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\nimport type { Database } from '@/types/supabase'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient<Database>(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAGO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/permissions.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport type { Database } from '@/types/supabase'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\n// Create a Supabase client with service role key for server-side operations\nconst supabaseAdmin = createClient<Database>(supabaseUrl, supabaseServiceKey)\n\nexport type Permission = {\n  resource: string;\n  action: 'create' | 'read' | 'update' | 'delete';\n};\n\nexport type Role = 'admin' | 'healthcare_professional' | 'secretary' | 'assistant';\n\n/**\n * Check if a user has permission to access a resource based on user associations\n */\nexport async function hasPermission(\n  userId: string, \n  resource: string, \n  action: 'create' | 'read' | 'update' | 'delete'\n): Promise<boolean> {\n  try {\n    // Get user role from simplified system\n    const { data: user, error: userError } = await supabaseAdmin\n      .from('users')\n      .select('role')\n      .eq('id', userId)\n      .single()\n\n    if (userError || !user) {\n      return false\n    }\n\n    // Admin has all permissions\n    if (user.role === 'admin') {\n      return true\n    }\n\n    // Healthcare professionals have full permissions for their own resources\n    if (user.role === 'healthcare_professional') {\n      return hasHealthcareProfessionalPermission(resource, action)\n    }\n\n    // For secretaries, check user associations to see what they can access\n    if (user.role === 'secretary') {\n      return await hasSecretaryPermission(userId, resource, action)\n    }\n\n    // Assistants have read-only access to basic resources\n    if (user.role === 'assistant') {\n      return hasAssistantPermission(resource, action)\n    }\n\n    return false\n  } catch (error) {\n    console.error('Error in hasPermission:', error)\n    return false\n  }\n}\n\n/**\n * Check secretary permissions based on user associations\n */\nasync function hasSecretaryPermission(\n  userId: string,\n  resource: string,\n  action: 'create' | 'read' | 'update' | 'delete'\n): Promise<boolean> {\n  try {\n    // Secretaries can create, read, and update appointments and patients for doctors they're associated with\n    if (resource === 'appointments' && ['create', 'read', 'update'].includes(action)) {\n      return true\n    }\n    \n    if (resource === 'patients' && ['create', 'read', 'update'].includes(action)) {\n      return true\n    }\n\n    return false\n  } catch (error) {\n    console.error('Error checking secretary permissions:', error)\n    return false\n  }\n}\n\n/**\n * Healthcare professional permissions\n */\nfunction hasHealthcareProfessionalPermission(\n  resource: string,\n  action: 'create' | 'read' | 'update' | 'delete'\n): boolean {\n  // Healthcare professionals have full access to their own data\n  const allowedResources = ['appointments', 'patients', 'medical_records']\n  return allowedResources.includes(resource)\n}\n\n/**\n * Assistant permissions (read-only)\n */\nfunction hasAssistantPermission(\n  resource: string,\n  action: 'create' | 'read' | 'update' | 'delete'\n): boolean {\n  // Assistants have read-only access\n  return action === 'read' && ['appointments', 'patients'].includes(resource)\n}\n\n/**\n * Check if a user has any of the specified roles\n */\nexport async function hasRole(userId: string, roles: Role[]): Promise<boolean> {\n  try {\n    const { data: user, error } = await supabaseAdmin\n      .from('users')\n      .select('role')\n      .eq('id', userId)\n      .single()\n\n    if (error || !user) {\n      console.error('Error checking roles:', error)\n      return false\n    }\n\n    return roles.includes(user.role as Role)\n  } catch (error) {\n    console.error('Error in hasRole:', error)\n    return false\n  }\n}\n\n/**\n * Get all permissions for a user based on their role and associations\n */\nexport async function getUserPermissions(userId: string): Promise<Permission[]> {\n  try {\n    // Get user role from simplified system\n    const { data: user, error: userError } = await supabaseAdmin\n      .from('users')\n      .select('role')\n      .eq('id', userId)\n      .single()\n\n    if (userError || !user) {\n      return []\n    }\n\n    // Return permissions based on role\n    return DEFAULT_PERMISSIONS[user.role as Role] || []\n  } catch (error) {\n    console.error('Error in getUserPermissions:', error)\n    return []\n  }\n}\n\n/**\n * Check if a user can access a specific healthcare professional's data\n */\nexport async function canAccessHealthcareProfessional(\n  userId: string,\n  healthcareProfessionalId: string\n): Promise<boolean> {\n  try {\n    // Get user role\n    const { data: user, error: userError } = await supabaseAdmin\n      .from('users')\n      .select('role')\n      .eq('id', userId)\n      .single()\n\n    if (userError || !user) {\n      return false\n    }\n\n    // Admin can access all\n    if (user.role === 'admin') {\n      return true\n    }\n\n    // Healthcare professionals can access their own data\n    if (user.role === 'healthcare_professional') {\n      const { data: professional, error: profError } = await supabaseAdmin\n        .from('healthcare_professionals')\n        .select('id')\n        .eq('user_id', userId)\n        .eq('id', healthcareProfessionalId)\n        .single()\n\n      return !profError && !!professional\n    }\n\n    // Secretaries can access healthcare professionals they're associated with\n    if (user.role === 'secretary') {\n      const { data: professional, error: profError } = await supabaseAdmin\n        .from('healthcare_professionals')\n        .select('user_id')\n        .eq('id', healthcareProfessionalId)\n        .single()\n\n      if (profError || !professional) {\n        return false\n      }\n\n      const { data: association, error: assocError } = await supabaseAdmin\n        .from('user_associations')\n        .select('id')\n        .eq('accessor_user_id', userId)\n        .eq('target_user_id', professional.user_id)\n        .eq('association_type', 'secretary_doctor')\n        .eq('is_active', true)\n        .single()\n\n      return !assocError && !!association\n    }\n\n    return false\n  } catch (error) {\n    console.error('Error checking healthcare professional access:', error)\n    return false\n  }\n}\n\n/**\n * Check if a user is an admin\n */\nexport async function isAdmin(userId: string): Promise<boolean> {\n  return hasRole(userId, ['admin'])\n}\n\n/**\n * Middleware function to check permissions for API routes\n */\nexport function requirePermission(resource: string, action: 'create' | 'read' | 'update' | 'delete') {\n  return async (userId: string): Promise<boolean> => {\n    return hasPermission(userId, resource, action)\n  }\n}\n\n/**\n * Middleware function to check roles for API routes\n */\nexport function requireRole(roles: Role[]) {\n  return async (userId: string): Promise<boolean> => {\n    return hasRole(userId, roles)\n  }\n}\n\n/**\n * Updated default permissions for each role based on association model\n */\nexport const DEFAULT_PERMISSIONS: Record<Role, Permission[]> = {\n  admin: [\n    // Full access to everything\n    { resource: 'appointments', action: 'create' },\n    { resource: 'appointments', action: 'read' },\n    { resource: 'appointments', action: 'update' },\n    { resource: 'appointments', action: 'delete' },\n    { resource: 'patients', action: 'create' },\n    { resource: 'patients', action: 'read' },\n    { resource: 'patients', action: 'update' },\n    { resource: 'patients', action: 'delete' },\n    { resource: 'healthcare_professionals', action: 'create' },\n    { resource: 'healthcare_professionals', action: 'read' },\n    { resource: 'healthcare_professionals', action: 'update' },\n    { resource: 'healthcare_professionals', action: 'delete' },\n    { resource: 'users', action: 'create' },\n    { resource: 'users', action: 'read' },\n    { resource: 'users', action: 'update' },\n    { resource: 'users', action: 'delete' },\n    { resource: 'user_associations', action: 'create' },\n    { resource: 'user_associations', action: 'read' },\n    { resource: 'user_associations', action: 'update' },\n    { resource: 'user_associations', action: 'delete' },\n    { resource: 'medical_records', action: 'create' },\n    { resource: 'medical_records', action: 'read' },\n    { resource: 'medical_records', action: 'update' },\n    { resource: 'medical_records', action: 'delete' },\n    { resource: 'settings', action: 'read' },\n    { resource: 'settings', action: 'update' },\n  ],\n  healthcare_professional: [\n    // Can manage their own appointments, patients and medical records\n    { resource: 'appointments', action: 'create' },\n    { resource: 'appointments', action: 'read' },\n    { resource: 'appointments', action: 'update' },\n    { resource: 'appointments', action: 'delete' },\n    { resource: 'patients', action: 'create' },\n    { resource: 'patients', action: 'read' },\n    { resource: 'patients', action: 'update' },\n    { resource: 'patients', action: 'delete' },\n    { resource: 'medical_records', action: 'create' },\n    { resource: 'medical_records', action: 'read' },\n    { resource: 'medical_records', action: 'update' },\n    { resource: 'medical_records', action: 'delete' },\n  ],\n  secretary: [\n    // Can manage appointments and patients for associated doctors\n    // Note: actual access is controlled by user_associations table\n    { resource: 'appointments', action: 'create' },\n    { resource: 'appointments', action: 'read' },\n    { resource: 'appointments', action: 'update' },\n    { resource: 'patients', action: 'create' },\n    { resource: 'patients', action: 'read' },\n    { resource: 'patients', action: 'update' },\n    { resource: 'healthcare_professionals', action: 'read' }, // Can see associated doctors\n  ],\n  assistant: [\n    // Read-only access to appointments and patients\n    { resource: 'appointments', action: 'read' },\n    { resource: 'patients', action: 'read' },\n    { resource: 'healthcare_professionals', action: 'read' },\n  ],\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGA,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAEhE,4EAA4E;AAC5E,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAY,aAAa;AAYnD,eAAe,cACpB,MAAc,EACd,QAAgB,EAChB,MAA+C;IAE/C,IAAI;QACF,uCAAuC;QACvC,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAC5C,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;QACT;QAEA,4BAA4B;QAC5B,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,OAAO;QACT;QAEA,yEAAyE;QACzE,IAAI,KAAK,IAAI,KAAK,2BAA2B;YAC3C,OAAO,oCAAoC,UAAU;QACvD;QAEA,uEAAuE;QACvE,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,OAAO,MAAM,uBAAuB,QAAQ,UAAU;QACxD;QAEA,sDAAsD;QACtD,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,OAAO,uBAAuB,UAAU;QAC1C;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAEA;;CAEC,GACD,eAAe,uBACb,MAAc,EACd,QAAgB,EAChB,MAA+C;IAE/C,IAAI;QACF,yGAAyG;QACzG,IAAI,aAAa,kBAAkB;YAAC;YAAU;YAAQ;SAAS,CAAC,QAAQ,CAAC,SAAS;YAChF,OAAO;QACT;QAEA,IAAI,aAAa,cAAc;YAAC;YAAU;YAAQ;SAAS,CAAC,QAAQ,CAAC,SAAS;YAC5E,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO;IACT;AACF;AAEA;;CAEC,GACD,SAAS,oCACP,QAAgB,EAChB,MAA+C;IAE/C,8DAA8D;IAC9D,MAAM,mBAAmB;QAAC;QAAgB;QAAY;KAAkB;IACxE,OAAO,iBAAiB,QAAQ,CAAC;AACnC;AAEA;;CAEC,GACD,SAAS,uBACP,QAAgB,EAChB,MAA+C;IAE/C,mCAAmC;IACnC,OAAO,WAAW,UAAU;QAAC;QAAgB;KAAW,CAAC,QAAQ,CAAC;AACpE;AAKO,eAAe,QAAQ,MAAc,EAAE,KAAa;IACzD,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cACjC,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,SAAS,CAAC,MAAM;YAClB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;QAEA,OAAO,MAAM,QAAQ,CAAC,KAAK,IAAI;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO;IACT;AACF;AAKO,eAAe,mBAAmB,MAAc;IACrD,IAAI;QACF,uCAAuC;QACvC,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAC5C,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,EAAE;QACX;QAEA,mCAAmC;QACnC,OAAO,mBAAmB,CAAC,KAAK,IAAI,CAAS,IAAI,EAAE;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,EAAE;IACX;AACF;AAKO,eAAe,gCACpB,MAAc,EACd,wBAAgC;IAEhC,IAAI;QACF,gBAAgB;QAChB,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAC5C,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;QACT;QAEA,uBAAuB;QACvB,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,OAAO;QACT;QAEA,qDAAqD;QACrD,IAAI,KAAK,IAAI,KAAK,2BAA2B;YAC3C,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cACpD,IAAI,CAAC,4BACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,MAAM,0BACT,MAAM;YAET,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB;QAEA,0EAA0E;QAC1E,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cACpD,IAAI,CAAC,4BACL,MAAM,CAAC,WACP,EAAE,CAAC,MAAM,0BACT,MAAM;YAET,IAAI,aAAa,CAAC,cAAc;gBAC9B,OAAO;YACT;YAEA,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,cACpD,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,oBAAoB,QACvB,EAAE,CAAC,kBAAkB,aAAa,OAAO,EACzC,EAAE,CAAC,oBAAoB,oBACvB,EAAE,CAAC,aAAa,MAChB,MAAM;YAET,OAAO,CAAC,cAAc,CAAC,CAAC;QAC1B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,OAAO;IACT;AACF;AAKO,eAAe,QAAQ,MAAc;IAC1C,OAAO,QAAQ,QAAQ;QAAC;KAAQ;AAClC;AAKO,SAAS,kBAAkB,QAAgB,EAAE,MAA+C;IACjG,OAAO,OAAO;QACZ,OAAO,cAAc,QAAQ,UAAU;IACzC;AACF;AAKO,SAAS,YAAY,KAAa;IACvC,OAAO,OAAO;QACZ,OAAO,QAAQ,QAAQ;IACzB;AACF;AAKO,MAAM,sBAAkD;IAC7D,OAAO;QACL,4BAA4B;QAC5B;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAgB,QAAQ;QAAO;QAC3C;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAY,QAAQ;QAAO;QACvC;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAA4B,QAAQ;QAAS;QACzD;YAAE,UAAU;YAA4B,QAAQ;QAAO;QACvD;YAAE,UAAU;YAA4B,QAAQ;QAAS;QACzD;YAAE,UAAU;YAA4B,QAAQ;QAAS;QACzD;YAAE,UAAU;YAAS,QAAQ;QAAS;QACtC;YAAE,UAAU;YAAS,QAAQ;QAAO;QACpC;YAAE,UAAU;YAAS,QAAQ;QAAS;QACtC;YAAE,UAAU;YAAS,QAAQ;QAAS;QACtC;YAAE,UAAU;YAAqB,QAAQ;QAAS;QAClD;YAAE,UAAU;YAAqB,QAAQ;QAAO;QAChD;YAAE,UAAU;YAAqB,QAAQ;QAAS;QAClD;YAAE,UAAU;YAAqB,QAAQ;QAAS;QAClD;YAAE,UAAU;YAAmB,QAAQ;QAAS;QAChD;YAAE,UAAU;YAAmB,QAAQ;QAAO;QAC9C;YAAE,UAAU;YAAmB,QAAQ;QAAS;QAChD;YAAE,UAAU;YAAmB,QAAQ;QAAS;QAChD;YAAE,UAAU;YAAY,QAAQ;QAAO;QACvC;YAAE,UAAU;YAAY,QAAQ;QAAS;KAC1C;IACD,yBAAyB;QACvB,kEAAkE;QAClE;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAgB,QAAQ;QAAO;QAC3C;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAY,QAAQ;QAAO;QACvC;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAmB,QAAQ;QAAS;QAChD;YAAE,UAAU;YAAmB,QAAQ;QAAO;QAC9C;YAAE,UAAU;YAAmB,QAAQ;QAAS;QAChD;YAAE,UAAU;YAAmB,QAAQ;QAAS;KACjD;IACD,WAAW;QACT,8DAA8D;QAC9D,+DAA+D;QAC/D;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAgB,QAAQ;QAAO;QAC3C;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAY,QAAQ;QAAO;QACvC;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAA4B,QAAQ;QAAO;KACxD;IACD,WAAW;QACT,gDAAgD;QAChD;YAAE,UAAU;YAAgB,QAAQ;QAAO;QAC3C;YAAE,UAAU;YAAY,QAAQ;QAAO;QACvC;YAAE,UAAU;YAA4B,QAAQ;QAAO;KACxD;AACH", "debugId": null}}, {"offset": {"line": 557, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/api-utils.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@/lib/supabase/server'\nimport { hasPermission, type Role } from '@/lib/permissions'\n\nexport interface ApiResponse<T = any> {\n  data?: T\n  error?: string\n  message?: string\n}\n\nexport function createApiResponse<T>(\n  data?: T,\n  message?: string,\n  status: number = 200\n): NextResponse<ApiResponse<T>> {\n  return NextResponse.json(\n    {\n      data,\n      message,\n    },\n    { status }\n  )\n}\n\nexport async function withAuth<T>(\n  request: NextRequest,\n  handler: (userId: string, supabase: any) => Promise<NextResponse<ApiResponse<T>>>\n): Promise<NextResponse<ApiResponse<T>>> {\n  try {\n    const supabase = await createClient()\n    \n    const {\n      data: { user },\n      error: authError,\n    } = await supabase.auth.getUser()\n\n    console.log('🔐 Auth check:', {\n      hasUser: !!user,\n      userId: user?.id,\n      email: user?.email,\n      error: authError?.message\n    })\n\n    if (authError || !user) {\n      console.error('❌ Auth failed:', authError)\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    return await handler(user.id, supabase)\n  } catch (error) {\n    console.error('API Error:', error)\n    return NextResponse.json(\n      { error: error instanceof Error ? error.message : 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function withAuthAndPermission<T>(\n  request: NextRequest,\n  resource: string,\n  action: 'create' | 'read' | 'update' | 'delete',\n  handler: (userId: string, supabase: any) => Promise<NextResponse<ApiResponse<T>>>\n): Promise<NextResponse<ApiResponse<T>>> {\n  try {\n    const supabase = await createClient()\n\n    const {\n      data: { user },\n      error: authError,\n    } = await supabase.auth.getUser()\n\n    if (authError || !user) {\n      return createApiResponse(undefined, 'Unauthorized', 401)\n    }\n\n    // Check permissions\n    const hasAccess = await hasPermission(user.id, resource, action)\n    if (!hasAccess) {\n      return createApiResponse(undefined, 'Forbidden: Insufficient permissions', 403)\n    }\n\n    return await handler(user.id, supabase)\n  } catch (error) {\n    console.error('API Error:', error)\n    return createApiResponse(\n      undefined,\n      error instanceof Error ? error.message : 'Internal server error',\n      500\n    )\n  }\n}\n\nexport function handleApiError(error: any): NextResponse<ApiResponse> {\n  console.error('API Error:', error)\n\n  if (error?.code === 'PGRST116') {\n    return NextResponse.json({ error: 'Resource not found' }, { status: 404 })\n  }\n\n  if (error?.code === '23505') {\n    return NextResponse.json({ error: 'Resource already exists' }, { status: 409 })\n  }\n\n  return NextResponse.json(\n    { error: error?.message || 'Internal server error' },\n    { status: 500 }\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAQO,SAAS,kBACd,IAAQ,EACR,OAAgB,EAChB,SAAiB,GAAG;IAEpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE;QACA;IACF,GACA;QAAE;IAAO;AAEb;AAEO,eAAe,SACpB,OAAoB,EACpB,OAAiF;IAEjF,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAE/B,QAAQ,GAAG,CAAC,kBAAkB;YAC5B,SAAS,CAAC,CAAC;YACX,QAAQ,MAAM;YACd,OAAO,MAAM;YACb,OAAO,WAAW;QACpB;QAEA,IAAI,aAAa,CAAC,MAAM;YACtB,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,OAAO,MAAM,QAAQ,KAAK,EAAE,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAAwB,GAC1E;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,sBACpB,OAAoB,EACpB,QAAgB,EAChB,MAA+C,EAC/C,OAAiF;IAEjF,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAE/B,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,kBAAkB,WAAW,gBAAgB;QACtD;QAEA,oBAAoB;QACpB,MAAM,YAAY,MAAM,CAAA,GAAA,2HAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,EAAE,EAAE,UAAU;QACzD,IAAI,CAAC,WAAW;YACd,OAAO,kBAAkB,WAAW,uCAAuC;QAC7E;QAEA,OAAO,MAAM,QAAQ,KAAK,EAAE,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,kBACL,WACA,iBAAiB,QAAQ,MAAM,OAAO,GAAG,yBACzC;IAEJ;AACF;AAEO,SAAS,eAAe,KAAU;IACvC,QAAQ,KAAK,CAAC,cAAc;IAE5B,IAAI,OAAO,SAAS,YAAY;QAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAqB,GAAG;YAAE,QAAQ;QAAI;IAC1E;IAEA,IAAI,OAAO,SAAS,SAAS;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA0B,GAAG;YAAE,QAAQ;QAAI;IAC/E;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,OAAO,OAAO,WAAW;IAAwB,GACnD;QAAE,QAAQ;IAAI;AAElB", "debugId": null}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/debug-utils.ts"], "sourcesContent": ["// Debug utility for conditional logging\r\nconst DEBUG_MODE = process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_DEBUG === 'true';\r\n\r\nexport const debugLog = {\r\n  info: (...args: any[]) => {\r\n    if (DEBUG_MODE) {\r\n      console.log(...args);\r\n    }\r\n  },\r\n  error: (...args: any[]) => {\r\n    if (DEBUG_MODE) {\r\n      console.error(...args);\r\n    }\r\n  },\r\n  warn: (...args: any[]) => {\r\n    if (DEBUG_MODE) {\r\n      console.warn(...args);\r\n    }\r\n  },\r\n  // Always log critical errors\r\n  critical: (...args: any[]) => {\r\n    console.error(...args);\r\n  }\r\n};\r\n\r\n// Performance monitoring utility\r\nexport const perfMon = {\r\n  mark: (name: string) => {\r\n    if (DEBUG_MODE && performance.mark) {\r\n      performance.mark(name);\r\n    }\r\n  },\r\n  measure: (name: string, startMark: string, endMark?: string) => {\r\n    if (DEBUG_MODE && performance.measure) {\r\n      try {\r\n        performance.measure(name, startMark, endMark);\r\n        const measures = performance.getEntriesByName(name);\r\n        const lastMeasure = measures[measures.length - 1];\r\n        console.log(`⏱️ ${name}: ${lastMeasure.duration.toFixed(2)}ms`);\r\n      } catch (e) {\r\n        // Ignore performance measurement errors\r\n      }\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;AACxC,MAAM,aAAa,oDAAyB,iBAAiB,QAAQ,GAAG,CAAC,iBAAiB,KAAK;AAExF,MAAM,WAAW;IACtB,MAAM,CAAC,GAAG;QACR,IAAI,YAAY;YACd,QAAQ,GAAG,IAAI;QACjB;IACF;IACA,OAAO,CAAC,GAAG;QACT,IAAI,YAAY;YACd,QAAQ,KAAK,IAAI;QACnB;IACF;IACA,MAAM,CAAC,GAAG;QACR,IAAI,YAAY;YACd,QAAQ,IAAI,IAAI;QAClB;IACF;IACA,6BAA6B;IAC7B,UAAU,CAAC,GAAG;QACZ,QAAQ,KAAK,IAAI;IACnB;AACF;AAGO,MAAM,UAAU;IACrB,MAAM,CAAC;QACL,IAAI,cAAc,YAAY,IAAI,EAAE;YAClC,YAAY,IAAI,CAAC;QACnB;IACF;IACA,SAAS,CAAC,MAAc,WAAmB;QACzC,IAAI,cAAc,YAAY,OAAO,EAAE;YACrC,IAAI;gBACF,YAAY,OAAO,CAAC,MAAM,WAAW;gBACrC,MAAM,WAAW,YAAY,gBAAgB,CAAC;gBAC9C,MAAM,cAAc,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;gBACjD,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,YAAY,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YAChE,EAAE,OAAO,GAAG;YACV,wCAAwC;YAC1C;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/app/api/settings/profile/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'\nimport { debugLog } from '@/lib/debug-utils'\n\nexport async function PATCH(request: NextRequest) {\n  return withAuth(request, async (userId, supabase) => {\n    try {\n      const body = await request.json()\n      \n      // Check if a specific user_id or healthcare_professional_id is provided (for admin/secretary access)\n      const url = new URL(request.url)\n      const targetUserId = url.searchParams.get('user_id')\n      const healthcareProfessionalId = url.searchParams.get('healthcare_professional_id')\n\n      let finalUserId = userId;\n\n      // Get current user's role for permission checking\n      const { data: currentUser, error: currentUserError } = await supabase\n        .from('users')\n        .select('role')\n        .eq('id', userId)\n        .single()\n\n      if (currentUserError) {\n        debugLog.error('❌ Error fetching current user role:', currentUserError);\n        return handleApiError(new Error('Failed to verify user permissions'))\n      }\n\n      // If healthcare_professional_id is provided, get the corresponding user_id\n      if (healthcareProfessionalId) {\n        const { data: hpData, error: hpError } = await supabase\n          .from('healthcare_professionals')\n          .select('user_id')\n          .eq('id', healthcareProfessionalId)\n          .single()\n\n        if (hpError) {\n          debugLog.error('❌ Error finding healthcare professional by ID:', hpError);\n          return handleApiError(new Error('Healthcare professional not found'))\n        }\n\n        finalUserId = hpData.user_id;\n\n        // If user is a secretary, check if they have access to this healthcare professional\n        if (currentUser.role === 'secretary' && finalUserId !== userId) {\n          const { data: association, error: associationError } = await supabase\n            .from('user_associations')\n            .select('id')\n            .eq('accessor_user_id', userId)\n            .eq('target_user_id', finalUserId)\n            .eq('association_type', 'secretary_to_doctor')\n            .eq('is_active', true)\n            .single()\n\n          if (associationError || !association) {\n            debugLog.error('❌ Secretary access denied for profile update');\n            return createApiResponse(undefined, 'Access denied: You do not have permission to update this healthcare professional profile', 403)\n          }\n        }\n      } else if (targetUserId) {\n        finalUserId = targetUserId;\n        \n        // If user is a secretary trying to update another user's profile, validate access\n        if (currentUser.role === 'secretary' && finalUserId !== userId) {\n          const { data: association, error: associationError } = await supabase\n            .from('user_associations')\n            .select('id')\n            .eq('accessor_user_id', userId)\n            .eq('target_user_id', finalUserId)\n            .eq('association_type', 'secretary_to_doctor')\n            .eq('is_active', true)\n            .single()\n\n          if (associationError || !association) {\n            debugLog.error('❌ Secretary access denied for profile update');\n            return createApiResponse(undefined, 'Access denied: You do not have permission to update this user profile', 403)\n          }\n        }\n      }\n      \n      // Get user role to determine where to save data\n      const { data: userData, error: userError } = await supabase\n        .from('users')\n        .select('role')\n        .eq('id', finalUserId)\n        .single()\n\n      if (userError) {\n        throw new Error('Failed to fetch user data')\n      }\n\n      // Update users table\n      const { error: userUpdateError } = await supabase\n        .from('users')\n        .update({\n          name: body.name,\n          email: body.email,\n          phone: body.phone,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', finalUserId)\n\n      if (userUpdateError) {\n        throw new Error('Failed to update user profile')\n      }\n\n      // If user is a healthcare professional, update healthcare_professionals table\n      if (userData.role === 'healthcare_professional') {\n        // Check if healthcare professional record exists\n        const { data: hpData, error: hpCheckError } = await supabase\n          .from('healthcare_professionals')\n          .select('id')\n          .eq('user_id', finalUserId)\n          .single()\n\n        if (hpCheckError && hpCheckError.code !== 'PGRST116') {\n          throw new Error('Failed to check healthcare professional record')\n        }\n\n        const hpUpdateData = {\n          name: body.name,\n          email: body.email,\n          phone: body.phone,\n          specialty: body.specialty || null,\n          crm: body.crm || null,\n          address: body.address || null,\n          cpf: body.cpf || null,\n          updated_at: new Date().toISOString()\n        }\n\n        if (hpData) {\n          // Update existing record\n          const { error: hpUpdateError } = await supabase\n            .from('healthcare_professionals')\n            .update(hpUpdateData)\n            .eq('user_id', finalUserId)\n\n          if (hpUpdateError) {\n            throw new Error('Failed to update healthcare professional profile')\n          }\n        } else {\n          // Create new record\n          const { error: hpCreateError } = await supabase\n            .from('healthcare_professionals')\n            .insert({\n              user_id: finalUserId,\n              ...hpUpdateData\n            })\n\n          if (hpCreateError) {\n            throw new Error('Failed to create healthcare professional profile')\n          }\n        }\n      }\n      \n      return createApiResponse({ \n        message: 'Profile updated successfully',\n        profile: body \n      })\n    } catch (error) {\n      return handleApiError(error)\n    }\n  })\n}\n"], "names": [], "mappings": ";;;AACA;AACA;;;AAEO,eAAe,MAAM,OAAoB;IAC9C,OAAO,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO,QAAQ;QACtC,IAAI;YACF,MAAM,OAAO,MAAM,QAAQ,IAAI;YAE/B,qGAAqG;YACrG,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;YAC/B,MAAM,eAAe,IAAI,YAAY,CAAC,GAAG,CAAC;YAC1C,MAAM,2BAA2B,IAAI,YAAY,CAAC,GAAG,CAAC;YAEtD,IAAI,cAAc;YAElB,kDAAkD;YAClD,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,SAC1D,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,kBAAkB;gBACpB,8HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,uCAAuC;gBACtD,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,MAAM;YAClC;YAEA,2EAA2E;YAC3E,IAAI,0BAA0B;gBAC5B,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,OAAO,EAAE,GAAG,MAAM,SAC5C,IAAI,CAAC,4BACL,MAAM,CAAC,WACP,EAAE,CAAC,MAAM,0BACT,MAAM;gBAET,IAAI,SAAS;oBACX,8HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,kDAAkD;oBACjE,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,MAAM;gBAClC;gBAEA,cAAc,OAAO,OAAO;gBAE5B,oFAAoF;gBACpF,IAAI,YAAY,IAAI,KAAK,eAAe,gBAAgB,QAAQ;oBAC9D,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,SAC1D,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,oBAAoB,QACvB,EAAE,CAAC,kBAAkB,aACrB,EAAE,CAAC,oBAAoB,uBACvB,EAAE,CAAC,aAAa,MAChB,MAAM;oBAET,IAAI,oBAAoB,CAAC,aAAa;wBACpC,8HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC;wBACf,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,4FAA4F;oBAClI;gBACF;YACF,OAAO,IAAI,cAAc;gBACvB,cAAc;gBAEd,kFAAkF;gBAClF,IAAI,YAAY,IAAI,KAAK,eAAe,gBAAgB,QAAQ;oBAC9D,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,SAC1D,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,oBAAoB,QACvB,EAAE,CAAC,kBAAkB,aACrB,EAAE,CAAC,oBAAoB,uBACvB,EAAE,CAAC,aAAa,MAChB,MAAM;oBAET,IAAI,oBAAoB,CAAC,aAAa;wBACpC,8HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC;wBACf,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,yEAAyE;oBAC/G;gBACF;YACF;YAEA,gDAAgD;YAChD,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,aACT,MAAM;YAET,IAAI,WAAW;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,qBAAqB;YACrB,MAAM,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SACtC,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,OAAO,KAAK,KAAK;gBACjB,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,iBAAiB;gBACnB,MAAM,IAAI,MAAM;YAClB;YAEA,8EAA8E;YAC9E,IAAI,SAAS,IAAI,KAAK,2BAA2B;gBAC/C,iDAAiD;gBACjD,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACjD,IAAI,CAAC,4BACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,aACd,MAAM;gBAET,IAAI,gBAAgB,aAAa,IAAI,KAAK,YAAY;oBACpD,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,eAAe;oBACnB,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,OAAO,KAAK,KAAK;oBACjB,WAAW,KAAK,SAAS,IAAI;oBAC7B,KAAK,KAAK,GAAG,IAAI;oBACjB,SAAS,KAAK,OAAO,IAAI;oBACzB,KAAK,KAAK,GAAG,IAAI;oBACjB,YAAY,IAAI,OAAO,WAAW;gBACpC;gBAEA,IAAI,QAAQ;oBACV,yBAAyB;oBACzB,MAAM,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,4BACL,MAAM,CAAC,cACP,EAAE,CAAC,WAAW;oBAEjB,IAAI,eAAe;wBACjB,MAAM,IAAI,MAAM;oBAClB;gBACF,OAAO;oBACL,oBAAoB;oBACpB,MAAM,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,4BACL,MAAM,CAAC;wBACN,SAAS;wBACT,GAAG,YAAY;oBACjB;oBAEF,IAAI,eAAe;wBACjB,MAAM,IAAI,MAAM;oBAClB;gBACF;YACF;YAEA,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE;gBACvB,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;QACxB;IACF;AACF", "debugId": null}}]}