{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\nimport type { Database } from '@/types/supabase'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient<Database>(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAGO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/permissions.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport type { Database } from '@/types/supabase'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\n// Create a Supabase client with service role key for server-side operations\nconst supabaseAdmin = createClient<Database>(supabaseUrl, supabaseServiceKey)\n\nexport type Permission = {\n  resource: string;\n  action: 'create' | 'read' | 'update' | 'delete';\n};\n\nexport type Role = 'admin' | 'healthcare_professional' | 'secretary' | 'assistant';\n\n/**\n * Check if a user has permission to access a resource based on user associations\n */\nexport async function hasPermission(\n  userId: string, \n  resource: string, \n  action: 'create' | 'read' | 'update' | 'delete'\n): Promise<boolean> {\n  try {\n    // Get user role from simplified system\n    const { data: user, error: userError } = await supabaseAdmin\n      .from('users')\n      .select('role')\n      .eq('id', userId)\n      .single()\n\n    if (userError || !user) {\n      return false\n    }\n\n    // Admin has all permissions\n    if (user.role === 'admin') {\n      return true\n    }\n\n    // Healthcare professionals have full permissions for their own resources\n    if (user.role === 'healthcare_professional') {\n      return hasHealthcareProfessionalPermission(resource, action)\n    }\n\n    // For secretaries, check user associations to see what they can access\n    if (user.role === 'secretary') {\n      return await hasSecretaryPermission(userId, resource, action)\n    }\n\n    // Assistants have read-only access to basic resources\n    if (user.role === 'assistant') {\n      return hasAssistantPermission(resource, action)\n    }\n\n    return false\n  } catch (error) {\n    console.error('Error in hasPermission:', error)\n    return false\n  }\n}\n\n/**\n * Check secretary permissions based on user associations\n */\nasync function hasSecretaryPermission(\n  userId: string,\n  resource: string,\n  action: 'create' | 'read' | 'update' | 'delete'\n): Promise<boolean> {\n  try {\n    // Secretaries can create, read, and update appointments and patients for doctors they're associated with\n    if (resource === 'appointments' && ['create', 'read', 'update'].includes(action)) {\n      return true\n    }\n    \n    if (resource === 'patients' && ['create', 'read', 'update'].includes(action)) {\n      return true\n    }\n\n    return false\n  } catch (error) {\n    console.error('Error checking secretary permissions:', error)\n    return false\n  }\n}\n\n/**\n * Healthcare professional permissions\n */\nfunction hasHealthcareProfessionalPermission(\n  resource: string,\n  action: 'create' | 'read' | 'update' | 'delete'\n): boolean {\n  // Healthcare professionals have full access to their own data\n  const allowedResources = ['appointments', 'patients', 'medical_records']\n  return allowedResources.includes(resource)\n}\n\n/**\n * Assistant permissions (read-only)\n */\nfunction hasAssistantPermission(\n  resource: string,\n  action: 'create' | 'read' | 'update' | 'delete'\n): boolean {\n  // Assistants have read-only access\n  return action === 'read' && ['appointments', 'patients'].includes(resource)\n}\n\n/**\n * Check if a user has any of the specified roles\n */\nexport async function hasRole(userId: string, roles: Role[]): Promise<boolean> {\n  try {\n    const { data: user, error } = await supabaseAdmin\n      .from('users')\n      .select('role')\n      .eq('id', userId)\n      .single()\n\n    if (error || !user) {\n      console.error('Error checking roles:', error)\n      return false\n    }\n\n    return roles.includes(user.role as Role)\n  } catch (error) {\n    console.error('Error in hasRole:', error)\n    return false\n  }\n}\n\n/**\n * Get all permissions for a user based on their role and associations\n */\nexport async function getUserPermissions(userId: string): Promise<Permission[]> {\n  try {\n    // Get user role from simplified system\n    const { data: user, error: userError } = await supabaseAdmin\n      .from('users')\n      .select('role')\n      .eq('id', userId)\n      .single()\n\n    if (userError || !user) {\n      return []\n    }\n\n    // Return permissions based on role\n    return DEFAULT_PERMISSIONS[user.role as Role] || []\n  } catch (error) {\n    console.error('Error in getUserPermissions:', error)\n    return []\n  }\n}\n\n/**\n * Check if a user can access a specific healthcare professional's data\n */\nexport async function canAccessHealthcareProfessional(\n  userId: string,\n  healthcareProfessionalId: string\n): Promise<boolean> {\n  try {\n    // Get user role\n    const { data: user, error: userError } = await supabaseAdmin\n      .from('users')\n      .select('role')\n      .eq('id', userId)\n      .single()\n\n    if (userError || !user) {\n      return false\n    }\n\n    // Admin can access all\n    if (user.role === 'admin') {\n      return true\n    }\n\n    // Healthcare professionals can access their own data\n    if (user.role === 'healthcare_professional') {\n      const { data: professional, error: profError } = await supabaseAdmin\n        .from('healthcare_professionals')\n        .select('id')\n        .eq('user_id', userId)\n        .eq('id', healthcareProfessionalId)\n        .single()\n\n      return !profError && !!professional\n    }\n\n    // Secretaries can access healthcare professionals they're associated with\n    if (user.role === 'secretary') {\n      const { data: professional, error: profError } = await supabaseAdmin\n        .from('healthcare_professionals')\n        .select('user_id')\n        .eq('id', healthcareProfessionalId)\n        .single()\n\n      if (profError || !professional) {\n        return false\n      }\n\n      const { data: association, error: assocError } = await supabaseAdmin\n        .from('user_associations')\n        .select('id')\n        .eq('accessor_user_id', userId)\n        .eq('target_user_id', professional.user_id)\n        .eq('association_type', 'secretary_doctor')\n        .eq('is_active', true)\n        .single()\n\n      return !assocError && !!association\n    }\n\n    return false\n  } catch (error) {\n    console.error('Error checking healthcare professional access:', error)\n    return false\n  }\n}\n\n/**\n * Check if a user is an admin\n */\nexport async function isAdmin(userId: string): Promise<boolean> {\n  return hasRole(userId, ['admin'])\n}\n\n/**\n * Middleware function to check permissions for API routes\n */\nexport function requirePermission(resource: string, action: 'create' | 'read' | 'update' | 'delete') {\n  return async (userId: string): Promise<boolean> => {\n    return hasPermission(userId, resource, action)\n  }\n}\n\n/**\n * Middleware function to check roles for API routes\n */\nexport function requireRole(roles: Role[]) {\n  return async (userId: string): Promise<boolean> => {\n    return hasRole(userId, roles)\n  }\n}\n\n/**\n * Updated default permissions for each role based on association model\n */\nexport const DEFAULT_PERMISSIONS: Record<Role, Permission[]> = {\n  admin: [\n    // Full access to everything\n    { resource: 'appointments', action: 'create' },\n    { resource: 'appointments', action: 'read' },\n    { resource: 'appointments', action: 'update' },\n    { resource: 'appointments', action: 'delete' },\n    { resource: 'patients', action: 'create' },\n    { resource: 'patients', action: 'read' },\n    { resource: 'patients', action: 'update' },\n    { resource: 'patients', action: 'delete' },\n    { resource: 'healthcare_professionals', action: 'create' },\n    { resource: 'healthcare_professionals', action: 'read' },\n    { resource: 'healthcare_professionals', action: 'update' },\n    { resource: 'healthcare_professionals', action: 'delete' },\n    { resource: 'users', action: 'create' },\n    { resource: 'users', action: 'read' },\n    { resource: 'users', action: 'update' },\n    { resource: 'users', action: 'delete' },\n    { resource: 'user_associations', action: 'create' },\n    { resource: 'user_associations', action: 'read' },\n    { resource: 'user_associations', action: 'update' },\n    { resource: 'user_associations', action: 'delete' },\n    { resource: 'medical_records', action: 'create' },\n    { resource: 'medical_records', action: 'read' },\n    { resource: 'medical_records', action: 'update' },\n    { resource: 'medical_records', action: 'delete' },\n    { resource: 'settings', action: 'read' },\n    { resource: 'settings', action: 'update' },\n  ],\n  healthcare_professional: [\n    // Can manage their own appointments, patients and medical records\n    { resource: 'appointments', action: 'create' },\n    { resource: 'appointments', action: 'read' },\n    { resource: 'appointments', action: 'update' },\n    { resource: 'appointments', action: 'delete' },\n    { resource: 'patients', action: 'create' },\n    { resource: 'patients', action: 'read' },\n    { resource: 'patients', action: 'update' },\n    { resource: 'patients', action: 'delete' },\n    { resource: 'medical_records', action: 'create' },\n    { resource: 'medical_records', action: 'read' },\n    { resource: 'medical_records', action: 'update' },\n    { resource: 'medical_records', action: 'delete' },\n  ],\n  secretary: [\n    // Can manage appointments and patients for associated doctors\n    // Note: actual access is controlled by user_associations table\n    { resource: 'appointments', action: 'create' },\n    { resource: 'appointments', action: 'read' },\n    { resource: 'appointments', action: 'update' },\n    { resource: 'patients', action: 'create' },\n    { resource: 'patients', action: 'read' },\n    { resource: 'patients', action: 'update' },\n    { resource: 'healthcare_professionals', action: 'read' }, // Can see associated doctors\n  ],\n  assistant: [\n    // Read-only access to appointments and patients\n    { resource: 'appointments', action: 'read' },\n    { resource: 'patients', action: 'read' },\n    { resource: 'healthcare_professionals', action: 'read' },\n  ],\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGA,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAEhE,4EAA4E;AAC5E,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAY,aAAa;AAYnD,eAAe,cACpB,MAAc,EACd,QAAgB,EAChB,MAA+C;IAE/C,IAAI;QACF,uCAAuC;QACvC,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAC5C,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;QACT;QAEA,4BAA4B;QAC5B,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,OAAO;QACT;QAEA,yEAAyE;QACzE,IAAI,KAAK,IAAI,KAAK,2BAA2B;YAC3C,OAAO,oCAAoC,UAAU;QACvD;QAEA,uEAAuE;QACvE,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,OAAO,MAAM,uBAAuB,QAAQ,UAAU;QACxD;QAEA,sDAAsD;QACtD,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,OAAO,uBAAuB,UAAU;QAC1C;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAEA;;CAEC,GACD,eAAe,uBACb,MAAc,EACd,QAAgB,EAChB,MAA+C;IAE/C,IAAI;QACF,yGAAyG;QACzG,IAAI,aAAa,kBAAkB;YAAC;YAAU;YAAQ;SAAS,CAAC,QAAQ,CAAC,SAAS;YAChF,OAAO;QACT;QAEA,IAAI,aAAa,cAAc;YAAC;YAAU;YAAQ;SAAS,CAAC,QAAQ,CAAC,SAAS;YAC5E,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO;IACT;AACF;AAEA;;CAEC,GACD,SAAS,oCACP,QAAgB,EAChB,MAA+C;IAE/C,8DAA8D;IAC9D,MAAM,mBAAmB;QAAC;QAAgB;QAAY;KAAkB;IACxE,OAAO,iBAAiB,QAAQ,CAAC;AACnC;AAEA;;CAEC,GACD,SAAS,uBACP,QAAgB,EAChB,MAA+C;IAE/C,mCAAmC;IACnC,OAAO,WAAW,UAAU;QAAC;QAAgB;KAAW,CAAC,QAAQ,CAAC;AACpE;AAKO,eAAe,QAAQ,MAAc,EAAE,KAAa;IACzD,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cACjC,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,SAAS,CAAC,MAAM;YAClB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;QAEA,OAAO,MAAM,QAAQ,CAAC,KAAK,IAAI;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO;IACT;AACF;AAKO,eAAe,mBAAmB,MAAc;IACrD,IAAI;QACF,uCAAuC;QACvC,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAC5C,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,EAAE;QACX;QAEA,mCAAmC;QACnC,OAAO,mBAAmB,CAAC,KAAK,IAAI,CAAS,IAAI,EAAE;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,EAAE;IACX;AACF;AAKO,eAAe,gCACpB,MAAc,EACd,wBAAgC;IAEhC,IAAI;QACF,gBAAgB;QAChB,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAC5C,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;QACT;QAEA,uBAAuB;QACvB,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,OAAO;QACT;QAEA,qDAAqD;QACrD,IAAI,KAAK,IAAI,KAAK,2BAA2B;YAC3C,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cACpD,IAAI,CAAC,4BACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,MAAM,0BACT,MAAM;YAET,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB;QAEA,0EAA0E;QAC1E,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cACpD,IAAI,CAAC,4BACL,MAAM,CAAC,WACP,EAAE,CAAC,MAAM,0BACT,MAAM;YAET,IAAI,aAAa,CAAC,cAAc;gBAC9B,OAAO;YACT;YAEA,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,cACpD,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,oBAAoB,QACvB,EAAE,CAAC,kBAAkB,aAAa,OAAO,EACzC,EAAE,CAAC,oBAAoB,oBACvB,EAAE,CAAC,aAAa,MAChB,MAAM;YAET,OAAO,CAAC,cAAc,CAAC,CAAC;QAC1B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,OAAO;IACT;AACF;AAKO,eAAe,QAAQ,MAAc;IAC1C,OAAO,QAAQ,QAAQ;QAAC;KAAQ;AAClC;AAKO,SAAS,kBAAkB,QAAgB,EAAE,MAA+C;IACjG,OAAO,OAAO;QACZ,OAAO,cAAc,QAAQ,UAAU;IACzC;AACF;AAKO,SAAS,YAAY,KAAa;IACvC,OAAO,OAAO;QACZ,OAAO,QAAQ,QAAQ;IACzB;AACF;AAKO,MAAM,sBAAkD;IAC7D,OAAO;QACL,4BAA4B;QAC5B;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAgB,QAAQ;QAAO;QAC3C;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAY,QAAQ;QAAO;QACvC;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAA4B,QAAQ;QAAS;QACzD;YAAE,UAAU;YAA4B,QAAQ;QAAO;QACvD;YAAE,UAAU;YAA4B,QAAQ;QAAS;QACzD;YAAE,UAAU;YAA4B,QAAQ;QAAS;QACzD;YAAE,UAAU;YAAS,QAAQ;QAAS;QACtC;YAAE,UAAU;YAAS,QAAQ;QAAO;QACpC;YAAE,UAAU;YAAS,QAAQ;QAAS;QACtC;YAAE,UAAU;YAAS,QAAQ;QAAS;QACtC;YAAE,UAAU;YAAqB,QAAQ;QAAS;QAClD;YAAE,UAAU;YAAqB,QAAQ;QAAO;QAChD;YAAE,UAAU;YAAqB,QAAQ;QAAS;QAClD;YAAE,UAAU;YAAqB,QAAQ;QAAS;QAClD;YAAE,UAAU;YAAmB,QAAQ;QAAS;QAChD;YAAE,UAAU;YAAmB,QAAQ;QAAO;QAC9C;YAAE,UAAU;YAAmB,QAAQ;QAAS;QAChD;YAAE,UAAU;YAAmB,QAAQ;QAAS;QAChD;YAAE,UAAU;YAAY,QAAQ;QAAO;QACvC;YAAE,UAAU;YAAY,QAAQ;QAAS;KAC1C;IACD,yBAAyB;QACvB,kEAAkE;QAClE;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAgB,QAAQ;QAAO;QAC3C;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAY,QAAQ;QAAO;QACvC;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAmB,QAAQ;QAAS;QAChD;YAAE,UAAU;YAAmB,QAAQ;QAAO;QAC9C;YAAE,UAAU;YAAmB,QAAQ;QAAS;QAChD;YAAE,UAAU;YAAmB,QAAQ;QAAS;KACjD;IACD,WAAW;QACT,8DAA8D;QAC9D,+DAA+D;QAC/D;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAgB,QAAQ;QAAO;QAC3C;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAY,QAAQ;QAAO;QACvC;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAA4B,QAAQ;QAAO;KACxD;IACD,WAAW;QACT,gDAAgD;QAChD;YAAE,UAAU;YAAgB,QAAQ;QAAO;QAC3C;YAAE,UAAU;YAAY,QAAQ;QAAO;QACvC;YAAE,UAAU;YAA4B,QAAQ;QAAO;KACxD;AACH", "debugId": null}}, {"offset": {"line": 557, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/api-utils.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@/lib/supabase/server'\nimport { hasPermission, type Role } from '@/lib/permissions'\n\nexport interface ApiResponse<T = any> {\n  data?: T\n  error?: string\n  message?: string\n}\n\nexport function createApiResponse<T>(\n  data?: T,\n  message?: string,\n  status: number = 200\n): NextResponse<ApiResponse<T>> {\n  return NextResponse.json(\n    {\n      data,\n      message,\n    },\n    { status }\n  )\n}\n\nexport async function withAuth<T>(\n  request: NextRequest,\n  handler: (userId: string, supabase: any) => Promise<NextResponse<ApiResponse<T>>>\n): Promise<NextResponse<ApiResponse<T>>> {\n  try {\n    const supabase = await createClient()\n    \n    const {\n      data: { user },\n      error: authError,\n    } = await supabase.auth.getUser()\n\n    console.log('🔐 Auth check:', {\n      hasUser: !!user,\n      userId: user?.id,\n      email: user?.email,\n      error: authError?.message\n    })\n\n    if (authError || !user) {\n      console.error('❌ Auth failed:', authError)\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    return await handler(user.id, supabase)\n  } catch (error) {\n    console.error('API Error:', error)\n    return NextResponse.json(\n      { error: error instanceof Error ? error.message : 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function withAuthAndPermission<T>(\n  request: NextRequest,\n  resource: string,\n  action: 'create' | 'read' | 'update' | 'delete',\n  handler: (userId: string, supabase: any) => Promise<NextResponse<ApiResponse<T>>>\n): Promise<NextResponse<ApiResponse<T>>> {\n  try {\n    const supabase = await createClient()\n\n    const {\n      data: { user },\n      error: authError,\n    } = await supabase.auth.getUser()\n\n    if (authError || !user) {\n      return createApiResponse(undefined, 'Unauthorized', 401)\n    }\n\n    // Check permissions\n    const hasAccess = await hasPermission(user.id, resource, action)\n    if (!hasAccess) {\n      return createApiResponse(undefined, 'Forbidden: Insufficient permissions', 403)\n    }\n\n    return await handler(user.id, supabase)\n  } catch (error) {\n    console.error('API Error:', error)\n    return createApiResponse(\n      undefined,\n      error instanceof Error ? error.message : 'Internal server error',\n      500\n    )\n  }\n}\n\nexport function handleApiError(error: any): NextResponse<ApiResponse> {\n  console.error('API Error:', error)\n\n  if (error?.code === 'PGRST116') {\n    return NextResponse.json({ error: 'Resource not found' }, { status: 404 })\n  }\n\n  if (error?.code === '23505') {\n    return NextResponse.json({ error: 'Resource already exists' }, { status: 409 })\n  }\n\n  return NextResponse.json(\n    { error: error?.message || 'Internal server error' },\n    { status: 500 }\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAQO,SAAS,kBACd,IAAQ,EACR,OAAgB,EAChB,SAAiB,GAAG;IAEpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE;QACA;IACF,GACA;QAAE;IAAO;AAEb;AAEO,eAAe,SACpB,OAAoB,EACpB,OAAiF;IAEjF,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAE/B,QAAQ,GAAG,CAAC,kBAAkB;YAC5B,SAAS,CAAC,CAAC;YACX,QAAQ,MAAM;YACd,OAAO,MAAM;YACb,OAAO,WAAW;QACpB;QAEA,IAAI,aAAa,CAAC,MAAM;YACtB,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,OAAO,MAAM,QAAQ,KAAK,EAAE,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAAwB,GAC1E;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,sBACpB,OAAoB,EACpB,QAAgB,EAChB,MAA+C,EAC/C,OAAiF;IAEjF,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAE/B,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,kBAAkB,WAAW,gBAAgB;QACtD;QAEA,oBAAoB;QACpB,MAAM,YAAY,MAAM,CAAA,GAAA,2HAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,EAAE,EAAE,UAAU;QACzD,IAAI,CAAC,WAAW;YACd,OAAO,kBAAkB,WAAW,uCAAuC;QAC7E;QAEA,OAAO,MAAM,QAAQ,KAAK,EAAE,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,kBACL,WACA,iBAAiB,QAAQ,MAAM,OAAO,GAAG,yBACzC;IAEJ;AACF;AAEO,SAAS,eAAe,KAAU;IACvC,QAAQ,KAAK,CAAC,cAAc;IAE5B,IAAI,OAAO,SAAS,YAAY;QAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAqB,GAAG;YAAE,QAAQ;QAAI;IAC1E;IAEA,IAAI,OAAO,SAAS,SAAS;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA0B,GAAG;YAAE,QAAQ;QAAI;IAC/E;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,OAAO,OAAO,WAAW;IAAwB,GACnD;QAAE,QAAQ;IAAI;AAElB", "debugId": null}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/encryption.ts"], "sourcesContent": ["import crypto from 'crypto';\n\n/**\n * Medical data encryption utility\n * Uses AES-256-GCM for secure encryption of sensitive medical records\n */\n\nconst ALGORITHM = 'aes-256-gcm';\nconst KEY_LENGTH = 32; // 256 bits\nconst IV_LENGTH = 16; // 128 bits\nconst TAG_LENGTH = 16; // 128 bits\n\n/**\n * Get encryption key from environment variable\n * In production, this should be stored securely (e.g., AWS KMS, Azure Key Vault)\n */\nfunction getEncryptionKey(): Buffer {\n  const key = process.env.MEDICAL_RECORDS_ENCRYPTION_KEY;\n  \n  if (!key) {\n    throw new Error('MEDICAL_RECORDS_ENCRYPTION_KEY environment variable is required');\n  }\n  \n  // If key is hex string, convert to buffer\n  if (key.length === 64) {\n    return Buffer.from(key, 'hex');\n  }\n  \n  // If key is base64, convert to buffer\n  if (key.length === 44 && key.endsWith('=')) {\n    return Buffer.from(key, 'base64');\n  }\n  \n  // Otherwise, hash the key to ensure it's 32 bytes\n  return crypto.createHash('sha256').update(key).digest();\n}\n\n/**\n * Generate a random encryption key (for initial setup)\n */\nexport function generateEncryptionKey(): string {\n  return crypto.randomBytes(KEY_LENGTH).toString('hex');\n}\n\n/**\n * Encrypt sensitive medical data\n */\nexport function encryptMedicalData(plaintext: string): string {\n  try {\n    const key = getEncryptionKey();\n    const iv = crypto.randomBytes(IV_LENGTH);\n\n    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);\n    cipher.setAAD(Buffer.from('medical-record', 'utf8')); // Additional authenticated data\n\n    let encrypted = cipher.update(plaintext, 'utf8', 'hex');\n    encrypted += cipher.final('hex');\n\n    const tag = cipher.getAuthTag();\n\n    // Combine IV + tag + encrypted data\n    const combined = Buffer.concat([\n      iv,\n      tag,\n      Buffer.from(encrypted, 'hex')\n    ]);\n\n    return combined.toString('base64');\n  } catch (error) {\n    console.error('Encryption error:', error);\n    throw new Error('Failed to encrypt medical data');\n  }\n}\n\n/**\n * Decrypt sensitive medical data\n */\nexport function decryptMedicalData(encryptedData: string): string {\n  try {\n    const key = getEncryptionKey();\n    const combined = Buffer.from(encryptedData, 'base64');\n\n    // Extract IV, tag, and encrypted data\n    const iv = combined.subarray(0, IV_LENGTH);\n    const tag = combined.subarray(IV_LENGTH, IV_LENGTH + TAG_LENGTH);\n    const encrypted = combined.subarray(IV_LENGTH + TAG_LENGTH);\n\n    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);\n    decipher.setAuthTag(tag);\n    decipher.setAAD(Buffer.from('medical-record', 'utf8'));\n\n    let decrypted = decipher.update(encrypted, undefined, 'utf8');\n    decrypted += decipher.final('utf8');\n\n    return decrypted;\n  } catch (error) {\n    console.error('Decryption error:', error);\n    throw new Error('Failed to decrypt medical data');\n  }\n}\n\n/**\n * Validate if data is encrypted (basic check)\n */\nexport function isEncrypted(data: string): boolean {\n  try {\n    // Check if it's a valid base64 string with expected length\n    const buffer = Buffer.from(data, 'base64');\n    return buffer.length > (IV_LENGTH + TAG_LENGTH); // IV + tag + encrypted data for GCM mode\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Safely encrypt medical data with error handling\n */\nexport function safeEncryptMedicalData(plaintext: string): string {\n  if (!plaintext || plaintext.trim() === '') {\n    return plaintext;\n  }\n  \n  try {\n    return encryptMedicalData(plaintext);\n  } catch (error) {\n    console.error('Safe encryption failed, storing as plaintext:', error);\n    // In production, you might want to fail here instead of storing plaintext\n    return plaintext;\n  }\n}\n\n/**\n * Safely decrypt medical data with error handling\n */\nexport function safeDecryptMedicalData(encryptedData: string): string {\n  if (!encryptedData || encryptedData.trim() === '') {\n    return encryptedData;\n  }\n  \n  // If data doesn't look encrypted, return as-is (for backward compatibility)\n  if (!isEncrypted(encryptedData)) {\n    return encryptedData;\n  }\n  \n  try {\n    return decryptMedicalData(encryptedData);\n  } catch (error) {\n    console.error('Safe decryption failed:', error);\n    // Return a placeholder or the encrypted data (depending on your security policy)\n    return '[ENCRYPTED DATA - DECRYPTION FAILED]';\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEA;;;CAGC,GAED,MAAM,YAAY;AAClB,MAAM,aAAa,IAAI,WAAW;AAClC,MAAM,YAAY,IAAI,WAAW;AACjC,MAAM,aAAa,IAAI,WAAW;AAElC;;;CAGC,GACD,SAAS;IACP,MAAM,MAAM,QAAQ,GAAG,CAAC,8BAA8B;IAEtD,IAAI,CAAC,KAAK;QACR,MAAM,IAAI,MAAM;IAClB;IAEA,0CAA0C;IAC1C,IAAI,IAAI,MAAM,KAAK,IAAI;QACrB,OAAO,OAAO,IAAI,CAAC,KAAK;IAC1B;IAEA,sCAAsC;IACtC,IAAI,IAAI,MAAM,KAAK,MAAM,IAAI,QAAQ,CAAC,MAAM;QAC1C,OAAO,OAAO,IAAI,CAAC,KAAK;IAC1B;IAEA,kDAAkD;IAClD,OAAO,qGAAA,CAAA,UAAM,CAAC,UAAU,CAAC,UAAU,MAAM,CAAC,KAAK,MAAM;AACvD;AAKO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC,YAAY,QAAQ,CAAC;AACjD;AAKO,SAAS,mBAAmB,SAAiB;IAClD,IAAI;QACF,MAAM,MAAM;QACZ,MAAM,KAAK,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC;QAE9B,MAAM,SAAS,qGAAA,CAAA,UAAM,CAAC,cAAc,CAAC,WAAW,KAAK;QACrD,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,kBAAkB,UAAU,gCAAgC;QAEtF,IAAI,YAAY,OAAO,MAAM,CAAC,WAAW,QAAQ;QACjD,aAAa,OAAO,KAAK,CAAC;QAE1B,MAAM,MAAM,OAAO,UAAU;QAE7B,oCAAoC;QACpC,MAAM,WAAW,OAAO,MAAM,CAAC;YAC7B;YACA;YACA,OAAO,IAAI,CAAC,WAAW;SACxB;QAED,OAAO,SAAS,QAAQ,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,SAAS,mBAAmB,aAAqB;IACtD,IAAI;QACF,MAAM,MAAM;QACZ,MAAM,WAAW,OAAO,IAAI,CAAC,eAAe;QAE5C,sCAAsC;QACtC,MAAM,KAAK,SAAS,QAAQ,CAAC,GAAG;QAChC,MAAM,MAAM,SAAS,QAAQ,CAAC,WAAW,YAAY;QACrD,MAAM,YAAY,SAAS,QAAQ,CAAC,YAAY;QAEhD,MAAM,WAAW,qGAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,WAAW,KAAK;QACzD,SAAS,UAAU,CAAC;QACpB,SAAS,MAAM,CAAC,OAAO,IAAI,CAAC,kBAAkB;QAE9C,IAAI,YAAY,SAAS,MAAM,CAAC,WAAW,WAAW;QACtD,aAAa,SAAS,KAAK,CAAC;QAE5B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,SAAS,YAAY,IAAY;IACtC,IAAI;QACF,2DAA2D;QAC3D,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM;QACjC,OAAO,OAAO,MAAM,GAAI,YAAY,YAAa,yCAAyC;IAC5F,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,uBAAuB,SAAiB;IACtD,IAAI,CAAC,aAAa,UAAU,IAAI,OAAO,IAAI;QACzC,OAAO;IACT;IAEA,IAAI;QACF,OAAO,mBAAmB;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,0EAA0E;QAC1E,OAAO;IACT;AACF;AAKO,SAAS,uBAAuB,aAAqB;IAC1D,IAAI,CAAC,iBAAiB,cAAc,IAAI,OAAO,IAAI;QACjD,OAAO;IACT;IAEA,4EAA4E;IAC5E,IAAI,CAAC,YAAY,gBAAgB;QAC/B,OAAO;IACT;IAEA,IAAI;QACF,OAAO,mBAAmB;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,iFAAiF;QACjF,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/app/api/medical-records/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'\nimport { safeEncryptMedicalData, safeDecryptMedicalData } from '@/lib/encryption'\n\nexport async function GET(request: NextRequest) {\n  return withAuth(request, async (userId, supabase) => {\n    try {\n      const { searchParams } = new URL(request.url)\n      const appointmentId = searchParams.get('appointment_id')\n      const patientId = searchParams.get('patient_id')\n\n      // Get healthcare professional ID for current user\n      const { data: healthcareProfessional, error: hpError } = await supabase\n        .from('healthcare_professionals')\n        .select('id')\n        .eq('user_id', userId)\n        .single()\n\n      if (hpError) {\n        return handleApiError(hpError)\n      }\n\n      let query = supabase\n        .from('medical_records')\n        .select(`\n          *,\n          appointments!inner(patient_id),\n          healthcare_professionals!inner(name, user_id)\n        `)\n        .eq('healthcare_professional_id', healthcareProfessional.id)\n        .order('created_at', { ascending: false })\n\n      if (appointmentId) {\n        query = query.eq('appointment_id', appointmentId)\n      }\n\n      if (patientId) {\n        query = query.eq('appointments.patient_id', patientId)\n      }\n\n      const { data: records, error } = await query\n\n      if (error) {\n        return handleApiError(error)\n      }\n\n      // Format the response to include creator name and decrypt notes\n      const formattedRecords = records?.map((record: any) => ({\n        ...record,\n        notes: safeDecryptMedicalData(record.notes),\n        created_by_name: record.healthcare_professionals?.name || 'Sistema'\n      })) || []\n\n      return createApiResponse(formattedRecords)\n    } catch (error) {\n      return handleApiError(error)\n    }\n  })\n}\n\nexport async function POST(request: NextRequest) {\n  return withAuth(request, async (userId, supabase) => {\n    try {\n      const body = await request.json()\n      const { appointment_id, patient_id, notes, is_draft = false } = body\n\n      if (!appointment_id || !notes?.trim()) {\n        return handleApiError(new Error('appointment_id and notes are required'))\n      }\n\n      // Get healthcare professional ID for current user\n      const { data: healthcareProfessional, error: hpError } = await supabase\n        .from('healthcare_professionals')\n        .select('id, name')\n        .eq('user_id', userId)\n        .single()\n\n      if (hpError || !healthcareProfessional) {\n        return handleApiError(new Error('Healthcare professional not found'))\n      }\n\n      // Verify the appointment exists and get patient_id if not provided\n      const { data: appointment, error: appointmentError } = await supabase\n        .from('appointments')\n        .select('id, patient_id')\n        .eq('id', appointment_id)\n        .single()\n\n      if (appointmentError || !appointment) {\n        return handleApiError(new Error('Appointment not found'))\n      }\n\n      const recordData = {\n        appointment_id,\n        patient_id: patient_id || appointment.patient_id,\n        healthcare_professional_id: healthcareProfessional.id,\n        notes: safeEncryptMedicalData(notes.trim()),\n        is_draft: Boolean(is_draft),\n        created_at: new Date().toISOString()\n      }\n\n      const { data: record, error } = await supabase\n        .from('medical_records')\n        .insert(recordData)\n        .select('*')\n        .single()\n\n      if (error) {\n        return handleApiError(error)\n      }\n\n      // Format the response and decrypt notes\n      const formattedRecord = {\n        ...record,\n        notes: safeDecryptMedicalData(record.notes),\n        created_by_name: healthcareProfessional.name || 'Sistema'\n      }\n\n      return createApiResponse(formattedRecord, undefined, 201)\n    } catch (error) {\n      return handleApiError(error)\n    }\n  })\n}\n\nexport async function PUT(request: NextRequest) {\n  return withAuth(request, async (userId, supabase) => {\n    try {\n      const body = await request.json()\n      const { id, notes } = body\n\n      if (!id || !notes?.trim()) {\n        return handleApiError(new Error('id and notes are required'))\n      }\n\n      // Get healthcare professional ID for current user\n      const { data: healthcareProfessional, error: hpError } = await supabase\n        .from('healthcare_professionals')\n        .select('id, name')\n        .eq('user_id', userId)\n        .single()\n\n      if (hpError || !healthcareProfessional) {\n        return handleApiError(new Error('Healthcare professional not found'))\n      }\n\n      const { data: record, error } = await supabase\n        .from('medical_records')\n        .update({\n          notes: safeEncryptMedicalData(notes.trim()),\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', id)\n        .eq('healthcare_professional_id', healthcareProfessional.id)\n        .select('*')\n        .single()\n\n      if (error) {\n        return handleApiError(error)\n      }\n\n      // Format the response and decrypt notes\n      const formattedRecord = {\n        ...record,\n        notes: safeDecryptMedicalData(record.notes),\n        created_by_name: healthcareProfessional.name || 'Sistema'\n      }\n\n      return createApiResponse(formattedRecord)\n    } catch (error) {\n      return handleApiError(error)\n    }\n  })\n}\n\nexport async function DELETE(request: NextRequest) {\n  return withAuth(request, async (userId, supabase) => {\n    try {\n      const { searchParams } = new URL(request.url)\n      const id = searchParams.get('id')\n\n      if (!id) {\n        return handleApiError(new Error('id is required'))\n      }\n\n      // Get healthcare professional ID for current user\n      const { data: healthcareProfessional, error: hpError } = await supabase\n        .from('healthcare_professionals')\n        .select('id')\n        .eq('user_id', userId)\n        .single()\n\n      if (hpError || !healthcareProfessional) {\n        return handleApiError(new Error('Healthcare professional not found'))\n      }\n\n      const { error } = await supabase\n        .from('medical_records')\n        .delete()\n        .eq('id', id)\n        .eq('healthcare_professional_id', healthcareProfessional.id)\n\n      if (error) {\n        return handleApiError(error)\n      }\n\n      return createApiResponse({ message: 'Medical record deleted successfully' })\n    } catch (error) {\n      return handleApiError(error)\n    }\n  })\n}\n"], "names": [], "mappings": ";;;;;;AACA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,OAAO,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO,QAAQ;QACtC,IAAI;YACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;YAC5C,MAAM,gBAAgB,aAAa,GAAG,CAAC;YACvC,MAAM,YAAY,aAAa,GAAG,CAAC;YAEnC,kDAAkD;YAClD,MAAM,EAAE,MAAM,sBAAsB,EAAE,OAAO,OAAO,EAAE,GAAG,MAAM,SAC5D,IAAI,CAAC,4BACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,QACd,MAAM;YAET,IAAI,SAAS;gBACX,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;YACxB;YAEA,IAAI,QAAQ,SACT,IAAI,CAAC,mBACL,MAAM,CAAC,CAAC;;;;QAIT,CAAC,EACA,EAAE,CAAC,8BAA8B,uBAAuB,EAAE,EAC1D,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,eAAe;gBACjB,QAAQ,MAAM,EAAE,CAAC,kBAAkB;YACrC;YAEA,IAAI,WAAW;gBACb,QAAQ,MAAM,EAAE,CAAC,2BAA2B;YAC9C;YAEA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM;YAEvC,IAAI,OAAO;gBACT,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;YACxB;YAEA,gEAAgE;YAChE,MAAM,mBAAmB,SAAS,IAAI,CAAC,SAAgB,CAAC;oBACtD,GAAG,MAAM;oBACT,OAAO,CAAA,GAAA,0HAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,KAAK;oBAC1C,iBAAiB,OAAO,wBAAwB,EAAE,QAAQ;gBAC5D,CAAC,MAAM,EAAE;YAET,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE;QAC3B,EAAE,OAAO,OAAO;YACd,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;QACxB;IACF;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,OAAO,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO,QAAQ;QACtC,IAAI;YACF,MAAM,OAAO,MAAM,QAAQ,IAAI;YAC/B,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,KAAK,EAAE,GAAG;YAEhE,IAAI,CAAC,kBAAkB,CAAC,OAAO,QAAQ;gBACrC,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,MAAM;YAClC;YAEA,kDAAkD;YAClD,MAAM,EAAE,MAAM,sBAAsB,EAAE,OAAO,OAAO,EAAE,GAAG,MAAM,SAC5D,IAAI,CAAC,4BACL,MAAM,CAAC,YACP,EAAE,CAAC,WAAW,QACd,MAAM;YAET,IAAI,WAAW,CAAC,wBAAwB;gBACtC,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,MAAM;YAClC;YAEA,mEAAmE;YACnE,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,SAC1D,IAAI,CAAC,gBACL,MAAM,CAAC,kBACP,EAAE,CAAC,MAAM,gBACT,MAAM;YAET,IAAI,oBAAoB,CAAC,aAAa;gBACpC,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,MAAM;YAClC;YAEA,MAAM,aAAa;gBACjB;gBACA,YAAY,cAAc,YAAY,UAAU;gBAChD,4BAA4B,uBAAuB,EAAE;gBACrD,OAAO,CAAA,GAAA,0HAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM,IAAI;gBACxC,UAAU,QAAQ;gBAClB,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,mBACL,MAAM,CAAC,YACP,MAAM,CAAC,KACP,MAAM;YAET,IAAI,OAAO;gBACT,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;YACxB;YAEA,wCAAwC;YACxC,MAAM,kBAAkB;gBACtB,GAAG,MAAM;gBACT,OAAO,CAAA,GAAA,0HAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,KAAK;gBAC1C,iBAAiB,uBAAuB,IAAI,IAAI;YAClD;YAEA,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,iBAAiB,WAAW;QACvD,EAAE,OAAO,OAAO;YACd,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;QACxB;IACF;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,OAAO,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO,QAAQ;QACtC,IAAI;YACF,MAAM,OAAO,MAAM,QAAQ,IAAI;YAC/B,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG;YAEtB,IAAI,CAAC,MAAM,CAAC,OAAO,QAAQ;gBACzB,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,MAAM;YAClC;YAEA,kDAAkD;YAClD,MAAM,EAAE,MAAM,sBAAsB,EAAE,OAAO,OAAO,EAAE,GAAG,MAAM,SAC5D,IAAI,CAAC,4BACL,MAAM,CAAC,YACP,EAAE,CAAC,WAAW,QACd,MAAM;YAET,IAAI,WAAW,CAAC,wBAAwB;gBACtC,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,MAAM;YAClC;YAEA,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,mBACL,MAAM,CAAC;gBACN,OAAO,CAAA,GAAA,0HAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM,IAAI;gBACxC,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,8BAA8B,uBAAuB,EAAE,EAC1D,MAAM,CAAC,KACP,MAAM;YAET,IAAI,OAAO;gBACT,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;YACxB;YAEA,wCAAwC;YACxC,MAAM,kBAAkB;gBACtB,GAAG,MAAM;gBACT,OAAO,CAAA,GAAA,0HAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,KAAK;gBAC1C,iBAAiB,uBAAuB,IAAI,IAAI;YAClD;YAEA,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE;QAC3B,EAAE,OAAO,OAAO;YACd,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;QACxB;IACF;AACF;AAEO,eAAe,OAAO,OAAoB;IAC/C,OAAO,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO,QAAQ;QACtC,IAAI;YACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;YAC5C,MAAM,KAAK,aAAa,GAAG,CAAC;YAE5B,IAAI,CAAC,IAAI;gBACP,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,MAAM;YAClC;YAEA,kDAAkD;YAClD,MAAM,EAAE,MAAM,sBAAsB,EAAE,OAAO,OAAO,EAAE,GAAG,MAAM,SAC5D,IAAI,CAAC,4BACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,QACd,MAAM;YAET,IAAI,WAAW,CAAC,wBAAwB;gBACtC,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,MAAM;YAClC;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,mBACL,MAAM,GACN,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,8BAA8B,uBAAuB,EAAE;YAE7D,IAAI,OAAO;gBACT,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;YACxB;YAEA,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE;gBAAE,SAAS;YAAsC;QAC5E,EAAE,OAAO,OAAO;YACd,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;QACxB;IACF;AACF", "debugId": null}}]}