{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\nimport type { Database } from '@/types/supabase'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient<Database>(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAGO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/permissions.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport type { Database } from '@/types/supabase'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\n// Create a Supabase client with service role key for server-side operations\nconst supabaseAdmin = createClient<Database>(supabaseUrl, supabaseServiceKey)\n\nexport type Permission = {\n  resource: string;\n  action: 'create' | 'read' | 'update' | 'delete';\n};\n\nexport type Role = 'admin' | 'healthcare_professional' | 'secretary' | 'assistant';\n\n/**\n * Check if a user has permission to access a resource based on user associations\n */\nexport async function hasPermission(\n  userId: string, \n  resource: string, \n  action: 'create' | 'read' | 'update' | 'delete'\n): Promise<boolean> {\n  try {\n    // Get user role from simplified system\n    const { data: user, error: userError } = await supabaseAdmin\n      .from('users')\n      .select('role')\n      .eq('id', userId)\n      .single()\n\n    if (userError || !user) {\n      return false\n    }\n\n    // Admin has all permissions\n    if (user.role === 'admin') {\n      return true\n    }\n\n    // Healthcare professionals have full permissions for their own resources\n    if (user.role === 'healthcare_professional') {\n      return hasHealthcareProfessionalPermission(resource, action)\n    }\n\n    // For secretaries, check user associations to see what they can access\n    if (user.role === 'secretary') {\n      return await hasSecretaryPermission(userId, resource, action)\n    }\n\n    // Assistants have read-only access to basic resources\n    if (user.role === 'assistant') {\n      return hasAssistantPermission(resource, action)\n    }\n\n    return false\n  } catch (error) {\n    console.error('Error in hasPermission:', error)\n    return false\n  }\n}\n\n/**\n * Check secretary permissions based on user associations\n */\nasync function hasSecretaryPermission(\n  userId: string,\n  resource: string,\n  action: 'create' | 'read' | 'update' | 'delete'\n): Promise<boolean> {\n  try {\n    // Secretaries can create, read, and update appointments and patients for doctors they're associated with\n    if (resource === 'appointments' && ['create', 'read', 'update'].includes(action)) {\n      return true\n    }\n    \n    if (resource === 'patients' && ['create', 'read', 'update'].includes(action)) {\n      return true\n    }\n\n    return false\n  } catch (error) {\n    console.error('Error checking secretary permissions:', error)\n    return false\n  }\n}\n\n/**\n * Healthcare professional permissions\n */\nfunction hasHealthcareProfessionalPermission(\n  resource: string,\n  action: 'create' | 'read' | 'update' | 'delete'\n): boolean {\n  // Healthcare professionals have full access to their own data\n  const allowedResources = ['appointments', 'patients', 'medical_records']\n  return allowedResources.includes(resource)\n}\n\n/**\n * Assistant permissions (read-only)\n */\nfunction hasAssistantPermission(\n  resource: string,\n  action: 'create' | 'read' | 'update' | 'delete'\n): boolean {\n  // Assistants have read-only access\n  return action === 'read' && ['appointments', 'patients'].includes(resource)\n}\n\n/**\n * Check if a user has any of the specified roles\n */\nexport async function hasRole(userId: string, roles: Role[]): Promise<boolean> {\n  try {\n    const { data: user, error } = await supabaseAdmin\n      .from('users')\n      .select('role')\n      .eq('id', userId)\n      .single()\n\n    if (error || !user) {\n      console.error('Error checking roles:', error)\n      return false\n    }\n\n    return roles.includes(user.role as Role)\n  } catch (error) {\n    console.error('Error in hasRole:', error)\n    return false\n  }\n}\n\n/**\n * Get all permissions for a user based on their role and associations\n */\nexport async function getUserPermissions(userId: string): Promise<Permission[]> {\n  try {\n    // Get user role from simplified system\n    const { data: user, error: userError } = await supabaseAdmin\n      .from('users')\n      .select('role')\n      .eq('id', userId)\n      .single()\n\n    if (userError || !user) {\n      return []\n    }\n\n    // Return permissions based on role\n    return DEFAULT_PERMISSIONS[user.role as Role] || []\n  } catch (error) {\n    console.error('Error in getUserPermissions:', error)\n    return []\n  }\n}\n\n/**\n * Check if a user can access a specific healthcare professional's data\n */\nexport async function canAccessHealthcareProfessional(\n  userId: string,\n  healthcareProfessionalId: string\n): Promise<boolean> {\n  try {\n    // Get user role\n    const { data: user, error: userError } = await supabaseAdmin\n      .from('users')\n      .select('role')\n      .eq('id', userId)\n      .single()\n\n    if (userError || !user) {\n      return false\n    }\n\n    // Admin can access all\n    if (user.role === 'admin') {\n      return true\n    }\n\n    // Healthcare professionals can access their own data\n    if (user.role === 'healthcare_professional') {\n      const { data: professional, error: profError } = await supabaseAdmin\n        .from('healthcare_professionals')\n        .select('id')\n        .eq('user_id', userId)\n        .eq('id', healthcareProfessionalId)\n        .single()\n\n      return !profError && !!professional\n    }\n\n    // Secretaries can access healthcare professionals they're associated with\n    if (user.role === 'secretary') {\n      const { data: professional, error: profError } = await supabaseAdmin\n        .from('healthcare_professionals')\n        .select('user_id')\n        .eq('id', healthcareProfessionalId)\n        .single()\n\n      if (profError || !professional) {\n        return false\n      }\n\n      const { data: association, error: assocError } = await supabaseAdmin\n        .from('user_associations')\n        .select('id')\n        .eq('accessor_user_id', userId)\n        .eq('target_user_id', professional.user_id)\n        .eq('association_type', 'secretary_doctor')\n        .eq('is_active', true)\n        .single()\n\n      return !assocError && !!association\n    }\n\n    return false\n  } catch (error) {\n    console.error('Error checking healthcare professional access:', error)\n    return false\n  }\n}\n\n/**\n * Check if a user is an admin\n */\nexport async function isAdmin(userId: string): Promise<boolean> {\n  return hasRole(userId, ['admin'])\n}\n\n/**\n * Middleware function to check permissions for API routes\n */\nexport function requirePermission(resource: string, action: 'create' | 'read' | 'update' | 'delete') {\n  return async (userId: string): Promise<boolean> => {\n    return hasPermission(userId, resource, action)\n  }\n}\n\n/**\n * Middleware function to check roles for API routes\n */\nexport function requireRole(roles: Role[]) {\n  return async (userId: string): Promise<boolean> => {\n    return hasRole(userId, roles)\n  }\n}\n\n/**\n * Updated default permissions for each role based on association model\n */\nexport const DEFAULT_PERMISSIONS: Record<Role, Permission[]> = {\n  admin: [\n    // Full access to everything\n    { resource: 'appointments', action: 'create' },\n    { resource: 'appointments', action: 'read' },\n    { resource: 'appointments', action: 'update' },\n    { resource: 'appointments', action: 'delete' },\n    { resource: 'patients', action: 'create' },\n    { resource: 'patients', action: 'read' },\n    { resource: 'patients', action: 'update' },\n    { resource: 'patients', action: 'delete' },\n    { resource: 'healthcare_professionals', action: 'create' },\n    { resource: 'healthcare_professionals', action: 'read' },\n    { resource: 'healthcare_professionals', action: 'update' },\n    { resource: 'healthcare_professionals', action: 'delete' },\n    { resource: 'users', action: 'create' },\n    { resource: 'users', action: 'read' },\n    { resource: 'users', action: 'update' },\n    { resource: 'users', action: 'delete' },\n    { resource: 'user_associations', action: 'create' },\n    { resource: 'user_associations', action: 'read' },\n    { resource: 'user_associations', action: 'update' },\n    { resource: 'user_associations', action: 'delete' },\n    { resource: 'medical_records', action: 'create' },\n    { resource: 'medical_records', action: 'read' },\n    { resource: 'medical_records', action: 'update' },\n    { resource: 'medical_records', action: 'delete' },\n    { resource: 'settings', action: 'read' },\n    { resource: 'settings', action: 'update' },\n  ],\n  healthcare_professional: [\n    // Can manage their own appointments, patients and medical records\n    { resource: 'appointments', action: 'create' },\n    { resource: 'appointments', action: 'read' },\n    { resource: 'appointments', action: 'update' },\n    { resource: 'appointments', action: 'delete' },\n    { resource: 'patients', action: 'create' },\n    { resource: 'patients', action: 'read' },\n    { resource: 'patients', action: 'update' },\n    { resource: 'patients', action: 'delete' },\n    { resource: 'medical_records', action: 'create' },\n    { resource: 'medical_records', action: 'read' },\n    { resource: 'medical_records', action: 'update' },\n    { resource: 'medical_records', action: 'delete' },\n  ],\n  secretary: [\n    // Can manage appointments and patients for associated doctors\n    // Note: actual access is controlled by user_associations table\n    { resource: 'appointments', action: 'create' },\n    { resource: 'appointments', action: 'read' },\n    { resource: 'appointments', action: 'update' },\n    { resource: 'patients', action: 'create' },\n    { resource: 'patients', action: 'read' },\n    { resource: 'patients', action: 'update' },\n    { resource: 'healthcare_professionals', action: 'read' }, // Can see associated doctors\n  ],\n  assistant: [\n    // Read-only access to appointments and patients\n    { resource: 'appointments', action: 'read' },\n    { resource: 'patients', action: 'read' },\n    { resource: 'healthcare_professionals', action: 'read' },\n  ],\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGA,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAEhE,4EAA4E;AAC5E,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAY,aAAa;AAYnD,eAAe,cACpB,MAAc,EACd,QAAgB,EAChB,MAA+C;IAE/C,IAAI;QACF,uCAAuC;QACvC,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAC5C,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;QACT;QAEA,4BAA4B;QAC5B,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,OAAO;QACT;QAEA,yEAAyE;QACzE,IAAI,KAAK,IAAI,KAAK,2BAA2B;YAC3C,OAAO,oCAAoC,UAAU;QACvD;QAEA,uEAAuE;QACvE,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,OAAO,MAAM,uBAAuB,QAAQ,UAAU;QACxD;QAEA,sDAAsD;QACtD,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,OAAO,uBAAuB,UAAU;QAC1C;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAEA;;CAEC,GACD,eAAe,uBACb,MAAc,EACd,QAAgB,EAChB,MAA+C;IAE/C,IAAI;QACF,yGAAyG;QACzG,IAAI,aAAa,kBAAkB;YAAC;YAAU;YAAQ;SAAS,CAAC,QAAQ,CAAC,SAAS;YAChF,OAAO;QACT;QAEA,IAAI,aAAa,cAAc;YAAC;YAAU;YAAQ;SAAS,CAAC,QAAQ,CAAC,SAAS;YAC5E,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO;IACT;AACF;AAEA;;CAEC,GACD,SAAS,oCACP,QAAgB,EAChB,MAA+C;IAE/C,8DAA8D;IAC9D,MAAM,mBAAmB;QAAC;QAAgB;QAAY;KAAkB;IACxE,OAAO,iBAAiB,QAAQ,CAAC;AACnC;AAEA;;CAEC,GACD,SAAS,uBACP,QAAgB,EAChB,MAA+C;IAE/C,mCAAmC;IACnC,OAAO,WAAW,UAAU;QAAC;QAAgB;KAAW,CAAC,QAAQ,CAAC;AACpE;AAKO,eAAe,QAAQ,MAAc,EAAE,KAAa;IACzD,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cACjC,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,SAAS,CAAC,MAAM;YAClB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;QAEA,OAAO,MAAM,QAAQ,CAAC,KAAK,IAAI;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO;IACT;AACF;AAKO,eAAe,mBAAmB,MAAc;IACrD,IAAI;QACF,uCAAuC;QACvC,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAC5C,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,EAAE;QACX;QAEA,mCAAmC;QACnC,OAAO,mBAAmB,CAAC,KAAK,IAAI,CAAS,IAAI,EAAE;IACrD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,EAAE;IACX;AACF;AAKO,eAAe,gCACpB,MAAc,EACd,wBAAgC;IAEhC,IAAI;QACF,gBAAgB;QAChB,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAC5C,IAAI,CAAC,SACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;QACT;QAEA,uBAAuB;QACvB,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,OAAO;QACT;QAEA,qDAAqD;QACrD,IAAI,KAAK,IAAI,KAAK,2BAA2B;YAC3C,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cACpD,IAAI,CAAC,4BACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,MAAM,0BACT,MAAM;YAET,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB;QAEA,0EAA0E;QAC1E,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cACpD,IAAI,CAAC,4BACL,MAAM,CAAC,WACP,EAAE,CAAC,MAAM,0BACT,MAAM;YAET,IAAI,aAAa,CAAC,cAAc;gBAC9B,OAAO;YACT;YAEA,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,cACpD,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,oBAAoB,QACvB,EAAE,CAAC,kBAAkB,aAAa,OAAO,EACzC,EAAE,CAAC,oBAAoB,oBACvB,EAAE,CAAC,aAAa,MAChB,MAAM;YAET,OAAO,CAAC,cAAc,CAAC,CAAC;QAC1B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,OAAO;IACT;AACF;AAKO,eAAe,QAAQ,MAAc;IAC1C,OAAO,QAAQ,QAAQ;QAAC;KAAQ;AAClC;AAKO,SAAS,kBAAkB,QAAgB,EAAE,MAA+C;IACjG,OAAO,OAAO;QACZ,OAAO,cAAc,QAAQ,UAAU;IACzC;AACF;AAKO,SAAS,YAAY,KAAa;IACvC,OAAO,OAAO;QACZ,OAAO,QAAQ,QAAQ;IACzB;AACF;AAKO,MAAM,sBAAkD;IAC7D,OAAO;QACL,4BAA4B;QAC5B;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAgB,QAAQ;QAAO;QAC3C;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAY,QAAQ;QAAO;QACvC;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAA4B,QAAQ;QAAS;QACzD;YAAE,UAAU;YAA4B,QAAQ;QAAO;QACvD;YAAE,UAAU;YAA4B,QAAQ;QAAS;QACzD;YAAE,UAAU;YAA4B,QAAQ;QAAS;QACzD;YAAE,UAAU;YAAS,QAAQ;QAAS;QACtC;YAAE,UAAU;YAAS,QAAQ;QAAO;QACpC;YAAE,UAAU;YAAS,QAAQ;QAAS;QACtC;YAAE,UAAU;YAAS,QAAQ;QAAS;QACtC;YAAE,UAAU;YAAqB,QAAQ;QAAS;QAClD;YAAE,UAAU;YAAqB,QAAQ;QAAO;QAChD;YAAE,UAAU;YAAqB,QAAQ;QAAS;QAClD;YAAE,UAAU;YAAqB,QAAQ;QAAS;QAClD;YAAE,UAAU;YAAmB,QAAQ;QAAS;QAChD;YAAE,UAAU;YAAmB,QAAQ;QAAO;QAC9C;YAAE,UAAU;YAAmB,QAAQ;QAAS;QAChD;YAAE,UAAU;YAAmB,QAAQ;QAAS;QAChD;YAAE,UAAU;YAAY,QAAQ;QAAO;QACvC;YAAE,UAAU;YAAY,QAAQ;QAAS;KAC1C;IACD,yBAAyB;QACvB,kEAAkE;QAClE;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAgB,QAAQ;QAAO;QAC3C;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAY,QAAQ;QAAO;QACvC;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAmB,QAAQ;QAAS;QAChD;YAAE,UAAU;YAAmB,QAAQ;QAAO;QAC9C;YAAE,UAAU;YAAmB,QAAQ;QAAS;QAChD;YAAE,UAAU;YAAmB,QAAQ;QAAS;KACjD;IACD,WAAW;QACT,8DAA8D;QAC9D,+DAA+D;QAC/D;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAgB,QAAQ;QAAO;QAC3C;YAAE,UAAU;YAAgB,QAAQ;QAAS;QAC7C;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAAY,QAAQ;QAAO;QACvC;YAAE,UAAU;YAAY,QAAQ;QAAS;QACzC;YAAE,UAAU;YAA4B,QAAQ;QAAO;KACxD;IACD,WAAW;QACT,gDAAgD;QAChD;YAAE,UAAU;YAAgB,QAAQ;QAAO;QAC3C;YAAE,UAAU;YAAY,QAAQ;QAAO;QACvC;YAAE,UAAU;YAA4B,QAAQ;QAAO;KACxD;AACH", "debugId": null}}, {"offset": {"line": 557, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/api-utils.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@/lib/supabase/server'\nimport { hasPermission, type Role } from '@/lib/permissions'\n\nexport interface ApiResponse<T = any> {\n  data?: T\n  error?: string\n  message?: string\n}\n\nexport function createApiResponse<T>(\n  data?: T,\n  message?: string,\n  status: number = 200\n): NextResponse<ApiResponse<T>> {\n  return NextResponse.json(\n    {\n      data,\n      message,\n    },\n    { status }\n  )\n}\n\nexport async function withAuth<T>(\n  request: NextRequest,\n  handler: (userId: string, supabase: any) => Promise<NextResponse<ApiResponse<T>>>\n): Promise<NextResponse<ApiResponse<T>>> {\n  try {\n    const supabase = await createClient()\n    \n    const {\n      data: { user },\n      error: authError,\n    } = await supabase.auth.getUser()\n\n    console.log('🔐 Auth check:', {\n      hasUser: !!user,\n      userId: user?.id,\n      email: user?.email,\n      error: authError?.message\n    })\n\n    if (authError || !user) {\n      console.error('❌ Auth failed:', authError)\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    return await handler(user.id, supabase)\n  } catch (error) {\n    console.error('API Error:', error)\n    return NextResponse.json(\n      { error: error instanceof Error ? error.message : 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function withAuthAndPermission<T>(\n  request: NextRequest,\n  resource: string,\n  action: 'create' | 'read' | 'update' | 'delete',\n  handler: (userId: string, supabase: any) => Promise<NextResponse<ApiResponse<T>>>\n): Promise<NextResponse<ApiResponse<T>>> {\n  try {\n    const supabase = await createClient()\n\n    const {\n      data: { user },\n      error: authError,\n    } = await supabase.auth.getUser()\n\n    if (authError || !user) {\n      return createApiResponse(undefined, 'Unauthorized', 401)\n    }\n\n    // Check permissions\n    const hasAccess = await hasPermission(user.id, resource, action)\n    if (!hasAccess) {\n      return createApiResponse(undefined, 'Forbidden: Insufficient permissions', 403)\n    }\n\n    return await handler(user.id, supabase)\n  } catch (error) {\n    console.error('API Error:', error)\n    return createApiResponse(\n      undefined,\n      error instanceof Error ? error.message : 'Internal server error',\n      500\n    )\n  }\n}\n\nexport function handleApiError(error: any): NextResponse<ApiResponse> {\n  console.error('API Error:', error)\n\n  if (error?.code === 'PGRST116') {\n    return NextResponse.json({ error: 'Resource not found' }, { status: 404 })\n  }\n\n  if (error?.code === '23505') {\n    return NextResponse.json({ error: 'Resource already exists' }, { status: 409 })\n  }\n\n  return NextResponse.json(\n    { error: error?.message || 'Internal server error' },\n    { status: 500 }\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAQO,SAAS,kBACd,IAAQ,EACR,OAAgB,EAChB,SAAiB,GAAG;IAEpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE;QACA;IACF,GACA;QAAE;IAAO;AAEb;AAEO,eAAe,SACpB,OAAoB,EACpB,OAAiF;IAEjF,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAE/B,QAAQ,GAAG,CAAC,kBAAkB;YAC5B,SAAS,CAAC,CAAC;YACX,QAAQ,MAAM;YACd,OAAO,MAAM;YACb,OAAO,WAAW;QACpB;QAEA,IAAI,aAAa,CAAC,MAAM;YACtB,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,OAAO,MAAM,QAAQ,KAAK,EAAE,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAAwB,GAC1E;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,sBACpB,OAAoB,EACpB,QAAgB,EAChB,MAA+C,EAC/C,OAAiF;IAEjF,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAE/B,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,kBAAkB,WAAW,gBAAgB;QACtD;QAEA,oBAAoB;QACpB,MAAM,YAAY,MAAM,CAAA,GAAA,2HAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,EAAE,EAAE,UAAU;QACzD,IAAI,CAAC,WAAW;YACd,OAAO,kBAAkB,WAAW,uCAAuC;QAC7E;QAEA,OAAO,MAAM,QAAQ,KAAK,EAAE,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,kBACL,WACA,iBAAiB,QAAQ,MAAM,OAAO,GAAG,yBACzC;IAEJ;AACF;AAEO,SAAS,eAAe,KAAU;IACvC,QAAQ,KAAK,CAAC,cAAc;IAE5B,IAAI,OAAO,SAAS,YAAY;QAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAqB,GAAG;YAAE,QAAQ;QAAI;IAC1E;IAEA,IAAI,OAAO,SAAS,SAAS;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA0B,GAAG;YAAE,QAAQ;QAAI;IAC/E;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,OAAO,OAAO,WAAW;IAAwB,GACnD;QAAE,QAAQ;IAAI;AAElB", "debugId": null}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/app/api/patients/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'\nimport type { Tables, TablesUpdate } from '@/types/supabase'\n\ntype Patient = Tables<'patients'>\ntype PatientUpdate = TablesUpdate<'patients'>\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  const { id } = await params\n\n  return withAuth(request, async (userId, supabase) => {\n    try {\n      // Use the new get_accessible_patients function to check access\n      const { data: accessiblePatients, error } = await supabase\n        .rpc('get_accessible_patients', { current_user_id: userId })\n\n      if (error) {\n        return handleApiError(error)\n      }\n\n      // Find the specific patient in the accessible patients list\n      const patient = accessiblePatients?.find((p: any) => p.id === id)\n\n      if (!patient) {\n        return createApiResponse(null, 'Patient not found or access denied', 404)\n      }\n\n      return createApiResponse(patient)\n    } catch (error) {\n      return handleApiError(error)\n    }\n  })\n}\n\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  const { id } = await params\n\n  return withAuth(request, async (userId, supabase) => {\n    try {\n      // Check if user has access to this patient\n      const { data: accessiblePatients, error: accessError } = await supabase\n        .rpc('get_accessible_patients', { current_user_id: userId })\n\n      if (accessError) {\n        return handleApiError(accessError)\n      }\n\n      const hasAccess = accessiblePatients?.some((p: any) => p.id === id)\n      if (!hasAccess) {\n        return createApiResponse(null, 'Patient not found or access denied', 404)\n      }\n\n      const body = await request.json()\n\n      const updateData: PatientUpdate = {\n        ...body,\n        birth_date: body.birth_date || null\n      }\n\n      const { data: patient, error } = await supabase\n        .from('patients')\n        .update(updateData)\n        .eq('id', id)\n        .select()\n        .single()\n\n      if (error) {\n        return handleApiError(error)\n      }\n\n      return createApiResponse(patient)\n    } catch (error) {\n      return handleApiError(error)\n    }\n  })\n}\n\nexport async function PATCH(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  const { id } = await params\n\n  return withAuth(request, async (userId, supabase) => {\n    try {\n      console.log('🔄 PATCH Patient ID:', id, 'by user:', userId);\n\n      // Check if user has access to this patient\n      const { data: accessiblePatients, error: accessError } = await supabase\n        .rpc('get_accessible_patients', { current_user_id: userId })\n\n      if (accessError) {\n        console.error('❌ Access check error:', accessError);\n        return handleApiError(accessError)\n      }\n\n      const hasAccess = accessiblePatients?.some((p: any) => p.id === id)\n      if (!hasAccess) {\n        console.error('❌ Access denied for patient:', id, 'user:', userId);\n        return createApiResponse(null, 'Patient not found or access denied', 404)\n      }\n\n      const body = await request.json()\n      console.log('📝 Update data:', body);\n\n      const updateData: PatientUpdate = {\n        name: body.name,\n        email: body.email || null,\n        phone: body.phone || null,\n        birth_date: body.birth_date || null,\n        cpf: body.cpf || null,\n        address: body.address || null,\n        notes: body.notes || null\n      }\n\n      const { data: patient, error } = await supabase\n        .from('patients')\n        .update(updateData)\n        .eq('id', id)\n        .select()\n        .single()\n\n      if (error) {\n        console.error('❌ Database update error:', error);\n        return handleApiError(error)\n      }\n\n      // Handle healthcare professional associations if provided\n      if (body.healthcare_professional_ids && Array.isArray(body.healthcare_professional_ids)) {\n        console.log('🔗 Managing healthcare professional associations:', body.healthcare_professional_ids);\n        \n        // Remove all existing associations for this patient\n        await supabase\n          .from('patient_healthcare_professional_associations')\n          .delete()\n          .eq('patient_id', id)\n\n        // Add new associations\n        if (body.healthcare_professional_ids.length > 0) {\n          const associations = body.healthcare_professional_ids.map((professionalId: string) => ({\n            patient_id: id,\n            healthcare_professional_id: professionalId,\n            created_by: userId\n          }))\n\n          const { error: associationError } = await supabase\n            .from('patient_healthcare_professional_associations')\n            .insert(associations)\n\n          if (associationError) {\n            console.error('❌ Association error:', associationError);\n            // Don't fail the update, just log the error\n          } else {\n            console.log('✅ Healthcare professional associations updated');\n          }\n        }\n      } else if (body.healthcare_professional_id) {\n        // Support for legacy single professional ID (backwards compatibility)\n        console.log('🔗 Managing single healthcare professional association:', body.healthcare_professional_id);\n        \n        // Remove all existing associations for this patient\n        await supabase\n          .from('patient_healthcare_professional_associations')\n          .delete()\n          .eq('patient_id', id)\n\n        // Add new association\n        const { error: associationError } = await supabase\n          .from('patient_healthcare_professional_associations')\n          .insert({\n            patient_id: id,\n            healthcare_professional_id: body.healthcare_professional_id,\n            created_by: userId\n          })\n\n        if (associationError) {\n          console.error('❌ Association error:', associationError);\n          // Don't fail the update, just log the error\n        } else {\n          console.log('✅ Healthcare professional association updated');\n        }\n      } else {\n        // If no healthcare_professional_ids provided, remove existing associations\n        console.log('🗑️ Removing existing associations');\n        await supabase\n          .from('patient_healthcare_professional_associations')\n          .delete()\n          .eq('patient_id', id)\n      }\n\n      console.log('✅ Patient updated successfully:', patient.id);\n      return createApiResponse(patient)\n    } catch (error) {\n      console.error('❌ PATCH error:', error);\n      return handleApiError(error)\n    }\n  })\n}\n\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  const { id } = await params\n  \n  return withAuth(request, async (userId, supabase) => {\n    try {\n      console.log('🗑️ DELETE Patient ID:', id, 'by user:', userId);\n\n      // Check if user has access to this patient\n      const { data: accessiblePatients, error: accessError } = await supabase\n        .rpc('get_accessible_patients', { current_user_id: userId })\n\n      if (accessError) {\n        console.error('❌ Access check error:', accessError);\n        return handleApiError(accessError)\n      }\n\n      const hasAccess = accessiblePatients?.some((p: any) => p.id === id)\n      if (!hasAccess) {\n        console.error('❌ Access denied for patient:', id, 'user:', userId);\n        return createApiResponse(null, 'Patient not found or access denied', 404)\n      }\n\n      const { error } = await supabase\n        .from('patients')\n        .delete()\n        .eq('id', id)\n\n      if (error) {\n        console.error('❌ Database delete error:', error);\n        return handleApiError(error)\n      }\n\n      console.log('✅ Patient deleted successfully:', id);\n      return createApiResponse({ success: true })\n    } catch (error) {\n      console.error('❌ DELETE error:', error);\n      return handleApiError(error)\n    }\n  })\n}\n"], "names": [], "mappings": ";;;;;;AACA;;AAMO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IAErB,OAAO,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO,QAAQ;QACtC,IAAI;YACF,+DAA+D;YAC/D,MAAM,EAAE,MAAM,kBAAkB,EAAE,KAAK,EAAE,GAAG,MAAM,SAC/C,GAAG,CAAC,2BAA2B;gBAAE,iBAAiB;YAAO;YAE5D,IAAI,OAAO;gBACT,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;YACxB;YAEA,4DAA4D;YAC5D,MAAM,UAAU,oBAAoB,KAAK,CAAC,IAAW,EAAE,EAAE,KAAK;YAE9D,IAAI,CAAC,SAAS;gBACZ,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,sCAAsC;YACvE;YAEA,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE;QAC3B,EAAE,OAAO,OAAO;YACd,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;QACxB;IACF;AACF;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IAErB,OAAO,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO,QAAQ;QACtC,IAAI;YACF,2CAA2C;YAC3C,MAAM,EAAE,MAAM,kBAAkB,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAC5D,GAAG,CAAC,2BAA2B;gBAAE,iBAAiB;YAAO;YAE5D,IAAI,aAAa;gBACf,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;YACxB;YAEA,MAAM,YAAY,oBAAoB,KAAK,CAAC,IAAW,EAAE,EAAE,KAAK;YAChE,IAAI,CAAC,WAAW;gBACd,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,sCAAsC;YACvE;YAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;YAE/B,MAAM,aAA4B;gBAChC,GAAG,IAAI;gBACP,YAAY,KAAK,UAAU,IAAI;YACjC;YAEA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,YACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;YACxB;YAEA,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE;QAC3B,EAAE,OAAO,OAAO;YACd,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;QACxB;IACF;AACF;AAEO,eAAe,MACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IAErB,OAAO,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO,QAAQ;QACtC,IAAI;YACF,QAAQ,GAAG,CAAC,wBAAwB,IAAI,YAAY;YAEpD,2CAA2C;YAC3C,MAAM,EAAE,MAAM,kBAAkB,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAC5D,GAAG,CAAC,2BAA2B;gBAAE,iBAAiB;YAAO;YAE5D,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;YACxB;YAEA,MAAM,YAAY,oBAAoB,KAAK,CAAC,IAAW,EAAE,EAAE,KAAK;YAChE,IAAI,CAAC,WAAW;gBACd,QAAQ,KAAK,CAAC,gCAAgC,IAAI,SAAS;gBAC3D,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,sCAAsC;YACvE;YAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;YAC/B,QAAQ,GAAG,CAAC,mBAAmB;YAE/B,MAAM,aAA4B;gBAChC,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK,IAAI;gBACrB,OAAO,KAAK,KAAK,IAAI;gBACrB,YAAY,KAAK,UAAU,IAAI;gBAC/B,KAAK,KAAK,GAAG,IAAI;gBACjB,SAAS,KAAK,OAAO,IAAI;gBACzB,OAAO,KAAK,KAAK,IAAI;YACvB;YAEA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,YACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;YACxB;YAEA,0DAA0D;YAC1D,IAAI,KAAK,2BAA2B,IAAI,MAAM,OAAO,CAAC,KAAK,2BAA2B,GAAG;gBACvF,QAAQ,GAAG,CAAC,qDAAqD,KAAK,2BAA2B;gBAEjG,oDAAoD;gBACpD,MAAM,SACH,IAAI,CAAC,gDACL,MAAM,GACN,EAAE,CAAC,cAAc;gBAEpB,uBAAuB;gBACvB,IAAI,KAAK,2BAA2B,CAAC,MAAM,GAAG,GAAG;oBAC/C,MAAM,eAAe,KAAK,2BAA2B,CAAC,GAAG,CAAC,CAAC,iBAA2B,CAAC;4BACrF,YAAY;4BACZ,4BAA4B;4BAC5B,YAAY;wBACd,CAAC;oBAED,MAAM,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,SACvC,IAAI,CAAC,gDACL,MAAM,CAAC;oBAEV,IAAI,kBAAkB;wBACpB,QAAQ,KAAK,CAAC,wBAAwB;oBACtC,4CAA4C;oBAC9C,OAAO;wBACL,QAAQ,GAAG,CAAC;oBACd;gBACF;YACF,OAAO,IAAI,KAAK,0BAA0B,EAAE;gBAC1C,sEAAsE;gBACtE,QAAQ,GAAG,CAAC,2DAA2D,KAAK,0BAA0B;gBAEtG,oDAAoD;gBACpD,MAAM,SACH,IAAI,CAAC,gDACL,MAAM,GACN,EAAE,CAAC,cAAc;gBAEpB,sBAAsB;gBACtB,MAAM,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,SACvC,IAAI,CAAC,gDACL,MAAM,CAAC;oBACN,YAAY;oBACZ,4BAA4B,KAAK,0BAA0B;oBAC3D,YAAY;gBACd;gBAEF,IAAI,kBAAkB;oBACpB,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,4CAA4C;gBAC9C,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;YACF,OAAO;gBACL,2EAA2E;gBAC3E,QAAQ,GAAG,CAAC;gBACZ,MAAM,SACH,IAAI,CAAC,gDACL,MAAM,GACN,EAAE,CAAC,cAAc;YACtB;YAEA,QAAQ,GAAG,CAAC,mCAAmC,QAAQ,EAAE;YACzD,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;QACxB;IACF;AACF;AAEO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IAErB,OAAO,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO,QAAQ;QACtC,IAAI;YACF,QAAQ,GAAG,CAAC,0BAA0B,IAAI,YAAY;YAEtD,2CAA2C;YAC3C,MAAM,EAAE,MAAM,kBAAkB,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAC5D,GAAG,CAAC,2BAA2B;gBAAE,iBAAiB;YAAO;YAE5D,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;YACxB;YAEA,MAAM,YAAY,oBAAoB,KAAK,CAAC,IAAW,EAAE,EAAE,KAAK;YAChE,IAAI,CAAC,WAAW;gBACd,QAAQ,KAAK,CAAC,gCAAgC,IAAI,SAAS;gBAC3D,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,sCAAsC;YACvE;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;YACxB;YAEA,QAAQ,GAAG,CAAC,mCAAmC;YAC/C,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE;gBAAE,SAAS;YAAK;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;QACxB;IACF;AACF", "debugId": null}}]}