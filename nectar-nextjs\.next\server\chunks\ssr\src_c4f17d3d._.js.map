{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface TextareaProps\r\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\r\n  ({ className, ...props }, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nTextarea.displayName = \"Textarea\"\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/date-utils.ts"], "sourcesContent": ["import { format, parseISO, isValid } from 'date-fns';\nimport { ptBR } from 'date-fns/locale';\n\n/**\n * Utility functions for Brazilian date and time formatting\n */\n\n/**\n * Format date to Brazilian format (DD/MM/YYYY)\n */\nexport function formatDateBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'dd/MM/yyyy', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format time to Brazilian format (HH:mm)\n */\nexport function formatTimeBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'HH:mm', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format date and time to Brazilian format (DD/MM/YYYY HH:mm)\n */\nexport function formatDateTimeBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'dd/MM/yyyy HH:mm', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format date to Brazilian format with day of week (Segunda, DD/MM/YYYY)\n */\nexport function formatDateWithDayBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'EEEE, dd/MM/yyyy', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format date for input fields (YYYY-MM-DD)\n */\nexport function formatDateForInput(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'yyyy-MM-dd');\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format datetime for input fields (YYYY-MM-DDTHH:mm)\n * This function handles Brazilian timezone properly to avoid UTC conversion issues\n */\nexport function formatDateTimeForInput(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n\n    // Create a new date in local timezone to avoid UTC conversion\n    const localDate = new Date(dateObj.getTime() - (dateObj.getTimezoneOffset() * 60000));\n    return format(localDate, \"yyyy-MM-dd'T'HH:mm\");\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Convert local datetime to ISO string without timezone conversion\n * This prevents the 3-hour offset issue in Brazilian timezone\n */\nexport function toLocalISOString(date: Date): string {\n  try {\n    if (!isValid(date)) return '';\n\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    const hours = String(date.getHours()).padStart(2, '0');\n    const minutes = String(date.getMinutes()).padStart(2, '0');\n\n    return `${year}-${month}-${day}T${hours}:${minutes}`;\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Get relative time in Portuguese (hoje, ontem, amanhã, etc.)\n */\nexport function getRelativeTimeBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    \n    const today = new Date();\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    \n    const dateStr = format(dateObj, 'yyyy-MM-dd');\n    const todayStr = format(today, 'yyyy-MM-dd');\n    const tomorrowStr = format(tomorrow, 'yyyy-MM-dd');\n    const yesterdayStr = format(yesterday, 'yyyy-MM-dd');\n    \n    if (dateStr === todayStr) return 'Hoje';\n    if (dateStr === tomorrowStr) return 'Amanhã';\n    if (dateStr === yesterdayStr) return 'Ontem';\n    \n    return formatDateBR(dateObj);\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Parse Brazilian date format (DD/MM/YYYY) to Date object\n */\nexport function parseBRDate(dateStr: string): Date | null {\n  try {\n    const parts = dateStr.split('/');\n    if (parts.length !== 3) return null;\n    \n    const day = parseInt(parts[0], 10);\n    const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed\n    const year = parseInt(parts[2], 10);\n    \n    const date = new Date(year, month, day);\n    if (!isValid(date)) return null;\n    \n    return date;\n  } catch {\n    return null;\n  }\n}\n\n/**\n * Get appointment status in Portuguese\n */\nexport function getAppointmentStatusBR(status: string): string {\n  const statusMap: Record<string, string> = {\n    'scheduled': 'Agendado',\n    'confirmed': 'Confirmado',\n    'in_progress': 'Em Andamento',\n    'completed': 'Concluído',\n    'cancelled': 'Cancelado',\n    'no_show': 'Faltou'\n  };\n  \n  return statusMap[status] || status;\n}\n\n/**\n * Get appointment type in Portuguese\n */\nexport function getAppointmentTypeBR(type: string): string {\n  const typeMap: Record<string, string> = {\n    'consultation': 'Consulta',\n    'return': 'Retorno',\n    'teleconsultation': 'Teleconsulta',\n    'procedure': 'Procedimento',\n    'exam': 'Exame'\n  };\n  \n  return typeMap[type] || type;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;;;AASO,SAAS,aAAa,IAAmB;IAC9C,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,cAAc;YAAE,QAAQ,iJAAA,CAAA,OAAI;QAAC;IACtD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,aAAa,IAAmB;IAC9C,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,SAAS;YAAE,QAAQ,iJAAA,CAAA,OAAI;QAAC;IACjD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,iBAAiB,IAAmB;IAClD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;YAAE,QAAQ,iJAAA,CAAA,OAAI;QAAC;IAC5D,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,oBAAoB,IAAmB;IACrD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;YAAE,QAAQ,iJAAA,CAAA,OAAI;QAAC;IAC5D,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,mBAAmB,IAAmB;IACpD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;IACzB,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAMO,SAAS,uBAAuB,IAAmB;IACxD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAE9B,8DAA8D;QAC9D,MAAM,YAAY,IAAI,KAAK,QAAQ,OAAO,KAAM,QAAQ,iBAAiB,KAAK;QAC9E,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;IAC3B,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAMO,SAAS,iBAAiB,IAAU;IACzC,IAAI;QACF,IAAI,CAAC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;QAE3B,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,QAAQ,OAAO,KAAK,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;QACtD,MAAM,MAAM,OAAO,KAAK,OAAO,IAAI,QAAQ,CAAC,GAAG;QAC/C,MAAM,QAAQ,OAAO,KAAK,QAAQ,IAAI,QAAQ,CAAC,GAAG;QAClD,MAAM,UAAU,OAAO,KAAK,UAAU,IAAI,QAAQ,CAAC,GAAG;QAEtD,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS;IACtD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,kBAAkB,IAAmB;IACnD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAE9B,MAAM,QAAQ,IAAI;QAClB,MAAM,WAAW,IAAI,KAAK;QAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;QACtC,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;QAChC,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,OAAO;QAC/B,MAAM,cAAc,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;QACrC,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;QAEvC,IAAI,YAAY,UAAU,OAAO;QACjC,IAAI,YAAY,aAAa,OAAO;QACpC,IAAI,YAAY,cAAc,OAAO;QAErC,OAAO,aAAa;IACtB,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,YAAY,OAAe;IACzC,IAAI;QACF,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAC5B,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAE/B,MAAM,MAAM,SAAS,KAAK,CAAC,EAAE,EAAE;QAC/B,MAAM,QAAQ,SAAS,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,qBAAqB;QAC/D,MAAM,OAAO,SAAS,KAAK,CAAC,EAAE,EAAE;QAEhC,MAAM,OAAO,IAAI,KAAK,MAAM,OAAO;QACnC,IAAI,CAAC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;QAE3B,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,uBAAuB,MAAc;IACnD,MAAM,YAAoC;QACxC,aAAa;QACb,aAAa;QACb,eAAe;QACf,aAAa;QACb,aAAa;QACb,WAAW;IACb;IAEA,OAAO,SAAS,CAAC,OAAO,IAAI;AAC9B;AAKO,SAAS,qBAAqB,IAAY;IAC/C,MAAM,UAAkC;QACtC,gBAAgB;QAChB,UAAU;QACV,oBAAoB;QACpB,aAAa;QACb,QAAQ;IACV;IAEA,OAAO,OAAO,CAAC,KAAK,IAAI;AAC1B", "debugId": null}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/app/dashboard/prontuario/%5BpatientId%5D/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState, useEffect } from 'react';\nimport { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport {\n  ArrowLeft,\n  User,\n  Calendar,\n  Clock,\n  FileText,\n  Save,\n  CheckCircle,\n  AlertCircle,\n  Stethoscope,\n  Phone,\n  Mail,\n  MapPin\n} from 'lucide-react';\nimport { useToast } from '@/hooks/use-toast';\nimport { formatDateBR, formatTimeBR, formatDateTimeBR } from '@/lib/date-utils';\nimport { makeAuthenticatedRequest } from '@/lib/api-client';\n\ntype Patient = {\n  id: string;\n  name: string;\n  email: string | null;\n  phone: string | null;\n  birth_date: string | null;\n  cpf: string | null;\n  address: string | null;\n  notes: string | null;\n  created_at: string;\n  updated_at: string;\n};\n\ntype Appointment = {\n  id: string;\n  title: string;\n  description: string | null;\n  patient_id: string;\n  patient_name?: string;\n  healthcare_professional_id: string | null;\n  healthcare_professional_name?: string;\n  start_time: string;\n  end_time: string;\n  type: string;\n  status: string;\n  price: number | null;\n};\n\ntype MedicalRecord = {\n  id: string;\n  appointment_id: string;\n  patient_id: string;\n  notes: string;\n  created_at: string;\n  updated_at: string | null;\n  created_by_name: string;\n  is_draft: boolean;\n};\n\nconst MedicalRecordPage = () => {\n  const params = useParams();\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const { toast } = useToast();\n  \n  const patientId = params.patientId as string;\n  const appointmentId = searchParams.get('appointment_id');\n  \n  const [patient, setPatient] = useState<Patient | null>(null);\n  const [appointment, setAppointment] = useState<Appointment | null>(null);\n  const [medicalRecords, setMedicalRecords] = useState<MedicalRecord[]>([]);\n  const [currentRecord, setCurrentRecord] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [autoSaving, setAutoSaving] = useState(false);\n  const [lastSaved, setLastSaved] = useState<Date | null>(null);\n  const [isDraft, setIsDraft] = useState(false);\n\n  useEffect(() => {\n    if (patientId) {\n      fetchPatientData();\n      if (appointmentId) {\n        fetchAppointmentData();\n        fetchMedicalRecords();\n      }\n    }\n  }, [patientId, appointmentId]);\n\n  // Auto-save functionality - 2 minutes of inactivity\n  useEffect(() => {\n    if (currentRecord.trim() && appointmentId && !saving) {\n      const autoSaveTimer = setTimeout(() => {\n        handleSaveDraft();\n      }, 120000); // Auto-save after 2 minutes of inactivity\n\n      return () => clearTimeout(autoSaveTimer);\n    }\n  }, [currentRecord, appointmentId, saving]);\n\n  const fetchPatientData = async () => {\n    try {\n      const response = await makeAuthenticatedRequest(`/api/patients/${patientId}`);\n      if (!response.ok) throw new Error('Failed to fetch patient data');\n      const result = await response.json();\n      setPatient(result.data || result);\n    } catch (error) {\n      console.error('Error fetching patient:', error);\n      toast({\n        title: \"Erro\",\n        description: \"Erro ao carregar dados do paciente.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const fetchAppointmentData = async () => {\n    if (!appointmentId) return;\n    \n    try {\n      const response = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`);\n      if (!response.ok) throw new Error('Failed to fetch appointment data');\n      const result = await response.json();\n      setAppointment(result.data || result);\n    } catch (error) {\n      console.error('Error fetching appointment:', error);\n      toast({\n        title: \"Erro\",\n        description: \"Erro ao carregar dados da consulta.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const fetchMedicalRecords = async () => {\n    if (!appointmentId) return;\n    \n    try {\n      setLoading(true);\n      const response = await makeAuthenticatedRequest(`/api/medical-records?appointment_id=${appointmentId}`);\n      if (!response.ok) throw new Error('Failed to fetch medical records');\n      const result = await response.json();\n      const records = result.data || result;\n      setMedicalRecords(Array.isArray(records) ? records : []);\n      \n      // Check if there's a draft record\n      const draftRecord = records.find((record: MedicalRecord) => record.is_draft);\n      if (draftRecord) {\n        setCurrentRecord(draftRecord.notes);\n        setIsDraft(true);\n      }\n    } catch (error) {\n      console.error('Error fetching medical records:', error);\n      setMedicalRecords([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSaveDraft = async () => {\n    if (!currentRecord.trim() || !appointmentId || saving) return;\n\n    try {\n      setAutoSaving(true);\n      const response = await makeAuthenticatedRequest('/api/medical-records', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          appointment_id: appointmentId,\n          patient_id: patientId,\n          notes: currentRecord.trim(),\n          is_draft: true\n        })\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(errorData.message || 'Failed to save draft');\n      }\n\n      setLastSaved(new Date());\n      setIsDraft(true);\n\n      toast({\n        title: \"Rascunho salvo\",\n        description: \"Suas alterações foram salvas automaticamente.\",\n      });\n    } catch (error) {\n      console.error('Error saving draft:', error);\n      toast({\n        title: \"Erro ao salvar rascunho\",\n        description: \"Não foi possível salvar o rascunho. Suas alterações podem ser perdidas.\",\n        variant: \"destructive\"\n      });\n    } finally {\n      setAutoSaving(false);\n    }\n  };\n\n  const cleanupDrafts = async (appointmentId: string) => {\n    try {\n      const response = await makeAuthenticatedRequest(`/api/medical-records/cleanup-drafts`, {\n        method: 'DELETE',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ appointment_id: appointmentId })\n      });\n\n      if (!response.ok) {\n        console.warn('Failed to cleanup drafts, but continuing with completion');\n      }\n    } catch (error) {\n      console.warn('Error cleaning up drafts:', error);\n      // Don't throw error here - draft cleanup failure shouldn't prevent completion\n    }\n  };\n\n  const handleCompleteConsultation = async () => {\n    if (!currentRecord.trim() || !appointmentId) {\n      toast({\n        title: \"Erro\",\n        description: \"Por favor, adicione suas observações antes de finalizar.\",\n        variant: \"destructive\"\n      });\n      return;\n    }\n\n    try {\n      setSaving(true);\n\n      // Save final medical record\n      const recordResponse = await makeAuthenticatedRequest('/api/medical-records', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          appointment_id: appointmentId,\n          patient_id: patientId,\n          notes: currentRecord.trim(),\n          is_draft: false\n        })\n      });\n\n      if (!recordResponse.ok) {\n        const errorData = await recordResponse.json().catch(() => ({}));\n        throw new Error(errorData.message || 'Failed to save medical record');\n      }\n\n      // Update appointment status to completed\n      const appointmentResponse = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ status: 'completed' })\n      });\n\n      if (!appointmentResponse.ok) {\n        const errorData = await appointmentResponse.json().catch(() => ({}));\n        throw new Error(errorData.message || 'Failed to update appointment status');\n      }\n\n      // Clean up draft records AFTER successful completion\n      await cleanupDrafts(appointmentId);\n\n      toast({\n        title: \"Sucesso!\",\n        description: \"Atendimento finalizado com sucesso.\",\n      });\n\n      // Navigate back to agenda\n      router.push('/dashboard/agenda');\n    } catch (error) {\n      console.error('Error completing consultation:', error);\n      toast({\n        title: \"Erro\",\n        description: error instanceof Error ? error.message : \"Erro ao finalizar atendimento.\",\n        variant: \"destructive\"\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const calculateAge = (birthDate: string | null): string => {\n    if (!birthDate) return 'Não informado';\n    \n    const birth = new Date(birthDate);\n    const today = new Date();\n    let age = today.getFullYear() - birth.getFullYear();\n    const monthDiff = today.getMonth() - birth.getMonth();\n    \n    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {\n      age--;\n    }\n    \n    return `${age} anos`;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"></div>\n          <p className=\"mt-2 text-muted-foreground\">Carregando prontuário...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto p-6 max-w-6xl\">\n      {/* Header */}\n      <div className=\"flex items-center gap-4 mb-6\">\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => router.back()}\n        >\n          <ArrowLeft className=\"h-4 w-4 mr-2\" />\n          Voltar\n        </Button>\n        <div>\n          <h1 className=\"text-2xl font-bold\">Prontuário</h1>\n          <p className=\"text-muted-foreground\">\n            {patient?.name && `Histórico médico de ${patient.name}`}\n          </p>\n        </div>\n      </div>\n\n      {/* Patient Information */}\n      {patient && (\n        <Card className=\"mb-6\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <User className=\"h-5 w-5\" />\n              Informações do Paciente\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <Label className=\"text-sm font-medium\">Nome</Label>\n                <p className=\"text-sm\">{patient.name}</p>\n              </div>\n              <div>\n                <Label className=\"text-sm font-medium\">Email</Label>\n                <p className=\"text-sm\">{patient.email || 'Não informado'}</p>\n              </div>\n              <div>\n                <Label className=\"text-sm font-medium\">Telefone</Label>\n                <p className=\"text-sm\">{patient.phone || 'Não informado'}</p>\n              </div>\n              <div>\n                <Label className=\"text-sm font-medium\">Data de Nascimento</Label>\n                <p className=\"text-sm\">\n                  {patient.birth_date ? formatDateBR(patient.birth_date) : 'Não informado'}\n                </p>\n              </div>\n              <div>\n                <Label className=\"text-sm font-medium\">Idade</Label>\n                <p className=\"text-sm\">{calculateAge(patient.birth_date)}</p>\n              </div>\n              <div>\n                <Label className=\"text-sm font-medium\">CPF</Label>\n                <p className=\"text-sm\">{patient.cpf || 'Não informado'}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Appointment Information */}\n      {appointment && (\n        <Card className=\"mb-6\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Calendar className=\"h-5 w-5\" />\n              Informações da Consulta\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              <div>\n                <Label className=\"text-sm font-medium\">Título</Label>\n                <p className=\"text-sm\">{appointment.title}</p>\n              </div>\n              <div>\n                <Label className=\"text-sm font-medium\">Data</Label>\n                <p className=\"text-sm\">{formatDateBR(appointment.start_time)}</p>\n              </div>\n              <div>\n                <Label className=\"text-sm font-medium\">Horário</Label>\n                <p className=\"text-sm\">\n                  {formatTimeBR(appointment.start_time)} - {formatTimeBR(appointment.end_time)}\n                </p>\n              </div>\n              <div>\n                <Label className=\"text-sm font-medium\">Status</Label>\n                <Badge\n                  variant={\n                    appointment.status === 'completed' ? 'secondary' :\n                    appointment.status === 'in_progress' ? 'default' :\n                    appointment.status === 'cancelled' ? 'destructive' :\n                    'outline'\n                  }\n                >\n                  {appointment.status === 'scheduled' && 'Agendado'}\n                  {appointment.status === 'confirmed' && 'Confirmado'}\n                  {appointment.status === 'completed' && 'Concluído'}\n                  {appointment.status === 'cancelled' && 'Cancelado'}\n                  {appointment.status === 'in_progress' && 'Em Andamento'}\n                </Badge>\n              </div>\n              {appointment.healthcare_professional_name && (\n                <div className=\"md:col-span-2\">\n                  <Label className=\"text-sm font-medium\">Profissional</Label>\n                  <p className=\"text-sm\">{appointment.healthcare_professional_name}</p>\n                </div>\n              )}\n              {appointment.description && (\n                <div className=\"md:col-span-2\">\n                  <Label className=\"text-sm font-medium\">Descrição</Label>\n                  <p className=\"text-sm\">{appointment.description}</p>\n                </div>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* New Medical Record Entry */}\n      {appointmentId && appointment?.status !== 'completed' && (\n        <Card className=\"mb-6\">\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <FileText className=\"h-5 w-5\" />\n                  Nova Anotação\n                </CardTitle>\n                <CardDescription>\n                  Adicione uma nova entrada ao prontuário do paciente\n                </CardDescription>\n              </div>\n              <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\n                {autoSaving && (\n                  <div className=\"flex items-center gap-1\">\n                    <div className=\"animate-spin rounded-full h-3 w-3 border-b border-primary\"></div>\n                    <span>Salvando...</span>\n                  </div>\n                )}\n                {lastSaved && !autoSaving && (\n                  <div className=\"flex items-center gap-1\">\n                    <CheckCircle className=\"h-3 w-3 text-green-500\" />\n                    <span>Salvo às {formatTimeBR(lastSaved.toISOString())}</span>\n                  </div>\n                )}\n                {isDraft && (\n                  <Badge variant=\"outline\" className=\"text-xs\">\n                    Rascunho\n                  </Badge>\n                )}\n              </div>\n            </div>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"medical-notes\">Observações Médicas</Label>\n              <Textarea\n                id=\"medical-notes\"\n                placeholder=\"Digite suas observações sobre a consulta, diagnóstico, tratamento recomendado, etc...\"\n                value={currentRecord}\n                onChange={(e) => setCurrentRecord(e.target.value)}\n                rows={8}\n                className=\"min-h-[200px]\"\n              />\n              <p className=\"text-xs text-muted-foreground\">\n                As anotações são automaticamente criptografadas e salvas como rascunho após 2 minutos de inatividade.\n              </p>\n            </div>\n\n            <div className=\"flex gap-2\">\n              <Button\n                onClick={handleSaveDraft}\n                disabled={!currentRecord.trim() || autoSaving}\n                variant=\"outline\"\n              >\n                <Save className=\"h-4 w-4 mr-2\" />\n                Salvar Rascunho\n              </Button>\n\n              <Button\n                onClick={handleCompleteConsultation}\n                disabled={!currentRecord.trim() || saving}\n                className=\"bg-green-600 hover:bg-green-700\"\n              >\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                {saving ? 'Finalizando...' : 'Finalizar Atendimento'}\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Medical Records History */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Stethoscope className=\"h-5 w-5\" />\n            Histórico de Anotações\n          </CardTitle>\n          <CardDescription>\n            {appointmentId\n              ? 'Registros médicos desta consulta'\n              : 'Histórico completo de registros médicos do paciente'\n            }\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {medicalRecords.length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              <FileText className=\"mx-auto h-12 w-12 mb-4 opacity-50\" />\n              <p>Nenhum registro médico encontrado</p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {medicalRecords\n                .filter(record => !record.is_draft) // Only show finalized records\n                .map((record) => (\n                <div key={record.id} className=\"border-l-4 border-primary pl-4 py-3 bg-muted/30 rounded-r-lg\">\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <div className=\"flex items-center gap-2\">\n                      <span className=\"text-sm font-medium\">\n                        {formatDateTimeBR(record.created_at)}\n                      </span>\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        {record.created_by_name}\n                      </Badge>\n                    </div>\n                    {record.updated_at && record.updated_at !== record.created_at && (\n                      <span className=\"text-xs text-muted-foreground\">\n                        Editado em {formatDateTimeBR(record.updated_at)}\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"prose prose-sm max-w-none\">\n                    <p className=\"text-sm whitespace-pre-wrap leading-relaxed\">\n                      {record.notes}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n\nexport default MedicalRecordPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;AA1BA;;;;;;;;;;;;;AAmEA,MAAM,oBAAoB;IACxB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,YAAY,OAAO,SAAS;IAClC,MAAM,gBAAgB,aAAa,GAAG,CAAC;IAEvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb;YACA,IAAI,eAAe;gBACjB;gBACA;YACF;QACF;IACF,GAAG;QAAC;QAAW;KAAc;IAE7B,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,IAAI,MAAM,iBAAiB,CAAC,QAAQ;YACpD,MAAM,gBAAgB,WAAW;gBAC/B;YACF,GAAG,SAAS,0CAA0C;YAEtD,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAe;QAAe;KAAO;IAEzC,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,cAAc,EAAE,WAAW;YAC5E,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,WAAW,OAAO,IAAI,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,eAAe;QAEpB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,kBAAkB,EAAE,eAAe;YACpF,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,eAAe,OAAO,IAAI,IAAI;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,eAAe;QAEpB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,oCAAoC,EAAE,eAAe;YACtG,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,UAAU,OAAO,IAAI,IAAI;YAC/B,kBAAkB,MAAM,OAAO,CAAC,WAAW,UAAU,EAAE;YAEvD,kCAAkC;YAClC,MAAM,cAAc,QAAQ,IAAI,CAAC,CAAC,SAA0B,OAAO,QAAQ;YAC3E,IAAI,aAAa;gBACf,iBAAiB,YAAY,KAAK;gBAClC,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,kBAAkB,EAAE;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,cAAc,IAAI,MAAM,CAAC,iBAAiB,QAAQ;QAEvD,IAAI;YACF,cAAc;YACd,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,2BAAwB,AAAD,EAAE,wBAAwB;gBACtE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,gBAAgB;oBAChB,YAAY;oBACZ,OAAO,cAAc,IAAI;oBACzB,UAAU;gBACZ;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;YACvC;YAEA,aAAa,IAAI;YACjB,WAAW;YAEX,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,mCAAmC,CAAC,EAAE;gBACrF,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,gBAAgB;gBAAc;YACvD;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,QAAQ,IAAI,CAAC;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,6BAA6B;QAC1C,8EAA8E;QAChF;IACF;IAEA,MAAM,6BAA6B;QACjC,IAAI,CAAC,cAAc,IAAI,MAAM,CAAC,eAAe;YAC3C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,IAAI;YACF,UAAU;YAEV,4BAA4B;YAC5B,MAAM,iBAAiB,MAAM,CAAA,GAAA,2HAAA,CAAA,2BAAwB,AAAD,EAAE,wBAAwB;gBAC5E,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,gBAAgB;oBAChB,YAAY;oBACZ,OAAO,cAAc,IAAI;oBACzB,UAAU;gBACZ;YACF;YAEA,IAAI,CAAC,eAAe,EAAE,EAAE;gBACtB,MAAM,YAAY,MAAM,eAAe,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBAC7D,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;YACvC;YAEA,yCAAyC;YACzC,MAAM,sBAAsB,MAAM,CAAA,GAAA,2HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,kBAAkB,EAAE,eAAe,EAAE;gBAC/F,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,QAAQ;gBAAY;YAC7C;YAEA,IAAI,CAAC,oBAAoB,EAAE,EAAE;gBAC3B,MAAM,YAAY,MAAM,oBAAoB,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBAClE,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;YACvC;YAEA,qDAAqD;YACrD,MAAM,cAAc;YAEpB,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,0BAA0B;YAC1B,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;gBACJ,OAAO;gBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACtD,SAAS;YACX;QACF,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,WAAW,OAAO;QAEvB,MAAM,QAAQ,IAAI,KAAK;QACvB,MAAM,QAAQ,IAAI;QAClB,IAAI,MAAM,MAAM,WAAW,KAAK,MAAM,WAAW;QACjD,MAAM,YAAY,MAAM,QAAQ,KAAK,MAAM,QAAQ;QAEnD,IAAI,YAAY,KAAM,cAAc,KAAK,MAAM,OAAO,KAAK,MAAM,OAAO,IAAK;YAC3E;QACF;QAEA,OAAO,GAAG,IAAI,KAAK,CAAC;IACtB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,OAAO,IAAI;;0CAE1B,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGxC,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC;gCAAE,WAAU;0CACV,SAAS,QAAQ,CAAC,oBAAoB,EAAE,QAAQ,IAAI,EAAE;;;;;;;;;;;;;;;;;;YAM5D,yBACC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIhC,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAW,QAAQ,IAAI;;;;;;;;;;;;8CAEtC,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAW,QAAQ,KAAK,IAAI;;;;;;;;;;;;8CAE3C,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAW,QAAQ,KAAK,IAAI;;;;;;;;;;;;8CAE3C,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDACV,QAAQ,UAAU,GAAG,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,UAAU,IAAI;;;;;;;;;;;;8CAG7D,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAW,aAAa,QAAQ,UAAU;;;;;;;;;;;;8CAEzD,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAW,QAAQ,GAAG,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQhD,6BACC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIpC,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAW,YAAY,KAAK;;;;;;;;;;;;8CAE3C,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAW,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,YAAY,UAAU;;;;;;;;;;;;8CAE7D,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;;gDACV,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,YAAY,UAAU;gDAAE;gDAAI,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,YAAY,QAAQ;;;;;;;;;;;;;8CAG/E,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC,iIAAA,CAAA,QAAK;4CACJ,SACE,YAAY,MAAM,KAAK,cAAc,cACrC,YAAY,MAAM,KAAK,gBAAgB,YACvC,YAAY,MAAM,KAAK,cAAc,gBACrC;;gDAGD,YAAY,MAAM,KAAK,eAAe;gDACtC,YAAY,MAAM,KAAK,eAAe;gDACtC,YAAY,MAAM,KAAK,eAAe;gDACtC,YAAY,MAAM,KAAK,eAAe;gDACtC,YAAY,MAAM,KAAK,iBAAiB;;;;;;;;;;;;;gCAG5C,YAAY,4BAA4B,kBACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAW,YAAY,4BAA4B;;;;;;;;;;;;gCAGnE,YAAY,WAAW,kBACtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAW,YAAY,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS1D,iBAAiB,aAAa,WAAW,6BACxC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGlC,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC;oCAAI,WAAU;;wCACZ,4BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;wCAGT,aAAa,CAAC,4BACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;;wDAAK;wDAAU,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,UAAU,WAAW;;;;;;;;;;;;;wCAGrD,yBACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;kCAOrD,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAgB;;;;;;kDAC/B,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAChD,MAAM;wCACN,WAAU;;;;;;kDAEZ,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAK/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,CAAC,cAAc,IAAI,MAAM;wCACnC,SAAQ;;0DAER,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAInC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,CAAC,cAAc,IAAI,MAAM;wCACnC,WAAU;;0DAEV,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,SAAS,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,gNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGrC,8OAAC,gIAAA,CAAA,kBAAe;0CACb,gBACG,qCACA;;;;;;;;;;;;kCAIR,8OAAC,gIAAA,CAAA,cAAW;kCACT,eAAe,MAAM,KAAK,kBACzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;8CAAE;;;;;;;;;;;iDAGL,8OAAC;4BAAI,WAAU;sCACZ,eACE,MAAM,CAAC,CAAA,SAAU,CAAC,OAAO,QAAQ,EAAE,8BAA8B;6BACjE,GAAG,CAAC,CAAC,uBACN,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,UAAU;;;;;;sEAErC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,OAAO,eAAe;;;;;;;;;;;;gDAG1B,OAAO,UAAU,IAAI,OAAO,UAAU,KAAK,OAAO,UAAU,kBAC3D,8OAAC;oDAAK,WAAU;;wDAAgC;wDAClC,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,UAAU;;;;;;;;;;;;;sDAIpD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DACV,OAAO,KAAK;;;;;;;;;;;;mCAlBT,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BnC;uCAEe", "debugId": null}}]}