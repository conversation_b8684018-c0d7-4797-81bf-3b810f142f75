{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Af/cVPLeKdxsj1l9c5WC1ajQ+ySDkOkFIp7fEyMmKb4=", "__NEXT_PREVIEW_MODE_ID": "2b04f6ec8f529774574f99822c9b8920", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7e6fe01bd9bd4bc65de3d5b5e63201883160c7bc25d0c932af7cbafbab6aaf59", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8b883d32fced0435bbeb6e7e39203045ca11facb9a7d18d4d85285dce7b70c31"}}}, "sortedMiddleware": ["/"], "functions": {}}