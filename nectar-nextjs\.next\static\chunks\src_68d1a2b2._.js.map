{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAEA;;;;;;AAEA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      ref={ref}\r\n      className={cn(\"w-full caption-bottom text-sm\", className)}\r\n      {...props}\r\n    />\r\n  </div>\r\n))\r\nTable.displayName = \"Table\"\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\r\n))\r\nTableHeader.displayName = \"TableHeader\"\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableBody.displayName = \"TableBody\"\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableFooter.displayName = \"TableFooter\"\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableRow.displayName = \"TableRow\"\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableHead.displayName = \"TableHead\"\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCell.displayName = \"TableCell\"\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCaption.displayName = \"TableCaption\"\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/tabs.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 763, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/date-utils.ts"], "sourcesContent": ["import { format, parseISO, isValid } from 'date-fns';\nimport { ptBR } from 'date-fns/locale';\n\n/**\n * Utility functions for Brazilian date and time formatting\n */\n\n/**\n * Format date to Brazilian format (DD/MM/YYYY)\n */\nexport function formatDateBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'dd/MM/yyyy', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format time to Brazilian format (HH:mm)\n */\nexport function formatTimeBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'HH:mm', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format date and time to Brazilian format (DD/MM/YYYY HH:mm)\n */\nexport function formatDateTimeBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'dd/MM/yyyy HH:mm', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format date to Brazilian format with day of week (Segunda, DD/MM/YYYY)\n */\nexport function formatDateWithDayBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'EEEE, dd/MM/yyyy', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format date for input fields (YYYY-MM-DD)\n */\nexport function formatDateForInput(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'yyyy-MM-dd');\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format datetime for input fields (YYYY-MM-DDTHH:mm)\n * This function handles Brazilian timezone properly to avoid UTC conversion issues\n */\nexport function formatDateTimeForInput(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n\n    // Create a new date in local timezone to avoid UTC conversion\n    const localDate = new Date(dateObj.getTime() - (dateObj.getTimezoneOffset() * 60000));\n    return format(localDate, \"yyyy-MM-dd'T'HH:mm\");\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Convert local datetime to ISO string without timezone conversion\n * This prevents the 3-hour offset issue in Brazilian timezone\n */\nexport function toLocalISOString(date: Date): string {\n  try {\n    if (!isValid(date)) return '';\n\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    const hours = String(date.getHours()).padStart(2, '0');\n    const minutes = String(date.getMinutes()).padStart(2, '0');\n\n    return `${year}-${month}-${day}T${hours}:${minutes}`;\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Get relative time in Portuguese (hoje, ontem, amanhã, etc.)\n */\nexport function getRelativeTimeBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    \n    const today = new Date();\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    \n    const dateStr = format(dateObj, 'yyyy-MM-dd');\n    const todayStr = format(today, 'yyyy-MM-dd');\n    const tomorrowStr = format(tomorrow, 'yyyy-MM-dd');\n    const yesterdayStr = format(yesterday, 'yyyy-MM-dd');\n    \n    if (dateStr === todayStr) return 'Hoje';\n    if (dateStr === tomorrowStr) return 'Amanhã';\n    if (dateStr === yesterdayStr) return 'Ontem';\n    \n    return formatDateBR(dateObj);\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Parse Brazilian date format (DD/MM/YYYY) to Date object\n */\nexport function parseBRDate(dateStr: string): Date | null {\n  try {\n    const parts = dateStr.split('/');\n    if (parts.length !== 3) return null;\n    \n    const day = parseInt(parts[0], 10);\n    const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed\n    const year = parseInt(parts[2], 10);\n    \n    const date = new Date(year, month, day);\n    if (!isValid(date)) return null;\n    \n    return date;\n  } catch {\n    return null;\n  }\n}\n\n/**\n * Get appointment status in Portuguese\n */\nexport function getAppointmentStatusBR(status: string): string {\n  const statusMap: Record<string, string> = {\n    'scheduled': 'Agendado',\n    'confirmed': 'Confirmado',\n    'in_progress': 'Em Andamento',\n    'completed': 'Concluído',\n    'cancelled': 'Cancelado',\n    'no_show': 'Faltou'\n  };\n  \n  return statusMap[status] || status;\n}\n\n/**\n * Get appointment type in Portuguese\n */\nexport function getAppointmentTypeBR(type: string): string {\n  const typeMap: Record<string, string> = {\n    'consultation': 'Consulta',\n    'return': 'Retorno',\n    'teleconsultation': 'Teleconsulta',\n    'procedure': 'Procedimento',\n    'exam': 'Exame'\n  };\n  \n  return typeMap[type] || type;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;;;AASO,SAAS,aAAa,IAAmB;IAC9C,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,cAAc;YAAE,QAAQ,oJAAA,CAAA,OAAI;QAAC;IACtD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,aAAa,IAAmB;IAC9C,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,SAAS;YAAE,QAAQ,oJAAA,CAAA,OAAI;QAAC;IACjD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,iBAAiB,IAAmB;IAClD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;YAAE,QAAQ,oJAAA,CAAA,OAAI;QAAC;IAC5D,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,oBAAoB,IAAmB;IACrD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;YAAE,QAAQ,oJAAA,CAAA,OAAI;QAAC;IAC5D,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,mBAAmB,IAAmB;IACpD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;IACzB,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAMO,SAAS,uBAAuB,IAAmB;IACxD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAE9B,8DAA8D;QAC9D,MAAM,YAAY,IAAI,KAAK,QAAQ,OAAO,KAAM,QAAQ,iBAAiB,KAAK;QAC9E,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;IAC3B,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAMO,SAAS,iBAAiB,IAAU;IACzC,IAAI;QACF,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;QAE3B,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,QAAQ,OAAO,KAAK,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;QACtD,MAAM,MAAM,OAAO,KAAK,OAAO,IAAI,QAAQ,CAAC,GAAG;QAC/C,MAAM,QAAQ,OAAO,KAAK,QAAQ,IAAI,QAAQ,CAAC,GAAG;QAClD,MAAM,UAAU,OAAO,KAAK,UAAU,IAAI,QAAQ,CAAC,GAAG;QAEtD,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS;IACtD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,kBAAkB,IAAmB;IACnD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAE9B,MAAM,QAAQ,IAAI;QAClB,MAAM,WAAW,IAAI,KAAK;QAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;QACtC,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;QAChC,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,OAAO;QAC/B,MAAM,cAAc,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;QACrC,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;QAEvC,IAAI,YAAY,UAAU,OAAO;QACjC,IAAI,YAAY,aAAa,OAAO;QACpC,IAAI,YAAY,cAAc,OAAO;QAErC,OAAO,aAAa;IACtB,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,YAAY,OAAe;IACzC,IAAI;QACF,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAC5B,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAE/B,MAAM,MAAM,SAAS,KAAK,CAAC,EAAE,EAAE;QAC/B,MAAM,QAAQ,SAAS,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,qBAAqB;QAC/D,MAAM,OAAO,SAAS,KAAK,CAAC,EAAE,EAAE;QAEhC,MAAM,OAAO,IAAI,KAAK,MAAM,OAAO;QACnC,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;QAE3B,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,uBAAuB,MAAc;IACnD,MAAM,YAAoC;QACxC,aAAa;QACb,aAAa;QACb,eAAe;QACf,aAAa;QACb,aAAa;QACb,WAAW;IACb;IAEA,OAAO,SAAS,CAAC,OAAO,IAAI;AAC9B;AAKO,SAAS,qBAAqB,IAAY;IAC/C,MAAM,UAAkC;QACtC,gBAAgB;QAChB,UAAU;QACV,oBAAoB;QACpB,aAAa;QACb,QAAQ;IACV;IAEA,OAAO,OAAO,CAAC,KAAK,IAAI;AAC1B", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nconst AlertDialog = AlertDialogPrimitive.Root\r\n\r\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\r\n\r\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\r\n\r\nconst AlertDialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n))\r\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\r\n\r\nconst AlertDialogContent = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPortal>\r\n    <AlertDialogOverlay />\r\n    <AlertDialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </AlertDialogPortal>\r\n))\r\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\r\n\r\nconst AlertDialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-2 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\r\n\r\nconst AlertDialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\r\n\r\nconst AlertDialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\r\n\r\nconst AlertDialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogDescription.displayName =\r\n  AlertDialogPrimitive.Description.displayName\r\n\r\nconst AlertDialogAction = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Action\r\n    ref={ref}\r\n    className={cn(buttonVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\r\n\r\nconst AlertDialogCancel = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Cancel\r\n    ref={ref}\r\n    className={cn(\r\n      buttonVariants({ variant: \"outline\" }),\r\n      \"mt-2 sm:mt-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AAEA;AACA;;;;;;AAEA,MAAM,cAAc,8KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,8KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,8KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,8KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,8KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1075, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/checkbox.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { Check } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Checkbox = React.forwardRef<\r\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <CheckboxPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <CheckboxPrimitive.Indicator\r\n      className={cn(\"flex items-center justify-center text-current\")}\r\n    >\r\n      <Check className=\"h-4 w-4\" />\r\n    </CheckboxPrimitive.Indicator>\r\n  </CheckboxPrimitive.Root>\r\n))\r\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\r\n\r\nexport { Checkbox }\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/UserAssociationsManager.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState, useEffect } from 'react'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { useToast } from '@/hooks/use-toast'\nimport { \n  Users, \n  UserPlus, \n  Trash2, \n  Settings, \n  Eye,\n  AlertCircle,\n  Link,\n  Unlink\n} from 'lucide-react'\nimport { makeAuthenticatedRequest } from '@/lib/api-client'\nimport { formatDateTimeBR } from '@/lib/date-utils'\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\n\ntype UserPermission = 'read' | 'create' | 'update' | 'delete'\n\nconst AVAILABLE_PERMISSIONS: Record<string, UserPermission[]> = {\n  patients: ['read', 'create', 'update', 'delete'],\n  appointments: ['read', 'create', 'update', 'delete'],\n  healthcare_professionals: ['read', 'create', 'update', 'delete'],\n  clinic_settings: ['read', 'create', 'update', 'delete'],\n  messages: ['read', 'create', 'update', 'delete'],\n  campaigns: ['read', 'create', 'update', 'delete']\n}\n\ninterface User {\n  id: string\n  email: string\n  name: string\n  role: string\n  is_active: boolean\n}\n\ninterface UserAssociation {\n  id: string\n  accessor_user_id: string\n  target_user_id: string\n  association_type: 'secretary_to_doctor' | 'doctor_to_doctor'\n  permissions?: Record<string, string[]> | null\n  is_active: boolean\n  created_by: string\n  created_at: string\n  accessor_user: User\n  target_user: User\n  created_by_user: User\n}\n\ninterface CreateAssociationForm {\n  accessor_user_id: string\n  target_user_id: string\n  association_type: 'secretary_to_doctor' | 'doctor_to_doctor'\n  permissions: Record<string, string[]>\n}\n\nconst defaultPermissions: Record<string, UserPermission[]> = {\n  patients: ['read', 'create', 'update', 'delete'],\n  appointments: ['read', 'create', 'update', 'delete'],\n  healthcare_professionals: ['read', 'create', 'update', 'delete'],\n  clinic_settings: ['read', 'create', 'update', 'delete'],\n  messages: ['read', 'create', 'update', 'delete'],\n  campaigns: ['read', 'create', 'update', 'delete']\n};\n\nexport function UserAssociationsManager() {\n  const [associations, setAssociations] = useState<UserAssociation[]>([])\n  const [users, setUsers] = useState<User[]>([])\n  const [loading, setLoading] = useState(true)\n  const [isDialogOpen, setIsDialogOpen] = useState(false)\n  const [selectedAssociation, setSelectedAssociation] = useState<UserAssociation | null>(null)\n  const { toast } = useToast()\n\n  const [form, setForm] = useState<CreateAssociationForm>({\n    accessor_user_id: '',\n    target_user_id: '',\n    association_type: 'secretary_to_doctor',\n    permissions: {}\n  })\n\n  useEffect(() => {\n    fetchData()\n  }, [])\n\n  const fetchData = async () => {\n    try {\n      setLoading(true)\n      await Promise.all([\n        fetchAssociations(),\n        fetchUsers()\n      ])\n    } catch (error) {\n      console.error('Error fetching data:', error)\n      toast({\n        title: \"Erro\",\n        description: \"Erro ao carregar dados das associações.\",\n        variant: \"destructive\"\n      })\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const fetchAssociations = async () => {\n    try {\n      const response = await makeAuthenticatedRequest('/api/user-associations')\n      if (response.ok) {\n        const result = await response.json()\n        setAssociations(result.data || [])\n      }\n    } catch (error) {\n      console.error('Error fetching associations:', error)\n    }\n  }\n\n  const fetchUsers = async () => {\n    try {\n      const response = await makeAuthenticatedRequest('/api/admin/users')\n      if (response.ok) {\n        const result = await response.json()\n        setUsers(result.data || [])\n      }\n    } catch (error) {\n      console.error('Error fetching users:', error)\n    }\n  }\n\n  const handleCreateAssociation = async () => {\n    try {\n      if (!form.accessor_user_id || !form.target_user_id) {\n        toast({\n          title: \"Erro\",\n          description: \"Selecione ambos os usuários para criar a associação.\",\n          variant: \"destructive\"\n        })\n        return\n      }\n\n      if (form.accessor_user_id === form.target_user_id) {\n        toast({\n          title: \"Erro\",\n          description: \"Um usuário não pode ser associado a si mesmo.\",\n          variant: \"destructive\"\n        })\n        return\n      }\n\n      const response = await makeAuthenticatedRequest('/api/user-associations', {\n        method: 'POST',\n        body: JSON.stringify(form)\n      })\n\n      if (response.ok) {\n        toast({\n          title: \"Sucesso\",\n          description: \"Associação criada com sucesso.\",\n        })\n        setIsDialogOpen(false)\n        resetForm()\n        fetchAssociations()\n      } else {\n        const error = await response.json()\n        throw new Error(error.message || 'Failed to create association')\n      }\n    } catch (error) {\n      console.error('Error creating association:', error)\n      toast({\n        title: \"Erro\",\n        description: error instanceof Error ? error.message : \"Erro ao criar associação.\",\n        variant: \"destructive\"\n      })\n    }\n  }\n\n  const handleDeleteAssociation = async (associationId: string) => {\n    try {\n      const response = await makeAuthenticatedRequest(\n        `/api/user-associations?id=${associationId}`,\n        { method: 'DELETE' }\n      )\n\n      if (response.ok) {\n        toast({\n          title: \"Sucesso\",\n          description: \"Associação removida com sucesso.\",\n        })\n        fetchAssociations()\n      } else {\n        throw new Error('Failed to delete association')\n      }\n    } catch (error) {\n      console.error('Error deleting association:', error)\n      toast({\n        title: \"Erro\",\n        description: \"Erro ao remover associação.\",\n        variant: \"destructive\"\n      })\n    }\n  }\n\n  const resetForm = () => {\n    setForm({\n      accessor_user_id: '',\n      target_user_id: '',\n      association_type: 'secretary_to_doctor',\n      permissions: {}\n    })\n  }\n\n  const handlePermissionChange = (resource: string, action: string, checked: boolean) => {\n    setForm(prev => {\n      const newPermissions = { ...prev.permissions }\n      \n      if (!newPermissions[resource]) {\n        newPermissions[resource] = []\n      }\n      \n      if (checked) {\n        if (!newPermissions[resource].includes(action)) {\n          newPermissions[resource].push(action)\n        }\n      } else {\n        newPermissions[resource] = newPermissions[resource].filter(a => a !== action)\n        if (newPermissions[resource].length === 0) {\n          delete newPermissions[resource]\n        }\n      }\n      \n      return { ...prev, permissions: newPermissions }\n    })\n  }\n\n  const getAssociationTypeLabel = (type: string) => {\n    switch (type) {\n      case 'secretary_to_doctor':\n        return 'Secretária → Médico'\n      case 'doctor_to_doctor':\n        return 'Médico → Médico'\n      default:\n        return type\n    }\n  }\n\n  const getFilteredUsers = (role?: string) => {\n    return users.filter(user => user.is_active && (!role || user.role === role))\n  }\n\n  if (loading) {\n    return (\n      <Card>\n        <CardHeader>\n          <CardTitle>Carregando...</CardTitle>\n        </CardHeader>\n      </Card>\n    )\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <div className=\"flex justify-between items-center\">\n          <div>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Link className=\"h-5 w-5\" />\n              Associações de Usuários\n            </CardTitle>\n            <CardDescription>\n              Gerencie o acesso entre usuários do sistema\n            </CardDescription>\n          </div>\n          \n          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>\n            <DialogTrigger asChild>\n              <Button>\n                <UserPlus className=\"h-4 w-4 mr-2\" />\n                Nova Associação\n              </Button>\n            </DialogTrigger>\n            <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n              <DialogHeader>\n                <DialogTitle>Criar Nova Associação</DialogTitle>\n                <DialogDescription>\n                  Associe usuários para permitir acesso compartilhado aos dados\n                </DialogDescription>\n              </DialogHeader>\n\n              <div className=\"space-y-4 py-4\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <Label htmlFor=\"association_type\">Tipo de Associação</Label>\n                    <Select \n                      value={form.association_type} \n                      onValueChange={(value: 'secretary_to_doctor' | 'doctor_to_doctor') => \n                        setForm({ ...form, association_type: value })\n                      }\n                    >\n                      <SelectTrigger>\n                        <SelectValue />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"secretary_to_doctor\">Secretária → Médico</SelectItem>\n                        <SelectItem value=\"doctor_to_doctor\">Médico → Médico</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <Label htmlFor=\"accessor_user\">\n                      {form.association_type === 'secretary_to_doctor' ? 'Secretária' : 'Médico (Acessor)'}\n                    </Label>\n                    <Select \n                      value={form.accessor_user_id} \n                      onValueChange={(value) => setForm({ ...form, accessor_user_id: value })}\n                    >\n                      <SelectTrigger>\n                        <SelectValue placeholder=\"Selecione o usuário\" />\n                      </SelectTrigger>\n                      <SelectContent>\n                        {getFilteredUsers(\n                          form.association_type === 'secretary_to_doctor' ? 'secretary' : 'healthcare_professional'\n                        ).map(user => (\n                          <SelectItem key={user.id} value={user.id}>\n                            {user.name} ({user.email})\n                          </SelectItem>\n                        ))}\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"target_user\">Médico (Alvo)</Label>\n                    <Select \n                      value={form.target_user_id} \n                      onValueChange={(value) => setForm({ ...form, target_user_id: value })}\n                    >\n                      <SelectTrigger>\n                        <SelectValue placeholder=\"Selecione o médico\" />\n                      </SelectTrigger>\n                      <SelectContent>\n                        {getFilteredUsers('healthcare_professional').map(user => (\n                          <SelectItem key={user.id} value={user.id}>\n                            {user.name} ({user.email})\n                          </SelectItem>\n                        ))}\n                      </SelectContent>\n                    </Select>\n                  </div>\n                </div>\n\n                <div>\n                  <Label>Permissões Específicas (opcional)</Label>\n                  <p className=\"text-sm text-muted-foreground mb-3\">\n                    Se não especificadas, serão usadas as permissões padrão do role\n                  </p>\n                  \n                  <div className=\"space-y-2 max-h-60 overflow-y-auto\">\n                    {Object.entries(AVAILABLE_PERMISSIONS).map(([resource, actions]) => (\n                      <div key={resource} className=\"border rounded p-2\">\n                        <h4 className=\"font-medium mb-1 text-sm capitalize\">{resource.replace('_', ' ')}</h4>\n                        <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-1\">\n                          {actions.map(action => (\n                            <div key={action} className=\"flex items-center space-x-1\">\n                              <Checkbox\n                                id={`${resource}-${action}`}\n                                checked={form.permissions[resource]?.includes(action) || false}\n                                onCheckedChange={(checked) =>\n                                  handlePermissionChange(resource, action, checked as boolean)\n                                }\n                              />\n                              <Label\n                                htmlFor={`${resource}-${action}`}\n                                className=\"text-xs capitalize\"\n                              >\n                                {action}\n                              </Label>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              <DialogFooter>\n                <Button variant=\"outline\" onClick={() => setIsDialogOpen(false)}>\n                  Cancelar\n                </Button>\n                <Button onClick={handleCreateAssociation}>\n                  Criar Associação\n                </Button>\n              </DialogFooter>\n            </DialogContent>\n          </Dialog>\n        </div>\n      </CardHeader>\n      \n      <CardContent>\n        {associations.length === 0 ? (\n          <div className=\"text-center py-8 text-muted-foreground\">\n            <Users className=\"mx-auto h-12 w-12 mb-4 opacity-50\" />\n            <p>Nenhuma associação configurada</p>\n            <p className=\"text-sm mt-2\">\n              Crie associações para permitir que usuários acessem dados de outros usuários\n            </p>\n          </div>\n        ) : (\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>Usuário Acessor</TableHead>\n                <TableHead>Usuário Alvo</TableHead>\n                <TableHead>Tipo</TableHead>\n                <TableHead>Permissões</TableHead>\n                <TableHead>Criado em</TableHead>\n                <TableHead>Ações</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {associations.map((association) => (\n                <TableRow key={association.id}>\n                  <TableCell>\n                    <div>\n                      <div className=\"font-medium\">{association.accessor_user.name}</div>\n                      <div className=\"text-sm text-muted-foreground\">\n                        {association.accessor_user.email}\n                      </div>\n                      <Badge variant=\"outline\" className=\"text-xs mt-1\">\n                        {association.accessor_user.role}\n                      </Badge>\n                    </div>\n                  </TableCell>\n                  <TableCell>\n                    <div>\n                      <div className=\"font-medium\">{association.target_user.name}</div>\n                      <div className=\"text-sm text-muted-foreground\">\n                        {association.target_user.email}\n                      </div>\n                      <Badge variant=\"outline\" className=\"text-xs mt-1\">\n                        {association.target_user.role}\n                      </Badge>\n                    </div>\n                  </TableCell>\n                  <TableCell>\n                    <Badge variant=\"secondary\">\n                      {getAssociationTypeLabel(association.association_type)}\n                    </Badge>\n                  </TableCell>\n                  <TableCell>\n                    {association.permissions ? (\n                      <div className=\"space-y-1\">\n                        {Object.entries(association.permissions).map(([resource, actions]) => (\n                          <div key={resource} className=\"text-xs\">\n                            <span className=\"font-medium\">{resource}:</span> {actions.join(', ')}\n                          </div>\n                        ))}\n                      </div>\n                    ) : (\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        Permissões padrão do role\n                      </Badge>\n                    )}\n                  </TableCell>\n                  <TableCell className=\"text-sm\">\n                    {formatDateTimeBR(association.created_at)}\n                  </TableCell>\n                  <TableCell>\n                    <AlertDialog>\n                      <AlertDialogTrigger asChild>\n                        <Button size=\"sm\" variant=\"destructive\">\n                          <Trash2 className=\"h-3 w-3\" />\n                        </Button>\n                      </AlertDialogTrigger>\n                      <AlertDialogContent>\n                        <AlertDialogHeader>\n                          <AlertDialogTitle>Remover Associação</AlertDialogTitle>\n                          <AlertDialogDescription>\n                            Tem certeza que deseja remover a associação entre{' '}\n                            <strong>{association.accessor_user.name}</strong> e{' '}\n                            <strong>{association.target_user.name}</strong>?\n                            Esta ação não pode ser desfeita.\n                          </AlertDialogDescription>\n                        </AlertDialogHeader>\n                        <AlertDialogFooter>\n                          <AlertDialogCancel>Cancelar</AlertDialogCancel>\n                          <AlertDialogAction\n                            onClick={() => handleDeleteAssociation(association.id)}\n                            className=\"bg-destructive text-destructive-foreground hover:bg-destructive/90\"\n                          >\n                            Remover\n                          </AlertDialogAction>\n                        </AlertDialogFooter>\n                      </AlertDialogContent>\n                    </AlertDialog>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAxBA;;;;;;;;;;;;;;;AA4BA,MAAM,wBAA0D;IAC9D,UAAU;QAAC;QAAQ;QAAU;QAAU;KAAS;IAChD,cAAc;QAAC;QAAQ;QAAU;QAAU;KAAS;IACpD,0BAA0B;QAAC;QAAQ;QAAU;QAAU;KAAS;IAChE,iBAAiB;QAAC;QAAQ;QAAU;QAAU;KAAS;IACvD,UAAU;QAAC;QAAQ;QAAU;QAAU;KAAS;IAChD,WAAW;QAAC;QAAQ;QAAU;QAAU;KAAS;AACnD;AA+BA,MAAM,qBAAuD;IAC3D,UAAU;QAAC;QAAQ;QAAU;QAAU;KAAS;IAChD,cAAc;QAAC;QAAQ;QAAU;QAAU;KAAS;IACpD,0BAA0B;QAAC;QAAQ;QAAU;QAAU;KAAS;IAChE,iBAAiB;QAAC;QAAQ;QAAU;QAAU;KAAS;IACvD,UAAU;QAAC;QAAQ;QAAU;QAAU;KAAS;IAChD,WAAW;QAAC;QAAQ;QAAU;QAAU;KAAS;AACnD;AAEO,SAAS;;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACvF,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;QACtD,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;QAClB,aAAa,CAAC;IAChB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR;QACF;4CAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,MAAM,QAAQ,GAAG,CAAC;gBAChB;gBACA;aACD;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,gBAAgB,OAAO,IAAI,IAAI,EAAE;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,SAAS,OAAO,IAAI,IAAI,EAAE;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,0BAA0B;QAC9B,IAAI;YACF,IAAI,CAAC,KAAK,gBAAgB,IAAI,CAAC,KAAK,cAAc,EAAE;gBAClD,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;gBACA;YACF;YAEA,IAAI,KAAK,gBAAgB,KAAK,KAAK,cAAc,EAAE;gBACjD,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;gBACA;YACF;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,0BAA0B;gBACxE,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA,gBAAgB;gBAChB;gBACA;YACF,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;gBACJ,OAAO;gBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACtD,SAAS;YACX;QACF;IACF;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAC5C,CAAC,0BAA0B,EAAE,eAAe,EAC5C;gBAAE,QAAQ;YAAS;YAGrB,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,YAAY;QAChB,QAAQ;YACN,kBAAkB;YAClB,gBAAgB;YAChB,kBAAkB;YAClB,aAAa,CAAC;QAChB;IACF;IAEA,MAAM,yBAAyB,CAAC,UAAkB,QAAgB;QAChE,QAAQ,CAAA;YACN,MAAM,iBAAiB;gBAAE,GAAG,KAAK,WAAW;YAAC;YAE7C,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE;gBAC7B,cAAc,CAAC,SAAS,GAAG,EAAE;YAC/B;YAEA,IAAI,SAAS;gBACX,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS;oBAC9C,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC;gBAChC;YACF,OAAO;gBACL,cAAc,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;gBACtE,IAAI,cAAc,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG;oBACzC,OAAO,cAAc,CAAC,SAAS;gBACjC;YACF;YAEA,OAAO;gBAAE,GAAG,IAAI;gBAAE,aAAa;YAAe;QAChD;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,IAAI,CAAC,CAAC,QAAQ,KAAK,IAAI,KAAK,IAAI;IAC5E;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;8BAAC;;;;;;;;;;;;;;;;IAInB;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG9B,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAKnB,6LAAC,qIAAA,CAAA,SAAM;4BAAC,MAAM;4BAAc,cAAc;;8CACxC,6LAAC,qIAAA,CAAA,gBAAa;oCAAC,OAAO;8CACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;;0DACL,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAIzC,6LAAC,qIAAA,CAAA,gBAAa;oCAAC,WAAU;;sDACvB,6LAAC,qIAAA,CAAA,eAAY;;8DACX,6LAAC,qIAAA,CAAA,cAAW;8DAAC;;;;;;8DACb,6LAAC,qIAAA,CAAA,oBAAiB;8DAAC;;;;;;;;;;;;sDAKrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAmB;;;;;;0EAClC,6LAAC,qIAAA,CAAA,SAAM;gEACL,OAAO,KAAK,gBAAgB;gEAC5B,eAAe,CAAC,QACd,QAAQ;wEAAE,GAAG,IAAI;wEAAE,kBAAkB;oEAAM;;kFAG7C,6LAAC,qIAAA,CAAA,gBAAa;kFACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,6LAAC,qIAAA,CAAA,gBAAa;;0FACZ,6LAAC,qIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAsB;;;;;;0FACxC,6LAAC,qIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAM7C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;8EACZ,KAAK,gBAAgB,KAAK,wBAAwB,eAAe;;;;;;8EAEpE,6LAAC,qIAAA,CAAA,SAAM;oEACL,OAAO,KAAK,gBAAgB;oEAC5B,eAAe,CAAC,QAAU,QAAQ;4EAAE,GAAG,IAAI;4EAAE,kBAAkB;wEAAM;;sFAErE,6LAAC,qIAAA,CAAA,gBAAa;sFACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;sFAE3B,6LAAC,qIAAA,CAAA,gBAAa;sFACX,iBACC,KAAK,gBAAgB,KAAK,wBAAwB,cAAc,2BAChE,GAAG,CAAC,CAAA,qBACJ,6LAAC,qIAAA,CAAA,aAAU;oFAAe,OAAO,KAAK,EAAE;;wFACrC,KAAK,IAAI;wFAAC;wFAAG,KAAK,KAAK;wFAAC;;mFADV,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;sEAQhC,6LAAC;;8EACC,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAc;;;;;;8EAC7B,6LAAC,qIAAA,CAAA,SAAM;oEACL,OAAO,KAAK,cAAc;oEAC1B,eAAe,CAAC,QAAU,QAAQ;4EAAE,GAAG,IAAI;4EAAE,gBAAgB;wEAAM;;sFAEnE,6LAAC,qIAAA,CAAA,gBAAa;sFACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;sFAE3B,6LAAC,qIAAA,CAAA,gBAAa;sFACX,iBAAiB,2BAA2B,GAAG,CAAC,CAAA,qBAC/C,6LAAC,qIAAA,CAAA,aAAU;oFAAe,OAAO,KAAK,EAAE;;wFACrC,KAAK,IAAI;wFAAC;wFAAG,KAAK,KAAK;wFAAC;;mFADV,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DASlC,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,6LAAC;4DAAE,WAAU;sEAAqC;;;;;;sEAIlD,6LAAC;4DAAI,WAAU;sEACZ,OAAO,OAAO,CAAC,uBAAuB,GAAG,CAAC,CAAC,CAAC,UAAU,QAAQ,iBAC7D,6LAAC;oEAAmB,WAAU;;sFAC5B,6LAAC;4EAAG,WAAU;sFAAuC,SAAS,OAAO,CAAC,KAAK;;;;;;sFAC3E,6LAAC;4EAAI,WAAU;sFACZ,QAAQ,GAAG,CAAC,CAAA,uBACX,6LAAC;oFAAiB,WAAU;;sGAC1B,6LAAC,uIAAA,CAAA,WAAQ;4FACP,IAAI,GAAG,SAAS,CAAC,EAAE,QAAQ;4FAC3B,SAAS,KAAK,WAAW,CAAC,SAAS,EAAE,SAAS,WAAW;4FACzD,iBAAiB,CAAC,UAChB,uBAAuB,UAAU,QAAQ;;;;;;sGAG7C,6LAAC,oIAAA,CAAA,QAAK;4FACJ,SAAS,GAAG,SAAS,CAAC,EAAE,QAAQ;4FAChC,WAAU;sGAET;;;;;;;mFAZK;;;;;;;;;;;mEAJN;;;;;;;;;;;;;;;;;;;;;;sDA2BlB,6LAAC,qIAAA,CAAA,eAAY;;8DACX,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,SAAS,IAAM,gBAAgB;8DAAQ;;;;;;8DAGjE,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAS;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpD,6LAAC,mIAAA,CAAA,cAAW;0BACT,aAAa,MAAM,KAAK,kBACvB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC;sCAAE;;;;;;sCACH,6LAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;yCAK9B,6LAAC,oIAAA,CAAA,QAAK;;sCACJ,6LAAC,oIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;kDACP,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;;;;;;;sCAGf,6LAAC,oIAAA,CAAA,YAAS;sCACP,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC,oIAAA,CAAA,WAAQ;;sDACP,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAe,YAAY,aAAa,CAAC,IAAI;;;;;;kEAC5D,6LAAC;wDAAI,WAAU;kEACZ,YAAY,aAAa,CAAC,KAAK;;;;;;kEAElC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAChC,YAAY,aAAa,CAAC,IAAI;;;;;;;;;;;;;;;;;sDAIrC,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAe,YAAY,WAAW,CAAC,IAAI;;;;;;kEAC1D,6LAAC;wDAAI,WAAU;kEACZ,YAAY,WAAW,CAAC,KAAK;;;;;;kEAEhC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAChC,YAAY,WAAW,CAAC,IAAI;;;;;;;;;;;;;;;;;sDAInC,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DACZ,wBAAwB,YAAY,gBAAgB;;;;;;;;;;;sDAGzD,6LAAC,oIAAA,CAAA,YAAS;sDACP,YAAY,WAAW,iBACtB,6LAAC;gDAAI,WAAU;0DACZ,OAAO,OAAO,CAAC,YAAY,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,QAAQ,iBAC/D,6LAAC;wDAAmB,WAAU;;0EAC5B,6LAAC;gEAAK,WAAU;;oEAAe;oEAAS;;;;;;;4DAAQ;4DAAE,QAAQ,IAAI,CAAC;;uDADvD;;;;;;;;;qEAMd,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAAU;;;;;;;;;;;sDAKjD,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY,UAAU;;;;;;sDAE1C,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,8IAAA,CAAA,cAAW;;kEACV,6LAAC,8IAAA,CAAA,qBAAkB;wDAAC,OAAO;kEACzB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,SAAQ;sEACxB,cAAA,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAGtB,6LAAC,8IAAA,CAAA,qBAAkB;;0EACjB,6LAAC,8IAAA,CAAA,oBAAiB;;kFAChB,6LAAC,8IAAA,CAAA,mBAAgB;kFAAC;;;;;;kFAClB,6LAAC,8IAAA,CAAA,yBAAsB;;4EAAC;4EAC4B;0FAClD,6LAAC;0FAAQ,YAAY,aAAa,CAAC,IAAI;;;;;;4EAAU;4EAAG;0FACpD,6LAAC;0FAAQ,YAAY,WAAW,CAAC,IAAI;;;;;;4EAAU;;;;;;;;;;;;;0EAInD,6LAAC,8IAAA,CAAA,oBAAiB;;kFAChB,6LAAC,8IAAA,CAAA,oBAAiB;kFAAC;;;;;;kFACnB,6LAAC,8IAAA,CAAA,oBAAiB;wEAChB,SAAS,IAAM,wBAAwB,YAAY,EAAE;wEACrD,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCApEI,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmF7C;GAxbgB;;QAMI,+HAAA,CAAA,WAAQ;;;KANZ", "debugId": null}}, {"offset": {"line": 2252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/app/dashboard/admin/usuarios/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { useToast } from '@/hooks/use-toast'\nimport { makeAuthenticatedRequest } from '@/lib/api-client'\nimport { <PERSON>, UserPlus, Eye, Link } from 'lucide-react'\nimport { UserAssociationsManager } from '@/components/UserAssociationsManager'\n\ninterface User {\n  id: string\n  email: string\n  name: string | null\n  phone: string | null\n  role: string\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\n// UserRole interface removed - using simplified role system from users.role\n\ninterface Permission {\n  id: string\n  role_name: string\n  resource: string\n  action: string\n}\n\nexport default function UsersAdminPage() {\n  const [users, setUsers] = useState<User[]>([])\n  // Removed userRoles state - using simplified role system\n  const [permissions, setPermissions] = useState<Permission[]>([])\n  const [loading, setLoading] = useState(true)\n  const [selectedUser, setSelectedUser] = useState<User | null>(null)\n  const [isDialogOpen, setIsDialogOpen] = useState(false)\n  const [activeTab, setActiveTab] = useState('users')\n  const { toast } = useToast()\n\n  // Form states\n  const [userForm, setUserForm] = useState({\n    email: '',\n    name: '',\n    phone: '',\n    password: '',\n    role: 'secretary',\n    is_active: true\n  })\n\n  // Removed roleForm - using simplified role system from users.role\n\n  useEffect(() => {\n    fetchData()\n  }, [])\n\n  const fetchData = async () => {\n    try {\n      setLoading(true)\n      await Promise.all([\n        fetchUsers(),\n        fetchPermissions()\n      ])\n    } catch (error) {\n      console.error('Error fetching data:', error)\n      toast({\n        title: \"Erro ao carregar dados\",\n        description: \"Não foi possível carregar os dados dos usuários.\",\n        variant: \"destructive\",\n      })\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const fetchUsers = async () => {\n    try {\n      const response = await makeAuthenticatedRequest('/api/admin/users')\n      if (response.ok) {\n        const result = await response.json()\n        setUsers(result.data || [])\n      }\n    } catch (error) {\n      console.error('Error fetching users:', error)\n    }\n  }\n\n  // Removed fetchUserRoles - using simplified role system\n\n  const fetchPermissions = async () => {\n    try {\n      const response = await makeAuthenticatedRequest('/api/permissions')\n      if (response.ok) {\n        const result = await response.json()\n        setPermissions(result.data || [])\n      }\n    } catch (error) {\n      console.error('Error fetching permissions:', error)\n    }\n  }\n\n  const handleCreateUser = async () => {\n    try {\n      const response = await makeAuthenticatedRequest('/api/admin/users', {\n        method: 'POST',\n        body: JSON.stringify(userForm)\n      })\n\n      if (response.ok) {\n        toast({\n          title: \"Usuário criado\",\n          description: \"Usuário criado com sucesso.\",\n        })\n        setIsDialogOpen(false)\n        setUserForm({\n          email: '',\n          name: '',\n          phone: '',\n          password: '',\n          role: 'secretary',\n          is_active: true\n        })\n        fetchUsers()\n      } else {\n        throw new Error('Failed to create user')\n      }\n    } catch (error) {\n      console.error('Error creating user:', error)\n      toast({\n        title: \"Erro ao criar usuário\",\n        description: \"Não foi possível criar o usuário.\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  // Removed handleAssignRole - using simplified role system\n  // Role assignment is now done directly in user creation\n\n  const handleToggleUserStatus = async (userId: string, isActive: boolean) => {\n    try {\n      const response = await makeAuthenticatedRequest(`/api/admin/users/${userId}`, {\n        method: 'PATCH',\n        body: JSON.stringify({ is_active: !isActive })\n      })\n\n      if (response.ok) {\n        toast({\n          title: \"Status atualizado\",\n          description: `Usuário ${!isActive ? 'ativado' : 'desativado'} com sucesso.`,\n        })\n        fetchUsers()\n      } else {\n        throw new Error('Failed to update user status')\n      }\n    } catch (error) {\n      console.error('Error updating user status:', error)\n      toast({\n        title: \"Erro ao atualizar status\",\n        description: \"Não foi possível atualizar o status do usuário.\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  const getUserRoles = (userId: string) => {\n    // Using simplified role system - get role directly from user\n    const user = users.find(u => u.id === userId)\n    return user?.role ? [user.role] : []\n  }\n\n  const getRolePermissions = (roleName: string) => {\n    return permissions.filter(p => p.role_name === roleName)\n  }\n\n  const availableRoles = ['admin', 'healthcare_professional', 'secretary']\n\n  if (loading) {\n    return (\n      <div className=\"container mx-auto p-6\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"></div>\n            <p className=\"mt-2 text-muted-foreground\">Carregando...</p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"container mx-auto p-6 space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold\">Administração de Usuários</h1>\n          <p className=\"text-muted-foreground\">\n            Gerencie usuários, roles e permissões do sistema\n          </p>\n        </div>\n        \n        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>\n          <DialogTrigger asChild>\n            <Button>\n              <UserPlus className=\"mr-2 h-4 w-4\" />\n              Novo Usuário\n            </Button>\n          </DialogTrigger>\n          <DialogContent>\n            <DialogHeader>\n              <DialogTitle>Criar Novo Usuário</DialogTitle>\n              <DialogDescription>\n                Adicione um novo usuário ao sistema\n              </DialogDescription>\n            </DialogHeader>\n            <div className=\"space-y-4\">\n              <div>\n                <Label htmlFor=\"email\">E-mail *</Label>\n                <Input\n                  id=\"email\"\n                  type=\"email\"\n                  value={userForm.email}\n                  onChange={(e) => setUserForm({ ...userForm, email: e.target.value })}\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"name\">Nome</Label>\n                <Input\n                  id=\"name\"\n                  value={userForm.name}\n                  onChange={(e) => setUserForm({ ...userForm, name: e.target.value })}\n                  placeholder=\"Nome completo\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"phone\">Telefone</Label>\n                <Input\n                  id=\"phone\"\n                  value={userForm.phone}\n                  onChange={(e) => setUserForm({ ...userForm, phone: e.target.value })}\n                  placeholder=\"(11) 99999-9999\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"password\">Senha Inicial *</Label>\n                <Input\n                  id=\"password\"\n                  type=\"password\"\n                  value={userForm.password}\n                  onChange={(e) => setUserForm({ ...userForm, password: e.target.value })}\n                  placeholder=\"Senha temporária para primeiro acesso\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"role\">Role Inicial</Label>\n                <Select value={userForm.role} onValueChange={(value) => setUserForm({ ...userForm, role: value })}>\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {availableRoles.map(role => (\n                      <SelectItem key={role} value={role}>\n                        {role.charAt(0).toUpperCase() + role.slice(1)}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n            <DialogFooter>\n              <Button variant=\"outline\" onClick={() => setIsDialogOpen(false)}>\n                Cancelar\n              </Button>\n              <Button onClick={handleCreateUser}>\n                Criar Usuário\n              </Button>\n            </DialogFooter>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      <Tabs value={activeTab} onValueChange={setActiveTab}>\n        <TabsList className=\"grid w-full grid-cols-2\">\n          <TabsTrigger value=\"users\" className=\"flex items-center gap-2\">\n            <Users className=\"h-4 w-4\" />\n            Usuários\n          </TabsTrigger>\n          <TabsTrigger value=\"associations\" className=\"flex items-center gap-2\">\n            <Link className=\"h-4 w-4\" />\n            Associações\n          </TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"users\" className=\"space-y-4\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Usuários do Sistema</CardTitle>\n              <CardDescription>\n                Lista de todos os usuários cadastrados no sistema\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>Nome</TableHead>\n                    <TableHead>E-mail</TableHead>\n                    <TableHead>Telefone</TableHead>\n                    <TableHead>Roles</TableHead>\n                    <TableHead>Status</TableHead>\n                    <TableHead>Ações</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {users.map((user) => (\n                    <TableRow key={user.id}>\n                      <TableCell className=\"font-medium\">\n                        {user.name || 'Sem nome'}\n                      </TableCell>\n                      <TableCell>{user.email}</TableCell>\n                      <TableCell>{user.phone || '-'}</TableCell>\n                      <TableCell>\n                        <div className=\"flex flex-wrap gap-1\">\n                          {getUserRoles(user.id).map((role) => (\n                            <Badge key={role} variant=\"secondary\">\n                              {role}\n                            </Badge>\n                          ))}\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <Badge variant={user.is_active ? \"default\" : \"destructive\"}>\n                          {user.is_active ? 'Ativo' : 'Inativo'}\n                        </Badge>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"flex items-center gap-2\">\n                          <Button\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => setSelectedUser(user)}\n                          >\n                            <Eye className=\"h-3 w-3\" />\n                          </Button>\n                          <Button\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => handleToggleUserStatus(user.id, user.is_active)}\n                          >\n                            {user.is_active ? 'Desativar' : 'Ativar'}\n                          </Button>\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"associations\" className=\"space-y-4\">\n          <UserAssociationsManager />\n        </TabsContent>\n      </Tabs>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AAfA;;;;;;;;;;;;;;;AAqCe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,yDAAyD;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,cAAc;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,WAAW;IACb;IAEA,kEAAkE;IAElE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,MAAM,QAAQ,GAAG,CAAC;gBAChB;gBACA;aACD;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,SAAS,OAAO,IAAI,IAAI,EAAE;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,wDAAwD;IAExD,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,eAAe,OAAO,IAAI,IAAI,EAAE;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,oBAAoB;gBAClE,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA,gBAAgB;gBAChB,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,UAAU;oBACV,MAAM;oBACN,WAAW;gBACb;gBACA;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,0DAA0D;IAC1D,wDAAwD;IAExD,MAAM,yBAAyB,OAAO,QAAgB;QACpD,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,iBAAiB,EAAE,QAAQ,EAAE;gBAC5E,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;oBAAE,WAAW,CAAC;gBAAS;YAC9C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,QAAQ,EAAE,CAAC,WAAW,YAAY,aAAa,aAAa,CAAC;gBAC7E;gBACA;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,6DAA6D;QAC7D,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtC,OAAO,MAAM,OAAO;YAAC,KAAK,IAAI;SAAC,GAAG,EAAE;IACtC;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;IACjD;IAEA,MAAM,iBAAiB;QAAC;QAAS;QAA2B;KAAY;IAExE,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;IAKpD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAc,cAAc;;0CACxC,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;;sDACL,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIzC,6LAAC,qIAAA,CAAA,gBAAa;;kDACZ,6LAAC,qIAAA,CAAA,eAAY;;0DACX,6LAAC,qIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,6LAAC,qIAAA,CAAA,oBAAiB;0DAAC;;;;;;;;;;;;kDAIrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAClE,aAAY;;;;;;;;;;;;0DAGhB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAO;;;;;;kEACtB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,IAAI;wDACpB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACjE,aAAY;;;;;;;;;;;;0DAGhB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,KAAK;wDACrB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAClE,aAAY;;;;;;;;;;;;0DAGhB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW;;;;;;kEAC1B,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,QAAQ;wDACxB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACrE,aAAY;;;;;;;;;;;;0DAGhB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAO;;;;;;kEACtB,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO,SAAS,IAAI;wDAAE,eAAe,CAAC,QAAU,YAAY;gEAAE,GAAG,QAAQ;gEAAE,MAAM;4DAAM;;0EAC7F,6LAAC,qIAAA,CAAA,gBAAa;0EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;0EAEd,6LAAC,qIAAA,CAAA,gBAAa;0EACX,eAAe,GAAG,CAAC,CAAA,qBAClB,6LAAC,qIAAA,CAAA,aAAU;wEAAY,OAAO;kFAC3B,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;uEAD5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ3B,6LAAC,qIAAA,CAAA,eAAY;;0DACX,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS,IAAM,gBAAgB;0DAAQ;;;;;;0DAGjE,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAS;0DAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,6LAAC,mIAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;;kCACrC,6LAAC,mIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAQ,WAAU;;kDACnC,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG/B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAe,WAAU;;kDAC1C,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAKhC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAQ,WAAU;kCACnC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;;0DACJ,6LAAC,oIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;sEACP,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;;;;;;;;;;;;0DAGf,6LAAC,oIAAA,CAAA,YAAS;0DACP,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,oIAAA,CAAA,WAAQ;;0EACP,6LAAC,oIAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,KAAK,IAAI,IAAI;;;;;;0EAEhB,6LAAC,oIAAA,CAAA,YAAS;0EAAE,KAAK,KAAK;;;;;;0EACtB,6LAAC,oIAAA,CAAA,YAAS;0EAAE,KAAK,KAAK,IAAI;;;;;;0EAC1B,6LAAC,oIAAA,CAAA,YAAS;0EACR,cAAA,6LAAC;oEAAI,WAAU;8EACZ,aAAa,KAAK,EAAE,EAAE,GAAG,CAAC,CAAC,qBAC1B,6LAAC,oIAAA,CAAA,QAAK;4EAAY,SAAQ;sFACvB;2EADS;;;;;;;;;;;;;;;0EAMlB,6LAAC,oIAAA,CAAA,YAAS;0EACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAS,KAAK,SAAS,GAAG,YAAY;8EAC1C,KAAK,SAAS,GAAG,UAAU;;;;;;;;;;;0EAGhC,6LAAC,oIAAA,CAAA,YAAS;0EACR,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,gBAAgB;sFAE/B,cAAA,6LAAC,mMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;;;;;;sFAEjB,6LAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,uBAAuB,KAAK,EAAE,EAAE,KAAK,SAAS;sFAE5D,KAAK,SAAS,GAAG,cAAc;;;;;;;;;;;;;;;;;;uDAlCzB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA8ClC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAe,WAAU;kCAC1C,cAAA,6LAAC,gJAAA,CAAA,0BAAuB;;;;;;;;;;;;;;;;;;;;;;AAKlC;GAjVwB;;QAQJ,+HAAA,CAAA,WAAQ;;;KARJ", "debugId": null}}]}