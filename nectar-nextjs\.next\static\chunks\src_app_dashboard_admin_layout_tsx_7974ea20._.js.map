{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/app/dashboard/admin/layout.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { usePermissions } from '@/hooks/usePermissions';\n\nexport default function AdminLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const { isAdmin, loading } = usePermissions();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!loading && !isAdmin) {\n      router.push('/dashboard');\n    }\n  }, [isAdmin, loading, router]);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\n      </div>\n    );\n  }\n\n  if (!isAdmin) {\n    return null; // Will redirect\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS,YAAY,EAClC,QAAQ,EAGT;;IACC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IAC1C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,WAAW,CAAC,SAAS;gBACxB,OAAO,IAAI,CAAC;YACd;QACF;gCAAG;QAAC;QAAS;QAAS;KAAO;IAE7B,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO,MAAM,gBAAgB;IAC/B;IAEA,qBAAO;kBAAG;;AACZ;GA3BwB;;QAKO,kIAAA,CAAA,iBAAc;QAC5B,qIAAA,CAAA,YAAS;;;KANF", "debugId": null}}]}