{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport {\n  ChevronDownIcon,\n  ChevronLeftIcon,\n  ChevronRightIcon,\n} from \"lucide-react\"\nimport { DayButton, DayPicker, getDefaultClassNames } from \"react-day-picker\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Button, buttonVariants } from \"@/components/ui/button\"\n\nfunction Calendar({\n  className,\n  classNames,\n  showOutsideDays = true,\n  captionLayout = \"label\",\n  buttonVariant = \"ghost\",\n  formatters,\n  components,\n  ...props\n}: React.ComponentProps<typeof DayPicker> & {\n  buttonVariant?: React.ComponentProps<typeof Button>[\"variant\"]\n}) {\n  const defaultClassNames = getDefaultClassNames()\n\n  return (\n    <DayPicker\n      showOutsideDays={showOutsideDays}\n      className={cn(\n        \"bg-background group/calendar p-3 [--cell-size:2rem] [[data-slot=card-content]_&]:bg-transparent [[data-slot=popover-content]_&]:bg-transparent\",\n        String.raw`rtl:**:[.rdp-button\\_next>svg]:rotate-180`,\n        String.raw`rtl:**:[.rdp-button\\_previous>svg]:rotate-180`,\n        className\n      )}\n      captionLayout={captionLayout}\n      formatters={{\n        formatMonthDropdown: (date) =>\n          date.toLocaleString(\"default\", { month: \"short\" }),\n        ...formatters,\n      }}\n      classNames={{\n        root: cn(\"w-fit\", defaultClassNames.root),\n        months: cn(\n          \"relative flex flex-col gap-4 md:flex-row\",\n          defaultClassNames.months\n        ),\n        month: cn(\"flex w-full flex-col gap-4\", defaultClassNames.month),\n        nav: cn(\n          \"absolute inset-x-0 top-0 flex w-full items-center justify-between gap-1\",\n          defaultClassNames.nav\n        ),\n        button_previous: cn(\n          buttonVariants({ variant: buttonVariant }),\n          \"h-[--cell-size] w-[--cell-size] select-none p-0 aria-disabled:opacity-50\",\n          defaultClassNames.button_previous\n        ),\n        button_next: cn(\n          buttonVariants({ variant: buttonVariant }),\n          \"h-[--cell-size] w-[--cell-size] select-none p-0 aria-disabled:opacity-50\",\n          defaultClassNames.button_next\n        ),\n        month_caption: cn(\n          \"flex h-[--cell-size] w-full items-center justify-center px-[--cell-size]\",\n          defaultClassNames.month_caption\n        ),\n        dropdowns: cn(\n          \"flex h-[--cell-size] w-full items-center justify-center gap-1.5 text-sm font-medium\",\n          defaultClassNames.dropdowns\n        ),\n        dropdown_root: cn(\n          \"has-focus:border-ring border-input shadow-xs has-focus:ring-ring/50 has-focus:ring-[3px] relative rounded-md border\",\n          defaultClassNames.dropdown_root\n        ),\n        dropdown: cn(\n          \"bg-popover absolute inset-0 opacity-0\",\n          defaultClassNames.dropdown\n        ),\n        caption_label: cn(\n          \"select-none font-medium\",\n          captionLayout === \"label\"\n            ? \"text-sm\"\n            : \"[&>svg]:text-muted-foreground flex h-8 items-center gap-1 rounded-md pl-2 pr-1 text-sm [&>svg]:size-3.5\",\n          defaultClassNames.caption_label\n        ),\n        table: \"w-full border-collapse\",\n        weekdays: cn(\"flex\", defaultClassNames.weekdays),\n        weekday: cn(\n          \"text-muted-foreground flex-1 select-none rounded-md text-[0.8rem] font-normal\",\n          defaultClassNames.weekday\n        ),\n        week: cn(\"mt-2 flex w-full\", defaultClassNames.week),\n        week_number_header: cn(\n          \"w-[--cell-size] select-none\",\n          defaultClassNames.week_number_header\n        ),\n        week_number: cn(\n          \"text-muted-foreground select-none text-[0.8rem]\",\n          defaultClassNames.week_number\n        ),\n        day: cn(\n          \"group/day relative aspect-square h-full w-full select-none p-0 text-center [&:first-child[data-selected=true]_button]:rounded-l-md [&:last-child[data-selected=true]_button]:rounded-r-md\",\n          defaultClassNames.day\n        ),\n        range_start: cn(\n          \"bg-accent rounded-l-md\",\n          defaultClassNames.range_start\n        ),\n        range_middle: cn(\"rounded-none\", defaultClassNames.range_middle),\n        range_end: cn(\"bg-accent rounded-r-md\", defaultClassNames.range_end),\n        today: cn(\n          \"bg-accent text-accent-foreground rounded-md data-[selected=true]:rounded-none\",\n          defaultClassNames.today\n        ),\n        outside: cn(\n          \"text-muted-foreground aria-selected:text-muted-foreground\",\n          defaultClassNames.outside\n        ),\n        disabled: cn(\n          \"text-muted-foreground opacity-50\",\n          defaultClassNames.disabled\n        ),\n        hidden: cn(\"invisible\", defaultClassNames.hidden),\n        ...classNames,\n      }}\n      components={{\n        Root: ({ className, rootRef, ...props }) => {\n          return (\n            <div\n              data-slot=\"calendar\"\n              ref={rootRef}\n              className={cn(className)}\n              {...props}\n            />\n          )\n        },\n        Chevron: ({ className, orientation, ...props }) => {\n          if (orientation === \"left\") {\n            return (\n              <ChevronLeftIcon className={cn(\"size-4\", className)} {...props} />\n            )\n          }\n\n          if (orientation === \"right\") {\n            return (\n              <ChevronRightIcon\n                className={cn(\"size-4\", className)}\n                {...props}\n              />\n            )\n          }\n\n          return (\n            <ChevronDownIcon className={cn(\"size-4\", className)} {...props} />\n          )\n        },\n        DayButton: CalendarDayButton,\n        WeekNumber: ({ children, ...props }) => {\n          return (\n            <td {...props}>\n              <div className=\"flex size-[--cell-size] items-center justify-center text-center\">\n                {children}\n              </div>\n            </td>\n          )\n        },\n        ...components,\n      }}\n      {...props}\n    />\n  )\n}\n\nfunction CalendarDayButton({\n  className,\n  day,\n  modifiers,\n  ...props\n}: React.ComponentProps<typeof DayButton>) {\n  const defaultClassNames = getDefaultClassNames()\n\n  const ref = React.useRef<HTMLButtonElement>(null)\n  React.useEffect(() => {\n    if (modifiers.focused) ref.current?.focus()\n  }, [modifiers.focused])\n\n  return (\n    <Button\n      ref={ref}\n      variant=\"ghost\"\n      size=\"icon\"\n      data-day={day.date.toLocaleDateString()}\n      data-selected-single={\n        modifiers.selected &&\n        !modifiers.range_start &&\n        !modifiers.range_end &&\n        !modifiers.range_middle\n      }\n      data-range-start={modifiers.range_start}\n      data-range-end={modifiers.range_end}\n      data-range-middle={modifiers.range_middle}\n      className={cn(\n        \"data-[selected-single=true]:bg-primary data-[selected-single=true]:text-primary-foreground data-[range-middle=true]:bg-accent data-[range-middle=true]:text-accent-foreground data-[range-start=true]:bg-primary data-[range-start=true]:text-primary-foreground data-[range-end=true]:bg-primary data-[range-end=true]:text-primary-foreground group-data-[focused=true]/day:border-ring group-data-[focused=true]/day:ring-ring/50 flex aspect-square h-auto w-full min-w-[--cell-size] flex-col gap-1 font-normal leading-none data-[range-end=true]:rounded-md data-[range-middle=true]:rounded-none data-[range-start=true]:rounded-md group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-[3px] [&>span]:text-xs [&>span]:opacity-70\",\n        defaultClassNames.day,\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Calendar, CalendarDayButton }\n\n\n/* import * as React from \"react\";\nimport { DayPicker } from \"react-day-picker\";\nimport { ptBR } from \"date-fns/locale\";\nimport { format, isAfter, isSameDay, startOfDay } from \"date-fns\";\nimport { cn } from \"@/lib/utils\";\nimport { ChevronLeft, ChevronRight } from \"lucide-react\";\n\nexport type CalendarProps = React.ComponentProps<typeof DayPicker> & {\n  appointmentCounts?: Record<string, number>;\n  clinicSettings?: {\n    working_hours_start: string;\n    working_hours_end: string;\n    working_days: number[];\n    appointment_duration_minutes: number;\n    allow_weekend_appointments: boolean;\n  };\n  appointments?: Array<{\n    start_time: string;\n    end_time: string;\n  }>;\n};\n\nfunction Calendar({\n  className,\n  classNames,\n  showOutsideDays = true,\n  appointmentCounts = {},\n  clinicSettings,\n  appointments = [],\n  ...props\n}: CalendarProps) {\n  const modifiers = React.useMemo(() => {\n    const hasAppointments: Date[] = [];\n    const hasAvailability: Date[] = [];\n    const today = startOfDay(new Date());\n\n    Object.keys(appointmentCounts).forEach(dateKey => {\n      if (appointmentCounts[dateKey] > 0) {\n        hasAppointments.push(new Date(dateKey));\n      }\n    });\n\n    // Calculate availability for future dates only\n    if (clinicSettings) {\n      const calculateAvailableSlots = (date: Date) => {\n        if (!isAfter(date, today) && !isSameDay(date, today)) return 0;\n\n        const dayOfWeek = date.getDay() === 0 ? 7 : date.getDay();\n\n        if (!clinicSettings.working_days.includes(dayOfWeek)) {\n          if (!clinicSettings.allow_weekend_appointments) return 0;\n        }\n\n        const [startHour, startMinute] = clinicSettings.working_hours_start.split(':').map(Number);\n        const [endHour, endMinute] = clinicSettings.working_hours_end.split(':').map(Number);\n\n        const startMinutes = startHour * 60 + startMinute;\n        const endMinutes = endHour * 60 + endMinute;\n        const totalMinutes = endMinutes - startMinutes;\n        const totalSlots = Math.floor(totalMinutes / clinicSettings.appointment_duration_minutes);\n\n        const dateStr = format(date, 'yyyy-MM-dd');\n        const dayAppointments = appointments.filter(apt =>\n          format(new Date(apt.start_time), 'yyyy-MM-dd') === dateStr\n        );\n\n        const occupiedSlots = dayAppointments.length;\n        return Math.max(0, totalSlots - occupiedSlots);\n      };\n\n      for (let i = 0; i < 60; i++) {\n        const checkDate = new Date(today);\n        checkDate.setDate(checkDate.getDate() + i);\n\n        if (calculateAvailableSlots(checkDate) > 0) {\n          hasAvailability.push(checkDate);\n        }\n      }\n    }\n\n    return { hasAppointments, hasAvailability };\n  }, [appointmentCounts, clinicSettings, appointments]);\n\n  const modifiersClassNames = {\n    hasAppointments: \"has-appointments\",\n    hasAvailability: \"has-availability\"\n  };\n\n  return (\n    <div className={cn(\"calendar-wrapper\", className)}>\n      <style jsx>{`\n        .calendar-wrapper :global(.rdp-root) {\n          --rdp-accent-color: #2563eb;\n          --rdp-accent-background-color: #dbeafe;\n          --rdp-day-height: 48px;\n          --rdp-day-width: 48px;\n          --rdp-day_button-border-radius: 8px;\n          --rdp-day_button-border: 1px solid transparent;\n          --rdp-day_button-height: 46px;\n          --rdp-day_button-width: 46px;\n          --rdp-selected-border: 2px solid var(--rdp-accent-color);\n          --rdp-disabled-opacity: 0.3;\n          --rdp-outside-opacity: 0.5;\n          --rdp-today-color: var(--rdp-accent-color);\n          --rdp-dropdown-gap: 0.5rem;\n          --rdp-months-gap: 2rem;\n          --rdp-nav_button-disabled-opacity: 0.5;\n          --rdp-nav_button-height: 2.25rem;\n          --rdp-nav_button-width: 2.25rem;\n          --rdp-nav-height: 2.75rem;\n          --rdp-range_middle-background-color: var(--rdp-accent-background-color);\n          --rdp-range_middle-color: inherit;\n          --rdp-range_start-color: white;\n          --rdp-range_start-background: linear-gradient(var(--rdp-gradient-direction), transparent 50%, var(--rdp-range_middle-background-color) 50%);\n          --rdp-range_start-date-background-color: var(--rdp-accent-color);\n          --rdp-range_end-background: linear-gradient(var(--rdp-gradient-direction), var(--rdp-range_middle-background-color) 50%, transparent 50%);\n          --rdp-range_end-color: white;\n          --rdp-range_end-date-background-color: var(--rdp-accent-color);\n          --rdp-week_number-border-radius: 100%;\n          --rdp-week_number-border: 2px solid transparent;\n          --rdp-week_number-height: var(--rdp-day-height);\n          --rdp-week_number-opacity: 0.75;\n          --rdp-week_number-width: var(--rdp-day-width);\n          --rdp-weeknumber-text-align: center;\n          --rdp-weekday-opacity: 0.75;\n          --rdp-weekday-padding: 0.75rem 0rem;\n          --rdp-weekday-text-align: center;\n          --rdp-gradient-direction: 90deg;\n          --rdp-animation_duration: 0.3s;\n          --rdp-animation_timing: cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n\n        .calendar-wrapper {\n          max-width: 400px;\n          margin: 0 auto;\n          padding: 24px;\n          background: white;\n          border-radius: 12px;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n        }\n\n\n        .calendar-wrapper :global(.rdp-root[dir=\"rtl\"]) {\n          --rdp-gradient-direction: -90deg;\n        }\n\n        .calendar-wrapper :global(.rdp-root[data-broadcast-calendar=\"true\"]) {\n          --rdp-outside-opacity: unset;\n        }\n\n        .calendar-wrapper :global(.rdp-root) {\n          position: relative;\n          box-sizing: border-box;\n        }\n\n        .calendar-wrapper :global(.rdp-root *) {\n          box-sizing: border-box;\n        }\n\n        .calendar-wrapper :global(.rdp-day) {\n          width: var(--rdp-day-width);\n          height: var(--rdp-day-height);\n          text-align: center;\n        }\n\n        .calendar-wrapper :global(.rdp-day_button) {\n          background: none;\n          padding: 0;\n          margin: 0;\n          cursor: pointer;\n          font: inherit;\n          color: inherit;\n          justify-content: center;\n          align-items: center;\n          display: flex;\n          width: var(--rdp-day_button-width);\n          height: var(--rdp-day_button-height);\n          border: var(--rdp-day_button-border);\n          border-radius: var(--rdp-day_button-border-radius);\n          transition: all 0.2s ease;\n        }\n\n        .calendar-wrapper :global(.rdp-day_button:hover) {\n          background-color: #f3f4f6;\n          transform: translateY(-1px);\n          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n        }\n\n        .calendar-wrapper :global(.rdp-day_button:disabled) {\n          cursor: revert;\n        }\n\n        .calendar-wrapper :global(.rdp-caption_label) {\n          z-index: 1;\n          position: relative;\n          display: inline-flex;\n          align-items: center;\n          white-space: nowrap;\n          border: 0;\n          font-size: 1.125rem;\n          font-weight: 600;\n          color: #1f2937;\n          text-transform: capitalize;\n        }\n\n        .calendar-wrapper :global(.rdp-dropdown:focus-visible ~ .rdp-caption_label) {\n          outline: 5px auto Highlight;\n          outline: 5px auto -webkit-focus-ring-color;\n        }\n\n        .calendar-wrapper :global(.rdp-button_next),\n        .calendar-wrapper :global(.rdp-button_previous) {\n          border: 1px solid #e5e7eb;\n          background: white;\n          padding: 0;\n          margin: 0;\n          cursor: pointer;\n          font: inherit;\n          color: #6b7280;\n          -moz-appearance: none;\n          -webkit-appearance: none;\n          display: inline-flex;\n          align-items: center;\n          justify-content: center;\n          position: relative;\n          appearance: none;\n          width: var(--rdp-nav_button-width);\n          height: var(--rdp-nav_button-height);\n          border-radius: 8px;\n          transition: all 0.2s ease;\n          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n        }\n\n        .calendar-wrapper :global(.rdp-button_next:hover),\n        .calendar-wrapper :global(.rdp-button_previous:hover) {\n          background: #f9fafb;\n          border-color: #d1d5db;\n          color: #374151;\n          transform: translateY(-1px);\n          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n        }\n\n        .calendar-wrapper :global(.rdp-button_next:disabled),\n        .calendar-wrapper :global(.rdp-button_next[aria-disabled=\"true\"]),\n        .calendar-wrapper :global(.rdp-button_previous:disabled),\n        .calendar-wrapper :global(.rdp-button_previous[aria-disabled=\"true\"]) {\n          cursor: revert;\n          opacity: var(--rdp-nav_button-disabled-opacity);\n        }\n\n        .calendar-wrapper :global(.rdp-button_next:disabled:hover),\n        .calendar-wrapper :global(.rdp-button_previous:disabled:hover) {\n          transform: none;\n          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n        }\n\n        .calendar-wrapper :global(.rdp-chevron) {\n          display: inline-block;\n          fill: currentColor;\n          width: 16px;\n          height: 16px;\n        }\n\n        .calendar-wrapper :global(.rdp-root[dir=\"rtl\"] .rdp-nav .rdp-chevron) {\n          transform: rotate(180deg);\n          transform-origin: 50%;\n        }\n\n        .calendar-wrapper :global(.rdp-dropdowns) {\n          position: relative;\n          display: inline-flex;\n          align-items: center;\n          gap: var(--rdp-dropdown-gap);\n        }\n\n        .calendar-wrapper :global(.rdp-dropdown) {\n          z-index: 2;\n          opacity: 0;\n          appearance: none;\n          position: absolute;\n          inset-block-start: 0;\n          inset-block-end: 0;\n          inset-inline-start: 0;\n          width: 100%;\n          margin: 0;\n          padding: 0;\n          cursor: inherit;\n          border: none;\n          line-height: inherit;\n        }\n\n        .calendar-wrapper :global(.rdp-dropdown_root) {\n          position: relative;\n          display: inline-flex;\n          align-items: center;\n        }\n\n        .calendar-wrapper :global(.rdp-dropdown_root[data-disabled=\"true\"] .rdp-chevron) {\n          opacity: var(--rdp-disabled-opacity);\n        }\n\n        .calendar-wrapper :global(.rdp-month_caption) {\n          display: flex;\n          align-content: center;\n          height: var(--rdp-nav-height);\n          font-weight: bold;\n          font-size: large;\n          justify-content: space-between;\n          align-items: center;\n          padding: 0 8px 24px 8px;\n        }\n\n        .calendar-wrapper :global(.rdp-months) {\n          position: relative;\n          display: flex;\n          flex-wrap: wrap;\n          gap: var(--rdp-months-gap);\n          max-width: fit-content;\n        }\n\n        .calendar-wrapper :global(.rdp-month_grid) {\n          border-collapse: collapse;\n          width: 100%;\n        }\n\n        .calendar-wrapper :global(.rdp-nav) {\n          display: flex;\n          align-items: center;\n          gap: 8px;\n        }\n\n        .calendar-wrapper :global(.rdp-weekday) {\n          opacity: var(--rdp-weekday-opacity);\n          padding: var(--rdp-weekday-padding);\n          font-weight: 600;\n          font-size: 14px;\n          text-align: var(--rdp-weekday-text-align);\n          text-transform: uppercase;\n          letter-spacing: 0.5px;\n          color: #6b7280;\n        }\n\n        .calendar-wrapper :global(.rdp-week_number) {\n          opacity: var(--rdp-week_number-opacity);\n          font-weight: 400;\n          font-size: small;\n          height: var(--rdp-week_number-height);\n          width: var(--rdp-week_number-width);\n          border: var(--rdp-week_number-border);\n          border-radius: var(--rdp-week_number-border-radius);\n          text-align: var(--rdp-weeknumber-text-align);\n        }\n\n        .calendar-wrapper :global(.rdp-today:not(.rdp-outside)) {\n          color: white;\n          font-weight: 600;\n        }\n\n        .calendar-wrapper :global(.rdp-today .rdp-day_button) {\n          background-color: var(--rdp-accent-color);\n          color: white;\n          box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3);\n        }\n\n        .calendar-wrapper :global(.rdp-today .rdp-day_button:hover) {\n          background-color: #1d4ed8;\n          transform: translateY(-1px);\n          box-shadow: 0 4px 8px rgba(37, 99, 235, 0.4);\n        }\n\n        .calendar-wrapper :global(.rdp-selected) {\n          font-weight: bold;\n        }\n\n        .calendar-wrapper :global(.rdp-selected .rdp-day_button) {\n          border: var(--rdp-selected-border);\n          background-color: var(--rdp-accent-color);\n          color: white;\n          box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3);\n        }\n\n        .calendar-wrapper :global(.rdp-selected .rdp-day_button:hover) {\n          background-color: #1d4ed8;\n          transform: translateY(-1px);\n          box-shadow: 0 4px 8px rgba(37, 99, 235, 0.4);\n        }\n\n        .calendar-wrapper :global(.rdp-outside) {\n          opacity: var(--rdp-outside-opacity);\n        }\n\n        .calendar-wrapper :global(.rdp-disabled) {\n          opacity: var(--rdp-disabled-opacity);\n        }\n\n        .calendar-wrapper :global(.rdp-disabled .rdp-day_button) {\n          cursor: not-allowed;\n        }\n\n        .calendar-wrapper :global(.rdp-disabled .rdp-day_button:hover) {\n          background: transparent;\n          transform: none;\n          box-shadow: none;\n        }\n\n        .calendar-wrapper :global(.rdp-hidden) {\n          visibility: hidden;\n        }\n\n        .calendar-wrapper :global(.rdp-focusable) {\n          cursor: pointer;\n        }\n\n\n        .calendar-wrapper :global(.has-appointments::after) {\n          content: '';\n          position: absolute;\n          bottom: 4px;\n          left: 50%;\n          transform: translateX(-50%);\n          width: 6px;\n          height: 6px;\n          background-color: #3b82f6;\n          border-radius: 50%;\n          border: 1px solid white;\n          z-index: 1;\n        }\n\n        .calendar-wrapper :global(.rdp-selected.has-appointments::after),\n        .calendar-wrapper :global(.rdp-today.has-appointments::after) {\n          background-color: white;\n          border-color: var(--rdp-accent-color);\n        }\n\n        .calendar-wrapper :global(.has-availability .rdp-day_button) {\n          background-color: #f0fdf4;\n          border-color: #bbf7d0;\n        }\n\n        .calendar-wrapper :global(.has-availability .rdp-day_button:hover) {\n          background-color: #dcfce7;\n          border-color: #86efac;\n        }\n\n\n        @keyframes rdp-slide_in_left {\n          0% { transform: translateX(-100%); }\n          100% { transform: translateX(0); }\n        }\n\n        @keyframes rdp-slide_in_right {\n          0% { transform: translateX(100%); }\n          100% { transform: translateX(0); }\n        }\n\n        @keyframes rdp-slide_out_left {\n          0% { transform: translateX(0); }\n          100% { transform: translateX(-100%); }\n        }\n\n        @keyframes rdp-slide_out_right {\n          0% { transform: translateX(0); }\n          100% { transform: translateX(100%); }\n        }\n\n        .calendar-wrapper :global(.rdp-weeks_before_enter) {\n          animation: rdp-slide_in_left var(--rdp-animation_duration) var(--rdp-animation_timing) forwards;\n        }\n\n        .calendar-wrapper :global(.rdp-weeks_before_exit) {\n          animation: rdp-slide_out_left var(--rdp-animation_duration) var(--rdp-animation_timing) forwards;\n        }\n\n        .calendar-wrapper :global(.rdp-weeks_after_enter) {\n          animation: rdp-slide_in_right var(--rdp-animation_duration) var(--rdp-animation_timing) forwards;\n        }\n\n        .calendar-wrapper :global(.rdp-weeks_after_exit) {\n          animation: rdp-slide_out_right var(--rdp-animation_duration) var(--rdp-animation_timing) forwards;\n        }\n\n\n        @media (max-width: 640px) {\n          .calendar-wrapper {\n            padding: 16px;\n            max-width: 320px;\n          }\n\n          .calendar-wrapper :global(.rdp-root) {\n            --rdp-day-height: 40px;\n            --rdp-day-width: 40px;\n            --rdp-day_button-height: 38px;\n            --rdp-day_button-width: 38px;\n            --rdp-nav_button-height: 2rem;\n            --rdp-nav_button-width: 2rem;\n            --rdp-weekday-padding: 0.5rem 0rem;\n          }\n\n          .calendar-wrapper :global(.rdp-caption_label) {\n            font-size: 1rem;\n          }\n\n          .calendar-wrapper :global(.rdp-weekday) {\n            font-size: 12px;\n          }\n\n          .calendar-wrapper :global(.rdp-month_caption) {\n            padding: 0 4px 16px 4px;\n          }\n        }\n\n        @media (max-width: 480px) {\n          .calendar-wrapper {\n            max-width: 280px;\n          }\n\n          .calendar-wrapper :global(.rdp-root) {\n            --rdp-day-height: 36px;\n            --rdp-day-width: 36px;\n            --rdp-day_button-height: 34px;\n            --rdp-day_button-width: 34px;\n          }\n\n          .calendar-wrapper :global(.rdp-day_button) {\n            font-size: 13px;\n          }\n\n          .calendar-wrapper :global(.rdp-weekday) {\n            font-size: 11px;\n          }\n        }\n      `}</style>\n      \n      <DayPicker\n        locale={ptBR}\n        showOutsideDays={showOutsideDays}\n        modifiers={modifiers}\n        modifiersClassNames={modifiersClassNames}\n        components={{\n          IconLeft: ({ ...props }) => <ChevronLeft className=\"w-4 h-4\" />,\n          IconRight: ({ ...props }) => <ChevronRight className=\"w-4 h-4\" />,\n        }}\n        navLayout=\"around\"\n        {...props}\n      />\n    </div>\n  );\n}\n\nCalendar.displayName = \"Calendar\";\n\nexport { Calendar };\n */\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAKA;AAAA;AAEA;AACA;;;AAXA;;;;;;AAaA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,gBAAgB,OAAO,EACvB,gBAAgB,OAAO,EACvB,UAAU,EACV,UAAU,EACV,GAAG,OAGJ;IACC,MAAM,oBAAoB,CAAA,GAAA,2LAAA,CAAA,uBAAoB,AAAD;IAE7C,qBACE,6LAAC,qKAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kJACA,OAAO,GAAG,CAAC,yCAAyC,CAAC,EACrD,OAAO,GAAG,CAAC,6CAA6C,CAAC,EACzD;QAEF,eAAe;QACf,YAAY;YACV,qBAAqB,CAAC,OACpB,KAAK,cAAc,CAAC,WAAW;oBAAE,OAAO;gBAAQ;YAClD,GAAG,UAAU;QACf;QACA,YAAY;YACV,MAAM,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,kBAAkB,IAAI;YACxC,QAAQ,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACP,4CACA,kBAAkB,MAAM;YAE1B,OAAO,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,kBAAkB,KAAK;YAC/D,KAAK,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACJ,2EACA,kBAAkB,GAAG;YAEvB,iBAAiB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAc,IACxC,4EACA,kBAAkB,eAAe;YAEnC,aAAa,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACZ,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAc,IACxC,4EACA,kBAAkB,WAAW;YAE/B,eAAe,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,4EACA,kBAAkB,aAAa;YAEjC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uFACA,kBAAkB,SAAS;YAE7B,eAAe,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,uHACA,kBAAkB,aAAa;YAEjC,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACT,yCACA,kBAAkB,QAAQ;YAE5B,eAAe,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,2BACA,kBAAkB,UACd,YACA,2GACJ,kBAAkB,aAAa;YAEjC,OAAO;YACP,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,kBAAkB,QAAQ;YAC/C,SAAS,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,iFACA,kBAAkB,OAAO;YAE3B,MAAM,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB,kBAAkB,IAAI;YACnD,oBAAoB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACnB,+BACA,kBAAkB,kBAAkB;YAEtC,aAAa,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACZ,mDACA,kBAAkB,WAAW;YAE/B,KAAK,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACJ,6LACA,kBAAkB,GAAG;YAEvB,aAAa,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACZ,0BACA,kBAAkB,WAAW;YAE/B,cAAc,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,kBAAkB,YAAY;YAC/D,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B,kBAAkB,SAAS;YACnE,OAAO,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACN,iFACA,kBAAkB,KAAK;YAEzB,SAAS,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,6DACA,kBAAkB,OAAO;YAE3B,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACT,oCACA,kBAAkB,QAAQ;YAE5B,QAAQ,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,kBAAkB,MAAM;YAChD,GAAG,UAAU;QACf;QACA,YAAY;YACV,MAAM,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO;gBACrC,qBACE,6LAAC;oBACC,aAAU;oBACV,KAAK;oBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;oBACb,GAAG,KAAK;;;;;;YAGf;YACA,SAAS,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,OAAO;gBAC5C,IAAI,gBAAgB,QAAQ;oBAC1B,qBACE,6LAAC,2NAAA,CAAA,kBAAe;wBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;wBAAa,GAAG,KAAK;;;;;;gBAElE;gBAEA,IAAI,gBAAgB,SAAS;oBAC3B,qBACE,6LAAC,6NAAA,CAAA,mBAAgB;wBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;wBACvB,GAAG,KAAK;;;;;;gBAGf;gBAEA,qBACE,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;YAElE;YACA,WAAW;YACX,YAAY,CAAC,EAAE,QAAQ,EAAE,GAAG,OAAO;gBACjC,qBACE,6LAAC;oBAAI,GAAG,KAAK;8BACX,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;YAIT;YACA,GAAG,UAAU;QACf;QACC,GAAG,KAAK;;;;;;AAGf;KA/JS;AAiKT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,EACH,SAAS,EACT,GAAG,OACoC;;IACvC,MAAM,oBAAoB,CAAA,GAAA,2LAAA,CAAA,uBAAoB,AAAD;IAE7C,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAqB;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;uCAAE;YACd,IAAI,UAAU,OAAO,EAAE,IAAI,OAAO,EAAE;QACtC;sCAAG;QAAC,UAAU,OAAO;KAAC;IAEtB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,KAAK;QACL,SAAQ;QACR,MAAK;QACL,YAAU,IAAI,IAAI,CAAC,kBAAkB;QACrC,wBACE,UAAU,QAAQ,IAClB,CAAC,UAAU,WAAW,IACtB,CAAC,UAAU,SAAS,IACpB,CAAC,UAAU,YAAY;QAEzB,oBAAkB,UAAU,WAAW;QACvC,kBAAgB,UAAU,SAAS;QACnC,qBAAmB,UAAU,YAAY;QACzC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uwBACA,kBAAkB,GAAG,EACrB;QAED,GAAG,KAAK;;;;;;AAGf;GApCS;MAAA;;;;;;;;CAyCT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA0iBC", "debugId": null}}, {"offset": {"line": 734, "column": 4}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 740, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 843, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/tabs.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 908, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAEA;;;;;;AAEA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nconst AlertDialog = AlertDialogPrimitive.Root\r\n\r\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\r\n\r\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\r\n\r\nconst AlertDialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n))\r\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\r\n\r\nconst AlertDialogContent = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPortal>\r\n    <AlertDialogOverlay />\r\n    <AlertDialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </AlertDialogPortal>\r\n))\r\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\r\n\r\nconst AlertDialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-2 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\r\n\r\nconst AlertDialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\r\n\r\nconst AlertDialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\r\n\r\nconst AlertDialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogDescription.displayName =\r\n  AlertDialogPrimitive.Description.displayName\r\n\r\nconst AlertDialogAction = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Action\r\n    ref={ref}\r\n    className={cn(buttonVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\r\n\r\nconst AlertDialogCancel = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Cancel\r\n    ref={ref}\r\n    className={cn(\r\n      buttonVariants({ variant: \"outline\" }),\r\n      \"mt-2 sm:mt-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AAEA;AACA;;;;;;AAEA,MAAM,cAAc,8KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,8KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,8KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,8KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,8KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ConfirmDialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport React from 'react';\nimport {\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogFooter,\n  AlertDialogHeader,\n  AlertDialogTitle,\n} from '@/components/ui/alert-dialog';\n\ninterface ConfirmDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  title: string;\n  description: string;\n  confirmText?: string;\n  cancelText?: string;\n  onConfirm: () => void;\n  variant?: 'default' | 'destructive';\n}\n\nconst ConfirmDialog: React.FC<ConfirmDialogProps> = ({\n  open,\n  onOpenChange,\n  title,\n  description,\n  confirmText = 'Confirmar',\n  cancelText = 'Cancelar',\n  onConfirm,\n  variant = 'default'\n}) => {\n  const handleConfirm = () => {\n    onConfirm();\n    onOpenChange(false);\n  };\n\n  return (\n    <AlertDialog open={open} onOpenChange={onOpenChange}>\n      <AlertDialogContent>\n        <AlertDialogHeader>\n          <AlertDialogTitle>{title}</AlertDialogTitle>\n          <AlertDialogDescription>\n            {description}\n          </AlertDialogDescription>\n        </AlertDialogHeader>\n        <AlertDialogFooter>\n          <AlertDialogCancel>{cancelText}</AlertDialogCancel>\n          <AlertDialogAction\n            onClick={handleConfirm}\n            className={variant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : ''}\n          >\n            {confirmText}\n          </AlertDialogAction>\n        </AlertDialogFooter>\n      </AlertDialogContent>\n    </AlertDialog>\n  );\n};\n\nexport default ConfirmDialog;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAyBA,MAAM,gBAA8C,CAAC,EACnD,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,WAAW,EACX,cAAc,WAAW,EACzB,aAAa,UAAU,EACvB,SAAS,EACT,UAAU,SAAS,EACpB;IACC,MAAM,gBAAgB;QACpB;QACA,aAAa;IACf;IAEA,qBACE,6LAAC,8IAAA,CAAA,cAAW;QAAC,MAAM;QAAM,cAAc;kBACrC,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;8BACjB,6LAAC,8IAAA,CAAA,oBAAiB;;sCAChB,6LAAC,8IAAA,CAAA,mBAAgB;sCAAE;;;;;;sCACnB,6LAAC,8IAAA,CAAA,yBAAsB;sCACpB;;;;;;;;;;;;8BAGL,6LAAC,8IAAA,CAAA,oBAAiB;;sCAChB,6LAAC,8IAAA,CAAA,oBAAiB;sCAAE;;;;;;sCACpB,6LAAC,8IAAA,CAAA,oBAAiB;4BAChB,SAAS;4BACT,WAAW,YAAY,gBAAgB,uEAAuE;sCAE7G;;;;;;;;;;;;;;;;;;;;;;;AAMb;KApCM;uCAsCS", "debugId": null}}, {"offset": {"line": 1362, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 1410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/time-slots.ts"], "sourcesContent": ["import { addMinutes, format, isSameDay, isWithinInterval, startOfDay, endOfDay } from 'date-fns';\r\n\r\nexport type TimeSlot = {\r\n  id: string;\r\n  start_time: Date;\r\n  end_time: Date;\r\n  isAvailable: boolean;\r\n  isBlocked: boolean;\r\n  type: 'available' | 'appointment' | 'blocked';\r\n  appointment?: any;\r\n  block?: any;\r\n};\r\n\r\nexport type WorkingDay = {\r\n  enabled: boolean;\r\n  working_hours_start: string;\r\n  working_hours_end: string;\r\n  break_intervals: { start: string; end: string; }[];\r\n};\r\n\r\nexport type ClinicSettings = {\r\n  appointment_duration_minutes: number;\r\n  timezone: string;\r\n  weekly_schedule: {\r\n    monday: WorkingDay;\r\n    tuesday: WorkingDay;\r\n    wednesday: WorkingDay;\r\n    thursday: WorkingDay;\r\n    friday: WorkingDay;\r\n    saturday: WorkingDay;\r\n    sunday: WorkingDay;\r\n  };\r\n};\r\n\r\n// Legacy format for backward compatibility\r\nexport type LegacyClinicSettings = {\r\n  working_hours_start: string;\r\n  working_hours_end: string;\r\n  working_days: number[];\r\n  appointment_duration_minutes: number;\r\n  allow_weekend_appointments: boolean;\r\n};\r\n\r\n/**\r\n * Generate time slots for a specific date based on clinic settings\r\n */\r\nexport function generateTimeSlots(\r\n  date: Date,\r\n  clinicSettings: ClinicSettings | LegacyClinicSettings,\r\n  appointments: any[] = [],\r\n  blocks: any[] = []\r\n): TimeSlot[] {\r\n  const slots: TimeSlot[] = [];\r\n  \r\n  // Ensure appointments and blocks are arrays\r\n  const validAppointments = Array.isArray(appointments) ? appointments : [];\r\n  const validBlocks = Array.isArray(blocks) ? blocks : [];\r\n  \r\n  // Check if we're using the new format or legacy format\r\n  const isNewFormat = 'weekly_schedule' in clinicSettings;\r\n  \r\n  let dayConfig: WorkingDay | null = null;\r\n  let appointmentDuration = 30;\r\n  \r\n  if (isNewFormat) {\r\n    const newSettings = clinicSettings as ClinicSettings;\r\n    appointmentDuration = newSettings.appointment_duration_minutes || 30;\r\n    \r\n    // Get day configuration\r\n    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];\r\n    const dayOfWeek = date.getDay();\r\n    const dayName = dayNames[dayOfWeek] as keyof typeof newSettings.weekly_schedule;\r\n    \r\n    dayConfig = newSettings.weekly_schedule[dayName];\r\n    \r\n    // If day is not enabled, return empty slots\r\n    if (!dayConfig || !dayConfig.enabled) {\r\n      return slots;\r\n    }\r\n  } else {\r\n    // Legacy format\r\n    const legacySettings = clinicSettings as LegacyClinicSettings;\r\n    appointmentDuration = legacySettings.appointment_duration_minutes || 30;\r\n    \r\n    // Check if the day is within working days (legacy format)\r\n    const dayOfWeek = date.getDay();\r\n    const workingDays = Array.isArray(legacySettings.working_days) ? legacySettings.working_days : [1, 2, 3, 4, 5];\r\n    \r\n    if (!legacySettings.allow_weekend_appointments && !workingDays.includes(dayOfWeek)) {\r\n      return slots;\r\n    }\r\n    \r\n    // Convert legacy format to day config\r\n    dayConfig = {\r\n      enabled: true,\r\n      working_hours_start: legacySettings.working_hours_start || '08:00',\r\n      working_hours_end: legacySettings.working_hours_end || '18:00',\r\n      break_intervals: []\r\n    };\r\n  }\r\n  \r\n  if (!dayConfig) return slots;\r\n\r\n  // Parse working hours\r\n  const [startHour, startMinute] = dayConfig.working_hours_start.split(':').map(Number);\r\n  const [endHour, endMinute] = dayConfig.working_hours_end.split(':').map(Number);\r\n\r\n  // Create start and end times for the day\r\n  const workingStart = new Date(date);\r\n  workingStart.setHours(startHour, startMinute, 0, 0);\r\n  \r\n  const workingEnd = new Date(date);\r\n  workingEnd.setHours(endHour, endMinute, 0, 0);\r\n\r\n  // Generate slots based on appointment duration\r\n  let currentTime = new Date(workingStart);\r\n  \r\n  while (currentTime < workingEnd) {\r\n    const slotEnd = addMinutes(currentTime, appointmentDuration);\r\n    \r\n    // Don't create slot if it would extend beyond working hours\r\n    if (slotEnd > workingEnd) {\r\n      break;\r\n    }\r\n\r\n    // Check if this slot is within a break interval\r\n    const isInBreakInterval = dayConfig.break_intervals.some(breakInterval => {\r\n      const [breakStartHour, breakStartMinute] = breakInterval.start.split(':').map(Number);\r\n      const [breakEndHour, breakEndMinute] = breakInterval.end.split(':').map(Number);\r\n      \r\n      const breakStart = new Date(date);\r\n      breakStart.setHours(breakStartHour, breakStartMinute, 0, 0);\r\n      \r\n      const breakEnd = new Date(date);\r\n      breakEnd.setHours(breakEndHour, breakEndMinute, 0, 0);\r\n      \r\n      return (\r\n        (currentTime >= breakStart && currentTime < breakEnd) ||\r\n        (slotEnd > breakStart && slotEnd <= breakEnd) ||\r\n        (currentTime <= breakStart && slotEnd >= breakEnd)\r\n      );\r\n    });\r\n\r\n    // Skip this slot if it's within a break interval\r\n    if (isInBreakInterval) {\r\n      currentTime = addMinutes(currentTime, appointmentDuration);\r\n      continue;\r\n    }\r\n\r\n    // Check if this slot overlaps with any existing appointment\r\n    const overlappingAppointment = validAppointments.find(apt => {\r\n      if (apt.status === 'cancelled') return false;\r\n      \r\n      const aptStart = new Date(apt.start_time);\r\n      const aptEnd = new Date(apt.end_time);\r\n      \r\n      return (\r\n        (currentTime >= aptStart && currentTime < aptEnd) ||\r\n        (slotEnd > aptStart && slotEnd <= aptEnd) ||\r\n        (currentTime <= aptStart && slotEnd >= aptEnd)\r\n      );\r\n    });\r\n\r\n    // Check if this slot overlaps with any blocked time\r\n    const overlappingBlock = validBlocks.find(block => {\r\n      const blockStart = new Date(block.start_time);\r\n      const blockEnd = new Date(block.end_time);\r\n      \r\n      return (\r\n        (currentTime >= blockStart && currentTime < blockEnd) ||\r\n        (slotEnd > blockStart && slotEnd <= blockEnd) ||\r\n        (currentTime <= blockStart && slotEnd >= blockEnd)\r\n      );\r\n    });\r\n\r\n    // Determine slot type and availability\r\n    let type: 'available' | 'appointment' | 'blocked' = 'available';\r\n    let isAvailable = true;\r\n    let isBlocked = false;\r\n    let appointment = undefined;\r\n    let block = undefined;\r\n\r\n    if (overlappingAppointment) {\r\n      type = 'appointment';\r\n      isAvailable = false;\r\n      appointment = overlappingAppointment;\r\n    } else if (overlappingBlock) {\r\n      type = 'blocked';\r\n      isAvailable = false;\r\n      isBlocked = true;\r\n      block = overlappingBlock;\r\n    }\r\n\r\n    // Create slot\r\n    const slot: TimeSlot = {\r\n      id: `slot-${currentTime.getTime()}`,\r\n      start_time: new Date(currentTime),\r\n      end_time: new Date(slotEnd),\r\n      isAvailable,\r\n      isBlocked,\r\n      type,\r\n      appointment,\r\n      block\r\n    };\r\n\r\n    slots.push(slot);\r\n\r\n    // Move to next slot\r\n    currentTime = addMinutes(currentTime, appointmentDuration);\r\n  }\r\n\r\n  return slots;\r\n}\r\n\r\n/**\r\n * Check if a time slot is available for booking\r\n */\r\nexport function isSlotAvailable(\r\n  startTime: Date,\r\n  endTime: Date,\r\n  appointments: any[] = [],\r\n  blocks: any[] = [],\r\n  clinicSettings?: ClinicSettings | LegacyClinicSettings\r\n): boolean {\r\n  // Ensure appointments and blocks are arrays\r\n  const validAppointments = Array.isArray(appointments) ? appointments : [];\r\n  const validBlocks = Array.isArray(blocks) ? blocks : [];\r\n  \r\n  // Check if the time is within working hours and not in break intervals\r\n  if (clinicSettings && 'weekly_schedule' in clinicSettings) {\r\n    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];\r\n    const dayOfWeek = startTime.getDay();\r\n    const dayName = dayNames[dayOfWeek] as keyof typeof clinicSettings.weekly_schedule;\r\n    const dayConfig = clinicSettings.weekly_schedule[dayName];\r\n    \r\n    // Check if day is enabled\r\n    if (!dayConfig || !dayConfig.enabled) {\r\n      return false;\r\n    }\r\n    \r\n    // Check if time is within working hours\r\n    const [startHour, startMinute] = dayConfig.working_hours_start.split(':').map(Number);\r\n    const [endHour, endMinute] = dayConfig.working_hours_end.split(':').map(Number);\r\n    \r\n    const workingStart = new Date(startTime);\r\n    workingStart.setHours(startHour, startMinute, 0, 0);\r\n    \r\n    const workingEnd = new Date(startTime);\r\n    workingEnd.setHours(endHour, endMinute, 0, 0);\r\n    \r\n    if (startTime < workingStart || endTime > workingEnd) {\r\n      return false;\r\n    }\r\n    \r\n    // Check if time overlaps with break intervals\r\n    const isInBreakInterval = dayConfig.break_intervals.some(breakInterval => {\r\n      const [breakStartHour, breakStartMinute] = breakInterval.start.split(':').map(Number);\r\n      const [breakEndHour, breakEndMinute] = breakInterval.end.split(':').map(Number);\r\n      \r\n      const breakStart = new Date(startTime);\r\n      breakStart.setHours(breakStartHour, breakStartMinute, 0, 0);\r\n      \r\n      const breakEnd = new Date(startTime);\r\n      breakEnd.setHours(breakEndHour, breakEndMinute, 0, 0);\r\n      \r\n      return (\r\n        (startTime >= breakStart && startTime < breakEnd) ||\r\n        (endTime > breakStart && endTime <= breakEnd) ||\r\n        (startTime <= breakStart && endTime >= breakEnd)\r\n      );\r\n    });\r\n    \r\n    if (isInBreakInterval) {\r\n      return false;\r\n    }\r\n  }\r\n  \r\n  // Check appointments\r\n  const hasAppointmentConflict = validAppointments.some(apt => {\r\n    if (apt.status === 'cancelled') return false;\r\n    \r\n    const aptStart = new Date(apt.start_time);\r\n    const aptEnd = new Date(apt.end_time);\r\n    \r\n    return (\r\n      (startTime >= aptStart && startTime < aptEnd) ||\r\n      (endTime > aptStart && endTime <= aptEnd) ||\r\n      (startTime <= aptStart && endTime >= aptEnd)\r\n    );\r\n  });\r\n\r\n  // Check blocks\r\n  const hasBlockConflict = validBlocks.some(block => {\r\n    const blockStart = new Date(block.start_time);\r\n    const blockEnd = new Date(block.end_time);\r\n    \r\n    return (\r\n      (startTime >= blockStart && startTime < blockEnd) ||\r\n      (endTime > blockStart && endTime <= blockEnd) ||\r\n      (startTime <= blockStart && endTime >= blockEnd)\r\n    );\r\n  });\r\n\r\n  return !hasAppointmentConflict && !hasBlockConflict;\r\n}\r\n\r\n/**\r\n * Format time for display in time slots\r\n */\r\nexport function formatSlotTime(date: Date): string {\r\n  return format(date, 'HH:mm');\r\n}\r\n\r\n/**\r\n * Get slot duration in minutes\r\n */\r\nexport function getSlotDuration(slot: TimeSlot): number {\r\n  return Math.round((slot.end_time.getTime() - slot.start_time.getTime()) / (1000 * 60));\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;;AA8CO,SAAS,kBACd,IAAU,EACV,cAAqD,EACrD,eAAsB,EAAE,EACxB,SAAgB,EAAE;IAElB,MAAM,QAAoB,EAAE;IAE5B,4CAA4C;IAC5C,MAAM,oBAAoB,MAAM,OAAO,CAAC,gBAAgB,eAAe,EAAE;IACzE,MAAM,cAAc,MAAM,OAAO,CAAC,UAAU,SAAS,EAAE;IAEvD,uDAAuD;IACvD,MAAM,cAAc,qBAAqB;IAEzC,IAAI,YAA+B;IACnC,IAAI,sBAAsB;IAE1B,IAAI,aAAa;QACf,MAAM,cAAc;QACpB,sBAAsB,YAAY,4BAA4B,IAAI;QAElE,wBAAwB;QACxB,MAAM,WAAW;YAAC;YAAU;YAAU;YAAW;YAAa;YAAY;YAAU;SAAW;QAC/F,MAAM,YAAY,KAAK,MAAM;QAC7B,MAAM,UAAU,QAAQ,CAAC,UAAU;QAEnC,YAAY,YAAY,eAAe,CAAC,QAAQ;QAEhD,4CAA4C;QAC5C,IAAI,CAAC,aAAa,CAAC,UAAU,OAAO,EAAE;YACpC,OAAO;QACT;IACF,OAAO;QACL,gBAAgB;QAChB,MAAM,iBAAiB;QACvB,sBAAsB,eAAe,4BAA4B,IAAI;QAErE,0DAA0D;QAC1D,MAAM,YAAY,KAAK,MAAM;QAC7B,MAAM,cAAc,MAAM,OAAO,CAAC,eAAe,YAAY,IAAI,eAAe,YAAY,GAAG;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE;QAE9G,IAAI,CAAC,eAAe,0BAA0B,IAAI,CAAC,YAAY,QAAQ,CAAC,YAAY;YAClF,OAAO;QACT;QAEA,sCAAsC;QACtC,YAAY;YACV,SAAS;YACT,qBAAqB,eAAe,mBAAmB,IAAI;YAC3D,mBAAmB,eAAe,iBAAiB,IAAI;YACvD,iBAAiB,EAAE;QACrB;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,sBAAsB;IACtB,MAAM,CAAC,WAAW,YAAY,GAAG,UAAU,mBAAmB,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;IAC9E,MAAM,CAAC,SAAS,UAAU,GAAG,UAAU,iBAAiB,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;IAExE,yCAAyC;IACzC,MAAM,eAAe,IAAI,KAAK;IAC9B,aAAa,QAAQ,CAAC,WAAW,aAAa,GAAG;IAEjD,MAAM,aAAa,IAAI,KAAK;IAC5B,WAAW,QAAQ,CAAC,SAAS,WAAW,GAAG;IAE3C,+CAA+C;IAC/C,IAAI,cAAc,IAAI,KAAK;IAE3B,MAAO,cAAc,WAAY;QAC/B,MAAM,UAAU,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,aAAa;QAExC,4DAA4D;QAC5D,IAAI,UAAU,YAAY;YACxB;QACF;QAEA,gDAAgD;QAChD,MAAM,oBAAoB,UAAU,eAAe,CAAC,IAAI,CAAC,CAAA;YACvD,MAAM,CAAC,gBAAgB,iBAAiB,GAAG,cAAc,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;YAC9E,MAAM,CAAC,cAAc,eAAe,GAAG,cAAc,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;YAExE,MAAM,aAAa,IAAI,KAAK;YAC5B,WAAW,QAAQ,CAAC,gBAAgB,kBAAkB,GAAG;YAEzD,MAAM,WAAW,IAAI,KAAK;YAC1B,SAAS,QAAQ,CAAC,cAAc,gBAAgB,GAAG;YAEnD,OACE,AAAC,eAAe,cAAc,cAAc,YAC3C,UAAU,cAAc,WAAW,YACnC,eAAe,cAAc,WAAW;QAE7C;QAEA,iDAAiD;QACjD,IAAI,mBAAmB;YACrB,cAAc,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,aAAa;YACtC;QACF;QAEA,4DAA4D;QAC5D,MAAM,yBAAyB,kBAAkB,IAAI,CAAC,CAAA;YACpD,IAAI,IAAI,MAAM,KAAK,aAAa,OAAO;YAEvC,MAAM,WAAW,IAAI,KAAK,IAAI,UAAU;YACxC,MAAM,SAAS,IAAI,KAAK,IAAI,QAAQ;YAEpC,OACE,AAAC,eAAe,YAAY,cAAc,UACzC,UAAU,YAAY,WAAW,UACjC,eAAe,YAAY,WAAW;QAE3C;QAEA,oDAAoD;QACpD,MAAM,mBAAmB,YAAY,IAAI,CAAC,CAAA;YACxC,MAAM,aAAa,IAAI,KAAK,MAAM,UAAU;YAC5C,MAAM,WAAW,IAAI,KAAK,MAAM,QAAQ;YAExC,OACE,AAAC,eAAe,cAAc,cAAc,YAC3C,UAAU,cAAc,WAAW,YACnC,eAAe,cAAc,WAAW;QAE7C;QAEA,uCAAuC;QACvC,IAAI,OAAgD;QACpD,IAAI,cAAc;QAClB,IAAI,YAAY;QAChB,IAAI,cAAc;QAClB,IAAI,QAAQ;QAEZ,IAAI,wBAAwB;YAC1B,OAAO;YACP,cAAc;YACd,cAAc;QAChB,OAAO,IAAI,kBAAkB;YAC3B,OAAO;YACP,cAAc;YACd,YAAY;YACZ,QAAQ;QACV;QAEA,cAAc;QACd,MAAM,OAAiB;YACrB,IAAI,CAAC,KAAK,EAAE,YAAY,OAAO,IAAI;YACnC,YAAY,IAAI,KAAK;YACrB,UAAU,IAAI,KAAK;YACnB;YACA;YACA;YACA;YACA;QACF;QAEA,MAAM,IAAI,CAAC;QAEX,oBAAoB;QACpB,cAAc,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IACxC;IAEA,OAAO;AACT;AAKO,SAAS,gBACd,SAAe,EACf,OAAa,EACb,eAAsB,EAAE,EACxB,SAAgB,EAAE,EAClB,cAAsD;IAEtD,4CAA4C;IAC5C,MAAM,oBAAoB,MAAM,OAAO,CAAC,gBAAgB,eAAe,EAAE;IACzE,MAAM,cAAc,MAAM,OAAO,CAAC,UAAU,SAAS,EAAE;IAEvD,uEAAuE;IACvE,IAAI,kBAAkB,qBAAqB,gBAAgB;QACzD,MAAM,WAAW;YAAC;YAAU;YAAU;YAAW;YAAa;YAAY;YAAU;SAAW;QAC/F,MAAM,YAAY,UAAU,MAAM;QAClC,MAAM,UAAU,QAAQ,CAAC,UAAU;QACnC,MAAM,YAAY,eAAe,eAAe,CAAC,QAAQ;QAEzD,0BAA0B;QAC1B,IAAI,CAAC,aAAa,CAAC,UAAU,OAAO,EAAE;YACpC,OAAO;QACT;QAEA,wCAAwC;QACxC,MAAM,CAAC,WAAW,YAAY,GAAG,UAAU,mBAAmB,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;QAC9E,MAAM,CAAC,SAAS,UAAU,GAAG,UAAU,iBAAiB,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;QAExE,MAAM,eAAe,IAAI,KAAK;QAC9B,aAAa,QAAQ,CAAC,WAAW,aAAa,GAAG;QAEjD,MAAM,aAAa,IAAI,KAAK;QAC5B,WAAW,QAAQ,CAAC,SAAS,WAAW,GAAG;QAE3C,IAAI,YAAY,gBAAgB,UAAU,YAAY;YACpD,OAAO;QACT;QAEA,8CAA8C;QAC9C,MAAM,oBAAoB,UAAU,eAAe,CAAC,IAAI,CAAC,CAAA;YACvD,MAAM,CAAC,gBAAgB,iBAAiB,GAAG,cAAc,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;YAC9E,MAAM,CAAC,cAAc,eAAe,GAAG,cAAc,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;YAExE,MAAM,aAAa,IAAI,KAAK;YAC5B,WAAW,QAAQ,CAAC,gBAAgB,kBAAkB,GAAG;YAEzD,MAAM,WAAW,IAAI,KAAK;YAC1B,SAAS,QAAQ,CAAC,cAAc,gBAAgB,GAAG;YAEnD,OACE,AAAC,aAAa,cAAc,YAAY,YACvC,UAAU,cAAc,WAAW,YACnC,aAAa,cAAc,WAAW;QAE3C;QAEA,IAAI,mBAAmB;YACrB,OAAO;QACT;IACF;IAEA,qBAAqB;IACrB,MAAM,yBAAyB,kBAAkB,IAAI,CAAC,CAAA;QACpD,IAAI,IAAI,MAAM,KAAK,aAAa,OAAO;QAEvC,MAAM,WAAW,IAAI,KAAK,IAAI,UAAU;QACxC,MAAM,SAAS,IAAI,KAAK,IAAI,QAAQ;QAEpC,OACE,AAAC,aAAa,YAAY,YAAY,UACrC,UAAU,YAAY,WAAW,UACjC,aAAa,YAAY,WAAW;IAEzC;IAEA,eAAe;IACf,MAAM,mBAAmB,YAAY,IAAI,CAAC,CAAA;QACxC,MAAM,aAAa,IAAI,KAAK,MAAM,UAAU;QAC5C,MAAM,WAAW,IAAI,KAAK,MAAM,QAAQ;QAExC,OACE,AAAC,aAAa,cAAc,YAAY,YACvC,UAAU,cAAc,WAAW,YACnC,aAAa,cAAc,WAAW;IAE3C;IAEA,OAAO,CAAC,0BAA0B,CAAC;AACrC;AAKO,SAAS,eAAe,IAAU;IACvC,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;AACtB;AAKO,SAAS,gBAAgB,IAAc;IAC5C,OAAO,KAAK,KAAK,CAAC,CAAC,KAAK,QAAQ,CAAC,OAAO,KAAK,KAAK,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;AACtF", "debugId": null}}, {"offset": {"line": 1626, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/status-colors.ts"], "sourcesContent": ["/**\n * Centralized status color system for appointment statuses\n * Ensures consistency across all components and views\n */\n\nexport type AppointmentStatus = \n  | 'scheduled' \n  | 'confirmed' \n  | 'in_progress' \n  | 'completed' \n  | 'cancelled' \n  | 'no_show'\n\nexport interface StatusColorConfig {\n  background: string\n  border: string\n  text: string\n  badge: 'default' | 'secondary' | 'destructive' | 'outline'\n  cardBackground: string\n  cardBorder: string\n  cardHover: string\n}\n\n/**\n * Unified status color configuration\n * Used across calendar, cards, badges, and all status displays\n */\nexport const STATUS_COLORS: Record<AppointmentStatus, StatusColorConfig> = {\n  scheduled: {\n    background: '#3b82f6', // blue-500\n    border: '#3b82f6',\n    text: '#ffffff',\n    badge: 'outline',\n    cardBackground: 'bg-blue-50',\n    cardBorder: 'border-blue-200',\n    cardHover: 'hover:bg-blue-100'\n  },\n  confirmed: {\n    background: '#10b981', // emerald-500\n    border: '#10b981',\n    text: '#ffffff',\n    badge: 'default',\n    cardBackground: 'bg-emerald-50',\n    cardBorder: 'border-emerald-200',\n    cardHover: 'hover:bg-emerald-100'\n  },\n  in_progress: {\n    background: '#f59e0b', // amber-500\n    border: '#f59e0b',\n    text: '#ffffff',\n    badge: 'default',\n    cardBackground: 'bg-amber-50',\n    cardBorder: 'border-amber-200',\n    cardHover: 'hover:bg-amber-100'\n  },\n  completed: {\n    background: '#6b7280', // gray-500\n    border: '#6b7280',\n    text: '#ffffff',\n    badge: 'secondary',\n    cardBackground: 'bg-gray-50',\n    cardBorder: 'border-gray-200',\n    cardHover: 'hover:bg-gray-100'\n  },\n  cancelled: {\n    background: '#ef4444', // red-500\n    border: '#ef4444',\n    text: '#ffffff',\n    badge: 'destructive',\n    cardBackground: 'bg-red-50',\n    cardBorder: 'border-red-200',\n    cardHover: 'hover:bg-red-100'\n  },\n  no_show: {\n    background: '#8b5cf6', // violet-500\n    border: '#8b5cf6',\n    text: '#ffffff',\n    badge: 'outline',\n    cardBackground: 'bg-violet-50',\n    cardBorder: 'border-violet-200',\n    cardHover: 'hover:bg-violet-100'\n  }\n}\n\n/**\n * Get status color for FullCalendar events\n */\nexport function getCalendarStatusColor(status: AppointmentStatus): string {\n  return STATUS_COLORS[status]?.background || STATUS_COLORS.scheduled.background\n}\n\n/**\n * Get badge variant for status\n */\nexport function getStatusBadgeVariant(status: AppointmentStatus): 'default' | 'secondary' | 'destructive' | 'outline' {\n  return STATUS_COLORS[status]?.badge || 'outline'\n}\n\n/**\n * Get card styling classes for status\n */\nexport function getStatusCardClasses(status: AppointmentStatus): string {\n  const config = STATUS_COLORS[status] || STATUS_COLORS.scheduled\n  return `${config.cardBackground} ${config.cardBorder} ${config.cardHover}`\n}\n\n/**\n * Get status text in Portuguese\n */\nexport function getStatusTextBR(status: AppointmentStatus): string {\n  const statusMap: Record<AppointmentStatus, string> = {\n    'scheduled': 'Agendado',\n    'confirmed': 'Confirmado',\n    'in_progress': 'Em Andamento',\n    'completed': 'Concluído',\n    'cancelled': 'Cancelado',\n    'no_show': 'Faltou'\n  }\n  \n  return statusMap[status] || status\n}\n\n/**\n * Get all available statuses for dropdowns/selects\n */\nexport function getAllStatuses(): Array<{ value: AppointmentStatus; label: string; color: string }> {\n  return Object.entries(STATUS_COLORS).map(([status, config]) => ({\n    value: status as AppointmentStatus,\n    label: getStatusTextBR(status as AppointmentStatus),\n    color: config.background\n  }))\n}\n\n/**\n * Check if status allows certain actions\n */\nexport function canStartConsultation(status: AppointmentStatus): boolean {\n  return ['scheduled', 'confirmed'].includes(status)\n}\n\nexport function canCompleteConsultation(status: AppointmentStatus): boolean {\n  return status === 'in_progress'\n}\n\nexport function canEditAppointment(status: AppointmentStatus): boolean {\n  return !['completed', 'cancelled'].includes(status)\n}\n\nexport function canCancelAppointment(status: AppointmentStatus): boolean {\n  return !['completed', 'cancelled'].includes(status)\n}\n\n/**\n * Get status priority for sorting (lower number = higher priority)\n */\nexport function getStatusPriority(status: AppointmentStatus): number {\n  const priorities: Record<AppointmentStatus, number> = {\n    'in_progress': 1,\n    'confirmed': 2,\n    'scheduled': 3,\n    'completed': 4,\n    'no_show': 5,\n    'cancelled': 6\n  }\n  \n  return priorities[status] || 999\n}\n\n/**\n * Get next logical status for workflow\n */\nexport function getNextStatus(currentStatus: AppointmentStatus): AppointmentStatus | null {\n  const workflow: Record<AppointmentStatus, AppointmentStatus | null> = {\n    'scheduled': 'confirmed',\n    'confirmed': 'in_progress',\n    'in_progress': 'completed',\n    'completed': null,\n    'cancelled': null,\n    'no_show': null\n  }\n  \n  return workflow[currentStatus] || null\n}\n\n/**\n * Check if status transition is valid\n */\nexport function isValidStatusTransition(from: AppointmentStatus, to: AppointmentStatus): boolean {\n  const validTransitions: Record<AppointmentStatus, AppointmentStatus[]> = {\n    'scheduled': ['confirmed', 'cancelled', 'no_show'],\n    'confirmed': ['in_progress', 'cancelled', 'no_show'],\n    'in_progress': ['completed', 'cancelled'],\n    'completed': [], // Final state\n    'cancelled': [], // Final state\n    'no_show': [] // Final state\n  }\n  \n  return validTransitions[from]?.includes(to) || false\n}\n\n/**\n * Get status icon name (for lucide-react icons)\n */\nexport function getStatusIcon(status: AppointmentStatus): string {\n  const icons: Record<AppointmentStatus, string> = {\n    'scheduled': 'Calendar',\n    'confirmed': 'CheckCircle',\n    'in_progress': 'Clock',\n    'completed': 'CheckCircle2',\n    'cancelled': 'XCircle',\n    'no_show': 'AlertCircle'\n  }\n  \n  return icons[status] || 'Calendar'\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;AAwBM,MAAM,gBAA8D;IACzE,WAAW;QACT,YAAY;QACZ,QAAQ;QACR,MAAM;QACN,OAAO;QACP,gBAAgB;QAChB,YAAY;QACZ,WAAW;IACb;IACA,WAAW;QACT,YAAY;QACZ,QAAQ;QACR,MAAM;QACN,OAAO;QACP,gBAAgB;QAChB,YAAY;QACZ,WAAW;IACb;IACA,aAAa;QACX,YAAY;QACZ,QAAQ;QACR,MAAM;QACN,OAAO;QACP,gBAAgB;QAChB,YAAY;QACZ,WAAW;IACb;IACA,WAAW;QACT,YAAY;QACZ,QAAQ;QACR,MAAM;QACN,OAAO;QACP,gBAAgB;QAChB,YAAY;QACZ,WAAW;IACb;IACA,WAAW;QACT,YAAY;QACZ,QAAQ;QACR,MAAM;QACN,OAAO;QACP,gBAAgB;QAChB,YAAY;QACZ,WAAW;IACb;IACA,SAAS;QACP,YAAY;QACZ,QAAQ;QACR,MAAM;QACN,OAAO;QACP,gBAAgB;QAChB,YAAY;QACZ,WAAW;IACb;AACF;AAKO,SAAS,uBAAuB,MAAyB;IAC9D,OAAO,aAAa,CAAC,OAAO,EAAE,cAAc,cAAc,SAAS,CAAC,UAAU;AAChF;AAKO,SAAS,sBAAsB,MAAyB;IAC7D,OAAO,aAAa,CAAC,OAAO,EAAE,SAAS;AACzC;AAKO,SAAS,qBAAqB,MAAyB;IAC5D,MAAM,SAAS,aAAa,CAAC,OAAO,IAAI,cAAc,SAAS;IAC/D,OAAO,GAAG,OAAO,cAAc,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC,CAAC,EAAE,OAAO,SAAS,EAAE;AAC5E;AAKO,SAAS,gBAAgB,MAAyB;IACvD,MAAM,YAA+C;QACnD,aAAa;QACb,aAAa;QACb,eAAe;QACf,aAAa;QACb,aAAa;QACb,WAAW;IACb;IAEA,OAAO,SAAS,CAAC,OAAO,IAAI;AAC9B;AAKO,SAAS;IACd,OAAO,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,QAAQ,OAAO,GAAK,CAAC;YAC9D,OAAO;YACP,OAAO,gBAAgB;YACvB,OAAO,OAAO,UAAU;QAC1B,CAAC;AACH;AAKO,SAAS,qBAAqB,MAAyB;IAC5D,OAAO;QAAC;QAAa;KAAY,CAAC,QAAQ,CAAC;AAC7C;AAEO,SAAS,wBAAwB,MAAyB;IAC/D,OAAO,WAAW;AACpB;AAEO,SAAS,mBAAmB,MAAyB;IAC1D,OAAO,CAAC;QAAC;QAAa;KAAY,CAAC,QAAQ,CAAC;AAC9C;AAEO,SAAS,qBAAqB,MAAyB;IAC5D,OAAO,CAAC;QAAC;QAAa;KAAY,CAAC,QAAQ,CAAC;AAC9C;AAKO,SAAS,kBAAkB,MAAyB;IACzD,MAAM,aAAgD;QACpD,eAAe;QACf,aAAa;QACb,aAAa;QACb,aAAa;QACb,WAAW;QACX,aAAa;IACf;IAEA,OAAO,UAAU,CAAC,OAAO,IAAI;AAC/B;AAKO,SAAS,cAAc,aAAgC;IAC5D,MAAM,WAAgE;QACpE,aAAa;QACb,aAAa;QACb,eAAe;QACf,aAAa;QACb,aAAa;QACb,WAAW;IACb;IAEA,OAAO,QAAQ,CAAC,cAAc,IAAI;AACpC;AAKO,SAAS,wBAAwB,IAAuB,EAAE,EAAqB;IACpF,MAAM,mBAAmE;QACvE,aAAa;YAAC;YAAa;YAAa;SAAU;QAClD,aAAa;YAAC;YAAe;YAAa;SAAU;QACpD,eAAe;YAAC;YAAa;SAAY;QACzC,aAAa,EAAE;QACf,aAAa,EAAE;QACf,WAAW,EAAE,CAAC,cAAc;IAC9B;IAEA,OAAO,gBAAgB,CAAC,KAAK,EAAE,SAAS,OAAO;AACjD;AAKO,SAAS,cAAc,MAAyB;IACrD,MAAM,QAA2C;QAC/C,aAAa;QACb,aAAa;QACb,eAAe;QACf,aAAa;QACb,aAAa;QACb,WAAW;IACb;IAEA,OAAO,KAAK,CAAC,OAAO,IAAI;AAC1B", "debugId": null}}, {"offset": {"line": 1814, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/TimeSlotCard.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Clock, Lock, Plus, User, Calendar, CheckCircle, Play, X } from 'lucide-react';\r\nimport { formatSlotTime, type TimeSlot } from '@/lib/time-slots';\r\nimport { getStatusBadgeVariant, getStatusTextBR, type AppointmentStatus } from '@/lib/status-colors';\r\n\r\ninterface TimeSlotCardProps {\r\n  slot: TimeSlot;\r\n  onSlotClick: (slot: TimeSlot) => void;\r\n  onBlockSlot: (slot: TimeSlot, event: React.MouseEvent) => void;\r\n  onUnblockSlot?: (slot: TimeSlot, event: React.MouseEvent) => void;\r\n  onConfirmAppointment?: (appointment: any, event: React.MouseEvent) => void;\r\n  onStartConsultation?: (appointment: any, event: React.MouseEvent) => void;\r\n  onCancelAppointment?: (appointment: any, event: React.MouseEvent) => void;\r\n  canBlockSlots: boolean;\r\n  canStartConsultation: boolean;\r\n  className?: string;\r\n}\r\n\r\nconst TimeSlotCard: React.FC<TimeSlotCardProps> = ({\r\n  slot,\r\n  onSlotClick,\r\n  onBlockSlot,\r\n  onUnblockSlot,\r\n  onConfirmAppointment,\r\n  onStartConsultation,\r\n  onCancelAppointment,\r\n  canBlockSlots,\r\n  canStartConsultation,\r\n  className = ''\r\n}) => {\r\n  const handleCardClick = () => {\r\n    if (slot.isAvailable) {\r\n      onSlotClick(slot);\r\n    } else if (slot.appointment) {\r\n      // Para consultas \"em atendimento\" ou \"concluído\", redirecionar para prontuário se possível\r\n      if (['in_progress', 'completed'].includes(slot.appointment.status) && canStartConsultation) {\r\n        // O redirecionamento será tratado no onSlotClick da página pai\r\n        onSlotClick(slot);\r\n      } else {\r\n        // Para outros status, abrir formulário de edição\r\n        onSlotClick(slot);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleBlockClick = (event: React.MouseEvent) => {\r\n    event.stopPropagation(); // Prevent triggering the card click\r\n    onBlockSlot(slot, event);\r\n  };\r\n\r\n  const handleUnblockClick = (event: React.MouseEvent) => {\r\n    event.stopPropagation(); // Prevent triggering the card click\r\n    if (onUnblockSlot) {\r\n      onUnblockSlot(slot, event);\r\n    }\r\n  };\r\n\r\n  const handleConfirmClick = (event: React.MouseEvent) => {\r\n    event.stopPropagation();\r\n    if (onConfirmAppointment && slot.appointment) {\r\n      onConfirmAppointment(slot.appointment, event);\r\n    }\r\n  };\r\n\r\n  const handleStartClick = (event: React.MouseEvent) => {\r\n    event.stopPropagation();\r\n    if (onStartConsultation && slot.appointment) {\r\n      onStartConsultation(slot.appointment, event);\r\n    }\r\n  };\r\n\r\n  const handleCancelClick = (event: React.MouseEvent) => {\r\n    event.stopPropagation();\r\n    if (onCancelAppointment && slot.appointment) {\r\n      onCancelAppointment(slot.appointment, event);\r\n    }\r\n  };\r\n\r\n  const getCardClasses = () => {\r\n    const baseClasses = \"transition-all duration-200 hover:shadow-md cursor-pointer\";\r\n    \r\n    if (slot.type === 'available') {\r\n      return `${baseClasses} border-2 border-dashed border-green-300 hover:border-green-400 hover:bg-green-50 ${className}`;\r\n    } else if (slot.type === 'appointment') {\r\n      return `${baseClasses} border-blue-300 bg-blue-50 hover:bg-blue-100 ${className}`;\r\n    } else if (slot.type === 'blocked') {\r\n      return `${baseClasses} border-red-300 bg-red-50 hover:bg-red-100 ${className}`;\r\n    }\r\n    \r\n    return baseClasses;\r\n  };\r\n\r\n  const getSlotIcon = () => {\r\n    if (slot.type === 'available') {\r\n      return <Plus className=\"h-4 w-4 text-green-600\" />;\r\n    } else if (slot.type === 'appointment') {\r\n      return <User className=\"h-4 w-4 text-blue-600\" />;\r\n    } else if (slot.type === 'blocked') {\r\n      return <Lock className=\"h-4 w-4 text-red-600\" />;\r\n    }\r\n    return <Clock className=\"h-4 w-4\" />;\r\n  };\r\n\r\n  const getSlotTitle = () => {\r\n    if (slot.type === 'available') {\r\n      return 'Horário Disponível';\r\n    } else if (slot.type === 'appointment' && slot.appointment) {\r\n      return slot.appointment.title || 'Consulta Agendada';\r\n    } else if (slot.type === 'blocked' && slot.block) {\r\n      return slot.block.reason || 'Horário Bloqueado';\r\n    }\r\n    return 'Slot';\r\n  };\r\n\r\n  const getSlotSubtitle = () => {\r\n    if (slot.type === 'appointment' && slot.appointment) {\r\n      return slot.appointment.patient_name || 'Paciente';\r\n    } else if (slot.type === 'blocked') {\r\n      return 'Indisponível';\r\n    }\r\n    return 'Clique para agendar';\r\n  };\r\n\r\n  const getAppointmentActions = () => {\r\n    if (slot.type !== 'appointment' || !slot.appointment) {\r\n      return null;\r\n    }\r\n\r\n    const appointment = slot.appointment;\r\n    const status = appointment.status;\r\n\r\n    const actions = [];\r\n\r\n    // Botão \"Confirmar\" para consultas agendadas (todos os usuários)\r\n    if (status === 'scheduled') {\r\n      actions.push(\r\n        <Button\r\n          key=\"confirm\"\r\n          size=\"sm\"\r\n          variant=\"outline\"\r\n          className=\"h-6 px-2 text-xs text-green-600 hover:text-green-700 hover:bg-green-50\"\r\n          onClick={handleConfirmClick}\r\n          title=\"Confirmar consulta\"\r\n        >\r\n          <CheckCircle className=\"h-3 w-3 mr-1\" />\r\n          Confirmar\r\n        </Button>\r\n      );\r\n    }\r\n\r\n    // Botão \"Iniciar\" para consultas confirmadas (só médicos)\r\n    if (status === 'confirmed' && canStartConsultation) {\r\n      actions.push(\r\n        <Button\r\n          key=\"start\"\r\n          size=\"sm\"\r\n          variant=\"outline\"\r\n          className=\"h-6 px-2 text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50\"\r\n          onClick={handleStartClick}\r\n          title=\"Iniciar atendimento\"\r\n        >\r\n          <Play className=\"h-3 w-3 mr-1\" />\r\n          Iniciar\r\n        </Button>\r\n      );\r\n    }\r\n\r\n    // Botão \"Cancelar\" para consultas que ainda não foram iniciadas\r\n    if (['scheduled', 'confirmed'].includes(status)) {\r\n      actions.push(\r\n        <Button\r\n          key=\"cancel\"\r\n          size=\"sm\"\r\n          variant=\"outline\"\r\n          className=\"h-6 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n          onClick={handleCancelClick}\r\n          title=\"Cancelar consulta\"\r\n        >\r\n          <X className=\"h-3 w-3\" />\r\n        </Button>\r\n      );\r\n    }\r\n\r\n    return actions.length > 0 ? (\r\n      <div className=\"flex flex-col space-y-1\">\r\n        {actions}\r\n      </div>\r\n    ) : null;\r\n  };\r\n\r\n  return (\r\n    <Card className={getCardClasses()} onClick={handleCardClick}>\r\n      <CardContent className=\"p-3\">\r\n        <div className=\"flex items-start justify-between\">\r\n          <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\r\n            {getSlotIcon()}\r\n            <div className=\"flex-1 min-w-0\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <span className=\"text-sm font-medium\">\r\n                  {formatSlotTime(slot.start_time)} - {formatSlotTime(slot.end_time)}\r\n                </span>\r\n                {slot.type === 'appointment' && slot.appointment && (\r\n                  <Badge\r\n                    variant={getStatusBadgeVariant(slot.appointment.status as AppointmentStatus)}\r\n                    className=\"text-xs\"\r\n                  >\r\n                    {getStatusTextBR(slot.appointment.status as AppointmentStatus)}\r\n                  </Badge>\r\n                )}\r\n              </div>\r\n              <p className=\"text-xs text-muted-foreground truncate mt-1\">\r\n                {getSlotTitle()}\r\n              </p>\r\n              {(slot.type === 'appointment' || slot.type === 'blocked') && (\r\n                <p className=\"text-xs text-muted-foreground truncate\">\r\n                  {getSlotSubtitle()}\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Action buttons */}\r\n          <div className=\"flex flex-col space-y-1 ml-2\">\r\n            {/* Appointment status actions */}\r\n            {getAppointmentActions()}\r\n            \r\n            {/* Block/Unblock actions for available and blocked slots */}\r\n            {slot.type === 'available' && canBlockSlots && (\r\n              <Button\r\n                size=\"sm\"\r\n                variant=\"outline\"\r\n                className=\"h-6 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                onClick={handleBlockClick}\r\n                title=\"Bloquear horário\"\r\n              >\r\n                <Lock className=\"h-3 w-3\" />\r\n              </Button>\r\n            )}\r\n            \r\n            {slot.type === 'blocked' && canBlockSlots && slot.block && onUnblockSlot && (\r\n              <Button\r\n                size=\"sm\"\r\n                variant=\"outline\"\r\n                className=\"h-6 px-2 text-xs text-green-600 hover:text-green-700 hover:bg-green-50\"\r\n                onClick={handleUnblockClick}\r\n                title=\"Desbloquear horário\"\r\n              >\r\n                <Lock className=\"h-3 w-3\" />\r\n              </Button>\r\n            )}\r\n            \r\n            {slot.type === 'available' && (\r\n              <div className=\"text-xs text-center text-green-600 font-medium\">\r\n                Livre\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Additional info for appointments */}\r\n        {slot.type === 'appointment' && slot.appointment && slot.appointment.healthcare_professional_name && (\r\n          <div className=\"mt-2 pt-2 border-t border-blue-200\">\r\n            <p className=\"text-xs text-blue-600 truncate\">\r\n              Dr(a). {slot.appointment.healthcare_professional_name}\r\n            </p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Special indicator for in-progress and completed appointments */}\r\n        {slot.type === 'appointment' && slot.appointment && \r\n         ['in_progress', 'completed'].includes(slot.appointment.status) && canStartConsultation && (\r\n          <div className=\"mt-2 pt-2 border-t border-purple-200\">\r\n            <p className=\"text-xs text-purple-600 text-center font-medium\">\r\n              {slot.appointment.status === 'in_progress' ? \r\n                '🩺 Clique para acessar o prontuário' : \r\n                '📋 Clique para ver o prontuário'\r\n              }\r\n            </p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Click hint for available slots */}\r\n        {slot.type === 'available' && (\r\n          <div className=\"mt-2 pt-2 border-t border-green-200\">\r\n            <p className=\"text-xs text-green-600 text-center\">\r\n              Clique para agendar consulta\r\n            </p>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default TimeSlotCard;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;;;AAeA,MAAM,eAA4C,CAAC,EACjD,IAAI,EACJ,WAAW,EACX,WAAW,EACX,aAAa,EACb,oBAAoB,EACpB,mBAAmB,EACnB,mBAAmB,EACnB,aAAa,EACb,oBAAoB,EACpB,YAAY,EAAE,EACf;IACC,MAAM,kBAAkB;QACtB,IAAI,KAAK,WAAW,EAAE;YACpB,YAAY;QACd,OAAO,IAAI,KAAK,WAAW,EAAE;YAC3B,2FAA2F;YAC3F,IAAI;gBAAC;gBAAe;aAAY,CAAC,QAAQ,CAAC,KAAK,WAAW,CAAC,MAAM,KAAK,sBAAsB;gBAC1F,+DAA+D;gBAC/D,YAAY;YACd,OAAO;gBACL,iDAAiD;gBACjD,YAAY;YACd;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe,IAAI,oCAAoC;QAC7D,YAAY,MAAM;IACpB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,eAAe,IAAI,oCAAoC;QAC7D,IAAI,eAAe;YACjB,cAAc,MAAM;QACtB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,eAAe;QACrB,IAAI,wBAAwB,KAAK,WAAW,EAAE;YAC5C,qBAAqB,KAAK,WAAW,EAAE;QACzC;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe;QACrB,IAAI,uBAAuB,KAAK,WAAW,EAAE;YAC3C,oBAAoB,KAAK,WAAW,EAAE;QACxC;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,eAAe;QACrB,IAAI,uBAAuB,KAAK,WAAW,EAAE;YAC3C,oBAAoB,KAAK,WAAW,EAAE;QACxC;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,cAAc;QAEpB,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,OAAO,GAAG,YAAY,kFAAkF,EAAE,WAAW;QACvH,OAAO,IAAI,KAAK,IAAI,KAAK,eAAe;YACtC,OAAO,GAAG,YAAY,8CAA8C,EAAE,WAAW;QACnF,OAAO,IAAI,KAAK,IAAI,KAAK,WAAW;YAClC,OAAO,GAAG,YAAY,2CAA2C,EAAE,WAAW;QAChF;QAEA,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,qBAAO,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QACzB,OAAO,IAAI,KAAK,IAAI,KAAK,eAAe;YACtC,qBAAO,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QACzB,OAAO,IAAI,KAAK,IAAI,KAAK,WAAW;YAClC,qBAAO,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QACzB;QACA,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;IAC1B;IAEA,MAAM,eAAe;QACnB,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,OAAO;QACT,OAAO,IAAI,KAAK,IAAI,KAAK,iBAAiB,KAAK,WAAW,EAAE;YAC1D,OAAO,KAAK,WAAW,CAAC,KAAK,IAAI;QACnC,OAAO,IAAI,KAAK,IAAI,KAAK,aAAa,KAAK,KAAK,EAAE;YAChD,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;QAC9B;QACA,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,IAAI,KAAK,IAAI,KAAK,iBAAiB,KAAK,WAAW,EAAE;YACnD,OAAO,KAAK,WAAW,CAAC,YAAY,IAAI;QAC1C,OAAO,IAAI,KAAK,IAAI,KAAK,WAAW;YAClC,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,wBAAwB;QAC5B,IAAI,KAAK,IAAI,KAAK,iBAAiB,CAAC,KAAK,WAAW,EAAE;YACpD,OAAO;QACT;QAEA,MAAM,cAAc,KAAK,WAAW;QACpC,MAAM,SAAS,YAAY,MAAM;QAEjC,MAAM,UAAU,EAAE;QAElB,iEAAiE;QACjE,IAAI,WAAW,aAAa;YAC1B,QAAQ,IAAI,eACV,6LAAC,qIAAA,CAAA,SAAM;gBAEL,MAAK;gBACL,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,OAAM;;kCAEN,6LAAC,8NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBAAiB;;eAPpC;;;;;QAWV;QAEA,0DAA0D;QAC1D,IAAI,WAAW,eAAe,sBAAsB;YAClD,QAAQ,IAAI,eACV,6LAAC,qIAAA,CAAA,SAAM;gBAEL,MAAK;gBACL,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,OAAM;;kCAEN,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAiB;;eAP7B;;;;;QAWV;QAEA,gEAAgE;QAChE,IAAI;YAAC;YAAa;SAAY,CAAC,QAAQ,CAAC,SAAS;YAC/C,QAAQ,IAAI,eACV,6LAAC,qIAAA,CAAA,SAAM;gBAEL,MAAK;gBACL,SAAQ;gBACR,WAAU;gBACV,SAAS;gBACT,OAAM;0BAEN,cAAA,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;eAPT;;;;;QAUV;QAEA,OAAO,QAAQ,MAAM,GAAG,kBACtB,6LAAC;YAAI,WAAU;sBACZ;;;;;mBAED;IACN;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW;QAAkB,SAAS;kBAC1C,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ;8CACD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;;wDACb,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,UAAU;wDAAE;wDAAI,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,QAAQ;;;;;;;gDAElE,KAAK,IAAI,KAAK,iBAAiB,KAAK,WAAW,kBAC9C,6LAAC,oIAAA,CAAA,QAAK;oDACJ,SAAS,CAAA,GAAA,iIAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK,WAAW,CAAC,MAAM;oDACtD,WAAU;8DAET,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,WAAW,CAAC,MAAM;;;;;;;;;;;;sDAI9C,6LAAC;4CAAE,WAAU;sDACV;;;;;;wCAEF,CAAC,KAAK,IAAI,KAAK,iBAAiB,KAAK,IAAI,KAAK,SAAS,mBACtD,6LAAC;4CAAE,WAAU;sDACV;;;;;;;;;;;;;;;;;;sCAOT,6LAAC;4BAAI,WAAU;;gCAEZ;gCAGA,KAAK,IAAI,KAAK,eAAe,+BAC5B,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS;oCACT,OAAM;8CAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;gCAInB,KAAK,IAAI,KAAK,aAAa,iBAAiB,KAAK,KAAK,IAAI,+BACzD,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS;oCACT,OAAM;8CAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;gCAInB,KAAK,IAAI,KAAK,6BACb,6LAAC;oCAAI,WAAU;8CAAiD;;;;;;;;;;;;;;;;;;gBAQrE,KAAK,IAAI,KAAK,iBAAiB,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,4BAA4B,kBAC/F,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAiC;4BACpC,KAAK,WAAW,CAAC,4BAA4B;;;;;;;;;;;;gBAM1D,KAAK,IAAI,KAAK,iBAAiB,KAAK,WAAW,IAC/C;oBAAC;oBAAe;iBAAY,CAAC,QAAQ,CAAC,KAAK,WAAW,CAAC,MAAM,KAAK,sCACjE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCACV,KAAK,WAAW,CAAC,MAAM,KAAK,gBAC3B,wCACA;;;;;;;;;;;gBAOP,KAAK,IAAI,KAAK,6BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;;;;;;;;;;;;AAQ9D;KAlRM;uCAoRS", "debugId": null}}, {"offset": {"line": 2244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/date-utils.ts"], "sourcesContent": ["import { format, parseISO, isValid } from 'date-fns';\nimport { ptBR } from 'date-fns/locale';\n\n/**\n * Utility functions for Brazilian date and time formatting\n */\n\n/**\n * Format date to Brazilian format (DD/MM/YYYY)\n */\nexport function formatDateBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'dd/MM/yyyy', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format time to Brazilian format (HH:mm)\n */\nexport function formatTimeBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'HH:mm', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format date and time to Brazilian format (DD/MM/YYYY HH:mm)\n */\nexport function formatDateTimeBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'dd/MM/yyyy HH:mm', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format date to Brazilian format with day of week (Segunda, DD/MM/YYYY)\n */\nexport function formatDateWithDayBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'EEEE, dd/MM/yyyy', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format date for input fields (YYYY-MM-DD)\n */\nexport function formatDateForInput(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'yyyy-MM-dd');\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format datetime for input fields (YYYY-MM-DDTHH:mm)\n * This function handles Brazilian timezone properly to avoid UTC conversion issues\n */\nexport function formatDateTimeForInput(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n\n    // Create a new date in local timezone to avoid UTC conversion\n    const localDate = new Date(dateObj.getTime() - (dateObj.getTimezoneOffset() * 60000));\n    return format(localDate, \"yyyy-MM-dd'T'HH:mm\");\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Convert local datetime to ISO string without timezone conversion\n * This prevents the 3-hour offset issue in Brazilian timezone\n */\nexport function toLocalISOString(date: Date): string {\n  try {\n    if (!isValid(date)) return '';\n\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    const hours = String(date.getHours()).padStart(2, '0');\n    const minutes = String(date.getMinutes()).padStart(2, '0');\n\n    return `${year}-${month}-${day}T${hours}:${minutes}`;\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Get relative time in Portuguese (hoje, ontem, amanhã, etc.)\n */\nexport function getRelativeTimeBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    \n    const today = new Date();\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    \n    const dateStr = format(dateObj, 'yyyy-MM-dd');\n    const todayStr = format(today, 'yyyy-MM-dd');\n    const tomorrowStr = format(tomorrow, 'yyyy-MM-dd');\n    const yesterdayStr = format(yesterday, 'yyyy-MM-dd');\n    \n    if (dateStr === todayStr) return 'Hoje';\n    if (dateStr === tomorrowStr) return 'Amanhã';\n    if (dateStr === yesterdayStr) return 'Ontem';\n    \n    return formatDateBR(dateObj);\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Parse Brazilian date format (DD/MM/YYYY) to Date object\n */\nexport function parseBRDate(dateStr: string): Date | null {\n  try {\n    const parts = dateStr.split('/');\n    if (parts.length !== 3) return null;\n    \n    const day = parseInt(parts[0], 10);\n    const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed\n    const year = parseInt(parts[2], 10);\n    \n    const date = new Date(year, month, day);\n    if (!isValid(date)) return null;\n    \n    return date;\n  } catch {\n    return null;\n  }\n}\n\n/**\n * Get appointment status in Portuguese\n */\nexport function getAppointmentStatusBR(status: string): string {\n  const statusMap: Record<string, string> = {\n    'scheduled': 'Agendado',\n    'confirmed': 'Confirmado',\n    'in_progress': 'Em Andamento',\n    'completed': 'Concluído',\n    'cancelled': 'Cancelado',\n    'no_show': 'Faltou'\n  };\n  \n  return statusMap[status] || status;\n}\n\n/**\n * Get appointment type in Portuguese\n */\nexport function getAppointmentTypeBR(type: string): string {\n  const typeMap: Record<string, string> = {\n    'consultation': 'Consulta',\n    'return': 'Retorno',\n    'teleconsultation': 'Teleconsulta',\n    'procedure': 'Procedimento',\n    'exam': 'Exame'\n  };\n  \n  return typeMap[type] || type;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;;;AASO,SAAS,aAAa,IAAmB;IAC9C,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,cAAc;YAAE,QAAQ,oJAAA,CAAA,OAAI;QAAC;IACtD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,aAAa,IAAmB;IAC9C,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,SAAS;YAAE,QAAQ,oJAAA,CAAA,OAAI;QAAC;IACjD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,iBAAiB,IAAmB;IAClD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;YAAE,QAAQ,oJAAA,CAAA,OAAI;QAAC;IAC5D,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,oBAAoB,IAAmB;IACrD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;YAAE,QAAQ,oJAAA,CAAA,OAAI;QAAC;IAC5D,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,mBAAmB,IAAmB;IACpD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;IACzB,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAMO,SAAS,uBAAuB,IAAmB;IACxD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAE9B,8DAA8D;QAC9D,MAAM,YAAY,IAAI,KAAK,QAAQ,OAAO,KAAM,QAAQ,iBAAiB,KAAK;QAC9E,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;IAC3B,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAMO,SAAS,iBAAiB,IAAU;IACzC,IAAI;QACF,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;QAE3B,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,QAAQ,OAAO,KAAK,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;QACtD,MAAM,MAAM,OAAO,KAAK,OAAO,IAAI,QAAQ,CAAC,GAAG;QAC/C,MAAM,QAAQ,OAAO,KAAK,QAAQ,IAAI,QAAQ,CAAC,GAAG;QAClD,MAAM,UAAU,OAAO,KAAK,UAAU,IAAI,QAAQ,CAAC,GAAG;QAEtD,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS;IACtD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,kBAAkB,IAAmB;IACnD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAE9B,MAAM,QAAQ,IAAI;QAClB,MAAM,WAAW,IAAI,KAAK;QAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;QACtC,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;QAChC,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,OAAO;QAC/B,MAAM,cAAc,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;QACrC,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;QAEvC,IAAI,YAAY,UAAU,OAAO;QACjC,IAAI,YAAY,aAAa,OAAO;QACpC,IAAI,YAAY,cAAc,OAAO;QAErC,OAAO,aAAa;IACtB,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,YAAY,OAAe;IACzC,IAAI;QACF,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAC5B,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAE/B,MAAM,MAAM,SAAS,KAAK,CAAC,EAAE,EAAE;QAC/B,MAAM,QAAQ,SAAS,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,qBAAqB;QAC/D,MAAM,OAAO,SAAS,KAAK,CAAC,EAAE,EAAE;QAEhC,MAAM,OAAO,IAAI,KAAK,MAAM,OAAO;QACnC,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;QAE3B,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,uBAAuB,MAAc;IACnD,MAAM,YAAoC;QACxC,aAAa;QACb,aAAa;QACb,eAAe;QACf,aAAa;QACb,aAAa;QACb,WAAW;IACb;IAEA,OAAO,SAAS,CAAC,OAAO,IAAI;AAC9B;AAKO,SAAS,qBAAqB,IAAY;IAC/C,MAAM,UAAkC;QACtC,gBAAgB;QAChB,UAAU;QACV,oBAAoB;QACpB,aAAa;QACb,QAAQ;IACV;IAEA,OAAO,OAAO,CAAC,KAAK,IAAI;AAC1B", "debugId": null}}, {"offset": {"line": 2405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/FullCalendarView.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport React, { useRef, useEffect, useState, useCallback } from 'react';\r\nimport FullCalendar from '@fullcalendar/react';\r\nimport dayGridPlugin from '@fullcalendar/daygrid';\r\nimport timeGridPlugin from '@fullcalendar/timegrid';\r\nimport interactionPlugin from '@fullcalendar/interaction';\r\nimport { EventInput, DateSelectArg, EventClickArg, EventDropArg } from '@fullcalendar/core';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { Calendar, Filter, Plus } from 'lucide-react';\r\nimport { useToast } from '@/hooks/use-toast';\r\nimport { format } from 'date-fns';\r\nimport { getCalendarStatusColor, type AppointmentStatus } from '@/lib/status-colors';\r\n\r\ntype AppointmentBlock = {\r\n  id: string;\r\n  start_time: string;\r\n  end_time: string;\r\n  healthcare_professional_id: string | null;\r\n  reason: string | null;\r\n  created_by: string;\r\n  created_at: string;\r\n};\r\n\r\ntype Appointment = {\r\n  id: string;\r\n  title: string;\r\n  description: string | null;\r\n  patient_id: string;\r\n  patient_name?: string;\r\n  healthcare_professional_id: string | null;\r\n  healthcare_professional_name?: string;\r\n  start_time: string;\r\n  end_time: string;\r\n  type: string;\r\n  status: string;\r\n  total_price: number | null;\r\n};\r\n\r\ntype HealthcareProfessional = {\r\n  id: string;\r\n  name: string;\r\n  specialty: string | null;\r\n  is_active: boolean;\r\n};\r\n\r\ninterface FullCalendarViewProps {\r\n  appointments: Appointment[];\r\n  appointmentBlocks?: AppointmentBlock[];\r\n  healthcareProfessionals: HealthcareProfessional[];\r\n  onAppointmentCreate: (selectInfo: DateSelectArg) => void;\r\n  onAppointmentClick: (appointment: Appointment) => void;\r\n  onAppointmentUpdate: (appointmentId: string, newStart: Date, newEnd: Date) => Promise<void>;\r\n  loading?: boolean;\r\n  selectedProfessional?: string;\r\n  onSelectedProfessionalChange?: (professionalId: string) => void;\r\n  onUnblockSlot?: (block: AppointmentBlock) => void;\r\n}\r\n\r\nconst FullCalendarView: React.FC<FullCalendarViewProps> = ({\r\n  appointments,\r\n  appointmentBlocks = [],\r\n  healthcareProfessionals,\r\n  onAppointmentCreate,\r\n  onAppointmentClick,\r\n  onAppointmentUpdate,\r\n  loading = false,\r\n  selectedProfessional: externalSelectedProfessional,\r\n  onSelectedProfessionalChange,\r\n  onUnblockSlot\r\n}) => {\r\n  const calendarRef = useRef<FullCalendar>(null);\r\n  const [internalSelectedProfessional, setInternalSelectedProfessional] = useState<string>('all');\r\n  const [currentView, setCurrentView] = useState<string>('timeGridWeek');\r\n  const [isMobile, setIsMobile] = useState<boolean>(false);\r\n  const [isInitialized, setIsInitialized] = useState<boolean>(false);\r\n  const { toast } = useToast();\r\n\r\n  // Initialize component after data is loaded\r\n  useEffect(() => {\r\n    if (!loading && appointments && healthcareProfessionals) {\r\n      setIsInitialized(true);\r\n    }\r\n  }, [loading, appointments, healthcareProfessionals]);\r\n\r\n  // Use external selectedProfessional if provided, otherwise use internal state\r\n  const selectedProfessional = externalSelectedProfessional ?? internalSelectedProfessional;\r\n  \r\n  const handleSelectedProfessionalChange = (professionalId: string) => {\r\n    if (onSelectedProfessionalChange) {\r\n      onSelectedProfessionalChange(professionalId);\r\n    } else {\r\n      setInternalSelectedProfessional(professionalId);\r\n    }\r\n  };\r\n\r\n  // Detect mobile screen size\r\n  useEffect(() => {\r\n    const checkMobile = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n      // Auto-switch to day view on mobile\r\n      if (window.innerWidth < 768 && currentView === 'timeGridWeek') {\r\n        setCurrentView('timeGridDay');\r\n      }\r\n    };\r\n\r\n    checkMobile();\r\n    window.addEventListener('resize', checkMobile);\r\n    return () => window.removeEventListener('resize', checkMobile);\r\n  }, [currentView]);\r\n\r\n  // Filter appointment blocks by selected healthcare professional\r\n  const filteredAppointmentBlocks = React.useMemo(() => {\r\n    if (selectedProfessional === 'all') {\r\n      return appointmentBlocks;\r\n    }\r\n    return appointmentBlocks.filter(block => block.healthcare_professional_id === selectedProfessional);\r\n  }, [appointmentBlocks, selectedProfessional]);\r\n\r\n  // Filter appointments by selected healthcare professional\r\n  const filteredAppointments = React.useMemo(() => {\r\n    if (selectedProfessional === 'all') {\r\n      return appointments;\r\n    }\r\n    return appointments.filter(apt => apt.healthcare_professional_id === selectedProfessional);\r\n  }, [appointments, selectedProfessional]);\r\n\r\n  // Convert appointments and blocks to FullCalendar events\r\n  const events: EventInput[] = React.useMemo(() => {\r\n    const appointmentEvents = filteredAppointments.map(appointment => ({\r\n      id: appointment.id,\r\n      title: appointment.title,\r\n      start: appointment.start_time,\r\n      end: appointment.end_time,\r\n      backgroundColor: getCalendarStatusColor(appointment.status as AppointmentStatus),\r\n      borderColor: getCalendarStatusColor(appointment.status as AppointmentStatus),\r\n      textColor: '#ffffff',\r\n      extendedProps: {\r\n        type: 'appointment',\r\n        appointment,\r\n        patient_name: appointment.patient_name,\r\n        healthcare_professional_name: appointment.healthcare_professional_name,\r\n        status: appointment.status,\r\n        description: appointment.description\r\n      }\r\n    }));\r\n\r\n    const blockEvents = filteredAppointmentBlocks.map(block => ({\r\n      id: `block-${block.id}`,\r\n      title: '🔒 Horário Bloqueado',\r\n      start: block.start_time,\r\n      end: block.end_time,\r\n      backgroundColor: '#dc2626', // Red color for blocked slots\r\n      borderColor: '#dc2626',\r\n      textColor: '#ffffff',\r\n      extendedProps: {\r\n        type: 'block',\r\n        block,\r\n        reason: block.reason\r\n      }\r\n    }));\r\n\r\n    return [...appointmentEvents, ...blockEvents];\r\n  }, [filteredAppointments, filteredAppointmentBlocks]);\r\n\r\n  const handleDateSelect = useCallback((selectInfo: DateSelectArg) => {\r\n    if (onAppointmentCreate) {\r\n      onAppointmentCreate(selectInfo);\r\n    }\r\n  }, [onAppointmentCreate]);\r\n\r\n  const handleEventClick = useCallback((clickInfo: EventClickArg) => {\r\n    const eventType = clickInfo.event.extendedProps.type;\r\n    \r\n    if (eventType === 'appointment') {\r\n      const appointment = clickInfo.event.extendedProps.appointment as Appointment;\r\n      if (onAppointmentClick && appointment) {\r\n        onAppointmentClick(appointment);\r\n      }\r\n    } else if (eventType === 'block') {\r\n      const block = clickInfo.event.extendedProps.block as AppointmentBlock;\r\n      if (block && onUnblockSlot) {\r\n        onUnblockSlot(block);\r\n      }\r\n    }\r\n  }, [onAppointmentClick, onUnblockSlot]);\r\n\r\n  const handleEventDrop = useCallback(async (dropInfo: EventDropArg) => {\r\n    try {\r\n      const appointmentId = dropInfo.event.id;\r\n      const newStart = dropInfo.event.start;\r\n      const newEnd = dropInfo.event.end;\r\n\r\n      if (!appointmentId || !newStart || !newEnd || !onAppointmentUpdate) {\r\n        dropInfo.revert();\r\n        return;\r\n      }\r\n\r\n      await onAppointmentUpdate(appointmentId, newStart, newEnd);\r\n\r\n      toast({\r\n        title: \"Sucesso!\",\r\n        description: \"Consulta reagendada com sucesso.\",\r\n      });\r\n    } catch (error) {\r\n      console.error('Error updating appointment:', error);\r\n      dropInfo.revert();\r\n      toast({\r\n        title: \"Erro\",\r\n        description: \"Erro ao reagendar consulta.\",\r\n        variant: \"destructive\"\r\n      });\r\n    }\r\n  }, [onAppointmentUpdate, toast]);\r\n\r\n  const handleEventResize = useCallback(async (resizeInfo: any) => {\r\n    try {\r\n      const appointmentId = resizeInfo.event.id;\r\n      const newStart = resizeInfo.event.start;\r\n      const newEnd = resizeInfo.event.end;\r\n\r\n      if (!appointmentId || !newStart || !newEnd || !onAppointmentUpdate) {\r\n        resizeInfo.revert();\r\n        return;\r\n      }\r\n\r\n      await onAppointmentUpdate(appointmentId, newStart, newEnd);\r\n\r\n      toast({\r\n        title: \"Sucesso!\",\r\n        description: \"Duração da consulta atualizada com sucesso.\",\r\n      });\r\n    } catch (error) {\r\n      console.error('Error resizing appointment:', error);\r\n      resizeInfo.revert();\r\n      toast({\r\n        title: \"Erro\",\r\n        description: \"Erro ao atualizar duração da consulta.\",\r\n        variant: \"destructive\"\r\n      });\r\n    }\r\n  }, [onAppointmentUpdate, toast]);\r\n\r\n  const handleViewChange = (view: string) => {\r\n    setCurrentView(view);\r\n    const calendarApi = calendarRef.current?.getApi();\r\n    if (calendarApi) {\r\n      calendarApi.changeView(view);\r\n    }\r\n  };\r\n\r\n  const handleToday = () => {\r\n    const calendarApi = calendarRef.current?.getApi();\r\n    if (calendarApi) {\r\n      calendarApi.today();\r\n    }\r\n  };\r\n\r\n  const handlePrev = () => {\r\n    const calendarApi = calendarRef.current?.getApi();\r\n    if (calendarApi) {\r\n      calendarApi.prev();\r\n    }\r\n  };\r\n\r\n  const handleNext = () => {\r\n    const calendarApi = calendarRef.current?.getApi();\r\n    if (calendarApi) {\r\n      calendarApi.next();\r\n    }\r\n  };\r\n\r\n  if (loading || !isInitialized) {\r\n    return (\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center text-lg\">\r\n            <Calendar className=\"mr-2 h-5 w-5 text-primary\" />\r\n            Agenda Completa\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"flex items-center justify-center h-96\">\r\n            <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"></div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n          <CardTitle className=\"flex items-center text-lg\">\r\n            <Calendar className=\"mr-2 h-5 w-5 text-primary\" />\r\n            Agenda Completa\r\n          </CardTitle>\r\n\r\n          <div className=\"flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4\">\r\n            {/* Healthcare Professional Filter */}\r\n            <div className=\"flex items-center gap-2 min-w-0\">\r\n              <Filter className=\"h-4 w-4 text-muted-foreground flex-shrink-0\" />\r\n              <Select value={selectedProfessional} onValueChange={handleSelectedProfessionalChange}>\r\n                <SelectTrigger className=\"w-[180px] sm:w-[200px]\">\r\n                  <SelectValue placeholder=\"Selecionar médico\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">Todos os médicos</SelectItem>\r\n                  {healthcareProfessionals\r\n                    .filter(prof => prof.is_active)\r\n                    .map(professional => (\r\n                      <SelectItem key={professional.id} value={professional.id}>\r\n                        <div className=\"flex flex-col\">\r\n                          <span className=\"font-medium\">{professional.name}</span>\r\n                          {professional.specialty && (\r\n                            <span className=\"text-xs text-muted-foreground\">{professional.specialty}</span>\r\n                          )}\r\n                        </div>\r\n                      </SelectItem>\r\n                    ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n\r\n            {/* View Controls */}\r\n            <div className=\"flex items-center gap-2\">\r\n              <Button variant=\"outline\" size=\"sm\" onClick={handleToday}>\r\n                Hoje\r\n              </Button>\r\n              <div className=\"flex items-center\">\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handlePrev}>‹</Button>\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleNext}>›</Button>\r\n              </div>\r\n              <Select value={currentView} onValueChange={handleViewChange}>\r\n                <SelectTrigger className=\"w-[120px]\">\r\n                  <SelectValue />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"timeGridDay\">Dia</SelectItem>\r\n                  <SelectItem value=\"timeGridWeek\">Semana</SelectItem>\r\n                  <SelectItem value=\"dayGridMonth\">Mês</SelectItem>\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <div className=\"w-full\">\r\n          {isInitialized && (\r\n            <FullCalendar\r\n              ref={calendarRef}\r\n              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}\r\n              headerToolbar={false}\r\n              initialView={currentView}\r\n              editable={true}\r\n              selectable={true}\r\n              selectMirror={true}\r\n              dayMaxEvents={true}\r\n              weekends={true}\r\n              events={events}\r\n              select={handleDateSelect}\r\n              eventClick={handleEventClick}\r\n              eventDrop={handleEventDrop}\r\n              eventResize={handleEventResize}\r\n              locale=\"pt-br\"\r\n              timeZone=\"local\"\r\n              height=\"auto\"\r\n              eventContent={(eventInfo) => {\r\n                const isAppointment = eventInfo.event.extendedProps.type === 'appointment';\r\n                const patientName = eventInfo.event.extendedProps.patient_name;\r\n                const professionalName = eventInfo.event.extendedProps.healthcare_professional_name;\r\n                const status = eventInfo.event.extendedProps.status;\r\n                const startTime = eventInfo.event.start;\r\n\r\n                return (\r\n                  <div className=\"p-1 text-xs overflow-hidden\">\r\n                    <div className=\"font-medium truncate\">\r\n                      {eventInfo.event.title}\r\n                    </div>\r\n                    {patientName && !isMobile && (\r\n                      <div className=\"text-xs opacity-90 truncate\">\r\n                        {patientName}\r\n                      </div>\r\n                    )}\r\n                    {professionalName && !isMobile && (\r\n                      <div className=\"text-xs opacity-75 truncate\">\r\n                        {professionalName}\r\n                      </div>\r\n                    )}\r\n                    {isMobile && startTime && (\r\n                      <div className=\"text-xs opacity-75 truncate\">\r\n                        {format(startTime, 'HH:mm')}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                );\r\n              }}\r\n            />\r\n          )}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default FullCalendarView;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;;;AA6DA,MAAM,mBAAoD,CAAC,EACzD,YAAY,EACZ,oBAAoB,EAAE,EACtB,uBAAuB,EACvB,mBAAmB,EACnB,kBAAkB,EAClB,mBAAmB,EACnB,UAAU,KAAK,EACf,sBAAsB,4BAA4B,EAClD,4BAA4B,EAC5B,aAAa,EACd;;IACC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAgB;IACzC,MAAM,CAAC,8BAA8B,gCAAgC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC5D,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,WAAW,gBAAgB,yBAAyB;gBACvD,iBAAiB;YACnB;QACF;qCAAG;QAAC;QAAS;QAAc;KAAwB;IAEnD,8EAA8E;IAC9E,MAAM,uBAAuB,gCAAgC;IAE7D,MAAM,mCAAmC,CAAC;QACxC,IAAI,8BAA8B;YAChC,6BAA6B;QAC/B,OAAO;YACL,gCAAgC;QAClC;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;0DAAc;oBAClB,YAAY,OAAO,UAAU,GAAG;oBAChC,oCAAoC;oBACpC,IAAI,OAAO,UAAU,GAAG,OAAO,gBAAgB,gBAAgB;wBAC7D,eAAe;oBACjB;gBACF;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;8CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;qCAAG;QAAC;KAAY;IAEhB,gEAAgE;IAChE,MAAM,4BAA4B,6JAAA,CAAA,UAAK,CAAC,OAAO;+DAAC;YAC9C,IAAI,yBAAyB,OAAO;gBAClC,OAAO;YACT;YACA,OAAO,kBAAkB,MAAM;uEAAC,CAAA,QAAS,MAAM,0BAA0B,KAAK;;QAChF;8DAAG;QAAC;QAAmB;KAAqB;IAE5C,0DAA0D;IAC1D,MAAM,uBAAuB,6JAAA,CAAA,UAAK,CAAC,OAAO;0DAAC;YACzC,IAAI,yBAAyB,OAAO;gBAClC,OAAO;YACT;YACA,OAAO,aAAa,MAAM;kEAAC,CAAA,MAAO,IAAI,0BAA0B,KAAK;;QACvE;yDAAG;QAAC;QAAc;KAAqB;IAEvC,yDAAyD;IACzD,MAAM,SAAuB,6JAAA,CAAA,UAAK,CAAC,OAAO;4CAAC;YACzC,MAAM,oBAAoB,qBAAqB,GAAG;sEAAC,CAAA,cAAe,CAAC;wBACjE,IAAI,YAAY,EAAE;wBAClB,OAAO,YAAY,KAAK;wBACxB,OAAO,YAAY,UAAU;wBAC7B,KAAK,YAAY,QAAQ;wBACzB,iBAAiB,CAAA,GAAA,iIAAA,CAAA,yBAAsB,AAAD,EAAE,YAAY,MAAM;wBAC1D,aAAa,CAAA,GAAA,iIAAA,CAAA,yBAAsB,AAAD,EAAE,YAAY,MAAM;wBACtD,WAAW;wBACX,eAAe;4BACb,MAAM;4BACN;4BACA,cAAc,YAAY,YAAY;4BACtC,8BAA8B,YAAY,4BAA4B;4BACtE,QAAQ,YAAY,MAAM;4BAC1B,aAAa,YAAY,WAAW;wBACtC;oBACF,CAAC;;YAED,MAAM,cAAc,0BAA0B,GAAG;gEAAC,CAAA,QAAS,CAAC;wBAC1D,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;wBACvB,OAAO;wBACP,OAAO,MAAM,UAAU;wBACvB,KAAK,MAAM,QAAQ;wBACnB,iBAAiB;wBACjB,aAAa;wBACb,WAAW;wBACX,eAAe;4BACb,MAAM;4BACN;4BACA,QAAQ,MAAM,MAAM;wBACtB;oBACF,CAAC;;YAED,OAAO;mBAAI;mBAAsB;aAAY;QAC/C;2CAAG;QAAC;QAAsB;KAA0B;IAEpD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACpC,IAAI,qBAAqB;gBACvB,oBAAoB;YACtB;QACF;yDAAG;QAAC;KAAoB;IAExB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACpC,MAAM,YAAY,UAAU,KAAK,CAAC,aAAa,CAAC,IAAI;YAEpD,IAAI,cAAc,eAAe;gBAC/B,MAAM,cAAc,UAAU,KAAK,CAAC,aAAa,CAAC,WAAW;gBAC7D,IAAI,sBAAsB,aAAa;oBACrC,mBAAmB;gBACrB;YACF,OAAO,IAAI,cAAc,SAAS;gBAChC,MAAM,QAAQ,UAAU,KAAK,CAAC,aAAa,CAAC,KAAK;gBACjD,IAAI,SAAS,eAAe;oBAC1B,cAAc;gBAChB;YACF;QACF;yDAAG;QAAC;QAAoB;KAAc;IAEtC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,OAAO;YACzC,IAAI;gBACF,MAAM,gBAAgB,SAAS,KAAK,CAAC,EAAE;gBACvC,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK;gBACrC,MAAM,SAAS,SAAS,KAAK,CAAC,GAAG;gBAEjC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC,qBAAqB;oBAClE,SAAS,MAAM;oBACf;gBACF;gBAEA,MAAM,oBAAoB,eAAe,UAAU;gBAEnD,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,SAAS,MAAM;gBACf,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;YACF;QACF;wDAAG;QAAC;QAAqB;KAAM;IAE/B,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OAAO;YAC3C,IAAI;gBACF,MAAM,gBAAgB,WAAW,KAAK,CAAC,EAAE;gBACzC,MAAM,WAAW,WAAW,KAAK,CAAC,KAAK;gBACvC,MAAM,SAAS,WAAW,KAAK,CAAC,GAAG;gBAEnC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC,qBAAqB;oBAClE,WAAW,MAAM;oBACjB;gBACF;gBAEA,MAAM,oBAAoB,eAAe,UAAU;gBAEnD,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,WAAW,MAAM;gBACjB,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;YACF;QACF;0DAAG;QAAC;QAAqB;KAAM;IAE/B,MAAM,mBAAmB,CAAC;QACxB,eAAe;QACf,MAAM,cAAc,YAAY,OAAO,EAAE;QACzC,IAAI,aAAa;YACf,YAAY,UAAU,CAAC;QACzB;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,cAAc,YAAY,OAAO,EAAE;QACzC,IAAI,aAAa;YACf,YAAY,KAAK;QACnB;IACF;IAEA,MAAM,aAAa;QACjB,MAAM,cAAc,YAAY,OAAO,EAAE;QACzC,IAAI,aAAa;YACf,YAAY,IAAI;QAClB;IACF;IAEA,MAAM,aAAa;QACjB,MAAM,cAAc,YAAY,OAAO,EAAE;QACzC,IAAI,aAAa;YACf,YAAY,IAAI;QAClB;IACF;IAEA,IAAI,WAAW,CAAC,eAAe;QAC7B,qBACE,6LAAC,mIAAA,CAAA,OAAI;;8BACH,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAA8B;;;;;;;;;;;;8BAItD,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAsB,eAAe;;8DAClD,6LAAC,qIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;wDACvB,wBACE,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAC7B,GAAG,CAAC,CAAA,6BACH,6LAAC,qIAAA,CAAA,aAAU;gEAAuB,OAAO,aAAa,EAAE;0EACtD,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAe,aAAa,IAAI;;;;;;wEAC/C,aAAa,SAAS,kBACrB,6LAAC;4EAAK,WAAU;sFAAiC,aAAa,SAAS;;;;;;;;;;;;+DAJ5D,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;8CAc1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,SAAS;sDAAa;;;;;;sDAG1D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS;8DAAY;;;;;;8DACvD,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS;8DAAY;;;;;;;;;;;;sDAEzD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAa,eAAe;;8DACzC,6LAAC,qIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAc;;;;;;sEAChC,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAe;;;;;;sEACjC,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAI,WAAU;8BACZ,+BACC,6LAAC,2JAAA,CAAA,UAAY;wBACX,KAAK;wBACL,SAAS;4BAAC,qJAAA,CAAA,UAAa;4BAAE,sJAAA,CAAA,UAAc;4BAAE,yJAAA,CAAA,UAAiB;yBAAC;wBAC3D,eAAe;wBACf,aAAa;wBACb,UAAU;wBACV,YAAY;wBACZ,cAAc;wBACd,cAAc;wBACd,UAAU;wBACV,QAAQ;wBACR,QAAQ;wBACR,YAAY;wBACZ,WAAW;wBACX,aAAa;wBACb,QAAO;wBACP,UAAS;wBACT,QAAO;wBACP,cAAc,CAAC;4BACb,MAAM,gBAAgB,UAAU,KAAK,CAAC,aAAa,CAAC,IAAI,KAAK;4BAC7D,MAAM,cAAc,UAAU,KAAK,CAAC,aAAa,CAAC,YAAY;4BAC9D,MAAM,mBAAmB,UAAU,KAAK,CAAC,aAAa,CAAC,4BAA4B;4BACnF,MAAM,SAAS,UAAU,KAAK,CAAC,aAAa,CAAC,MAAM;4BACnD,MAAM,YAAY,UAAU,KAAK,CAAC,KAAK;4BAEvC,qBACE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,UAAU,KAAK,CAAC,KAAK;;;;;;oCAEvB,eAAe,CAAC,0BACf,6LAAC;wCAAI,WAAU;kDACZ;;;;;;oCAGJ,oBAAoB,CAAC,0BACpB,6LAAC;wCAAI,WAAU;kDACZ;;;;;;oCAGJ,YAAY,2BACX,6LAAC;wCAAI,WAAU;kDACZ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;;;;;;;;;;;;wBAK7B;;;;;;;;;;;;;;;;;;;;;;AAOd;GA1VM;;QAiBc,+HAAA,CAAA,WAAQ;;;KAjBtB;uCA4VS", "debugId": null}}, {"offset": {"line": 3072, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface TextareaProps\r\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\r\n  ({ className, ...props }, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nTextarea.displayName = \"Textarea\"\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3295, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/checkbox.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { Check } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Checkbox = React.forwardRef<\r\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <CheckboxPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <CheckboxPrimitive.Indicator\r\n      className={cn(\"flex items-center justify-center text-current\")}\r\n    >\r\n      <Check className=\"h-4 w-4\" />\r\n    </CheckboxPrimitive.Indicator>\r\n  </CheckboxPrimitive.Root>\r\n))\r\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\r\n\r\nexport { Checkbox }\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3346, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/radio-group.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\r\nimport { Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst RadioGroup = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Root\r\n      className={cn(\"grid gap-2\", className)}\r\n      {...props}\r\n      ref={ref}\r\n    />\r\n  )\r\n})\r\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\r\n\r\nconst RadioGroupItem = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Item\r\n      ref={ref}\r\n      className={cn(\r\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\r\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\r\n      </RadioGroupPrimitive.Indicator>\r\n    </RadioGroupPrimitive.Item>\r\n  )\r\n})\r\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\r\n\r\nexport { RadioGroup, RadioGroupItem }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;;AACA,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4OACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;;AACA,eAAe,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/popover.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Popover = PopoverPrimitive.Root\r\n\r\nconst PopoverTrigger = PopoverPrimitive.Trigger\r\n\r\nconst PopoverContent = React.forwardRef<\r\n  React.ElementRef<typeof PopoverPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\r\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\r\n  <PopoverPrimitive.Portal>\r\n    <PopoverPrimitive.Content\r\n      ref={ref}\r\n      align={align}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </PopoverPrimitive.Portal>\r\n))\r\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent }\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/datetime-input.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Input } from '@/components/ui/input';\nimport { Button } from '@/components/ui/button';\nimport { Calendar } from '@/components/ui/calendar';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { Calendar as CalendarIcon, Clock } from 'lucide-react';\nimport { format, parse, isValid } from 'date-fns';\nimport { ptBR } from 'date-fns/locale';\nimport { cn } from '@/lib/utils';\nimport { toLocalISOString } from '@/lib/date-utils';\n\ninterface DateTimeInputProps {\n  value?: string;\n  onChange?: (value: string) => void;\n  placeholder?: string;\n  disabled?: boolean;\n  className?: string;\n  id?: string;\n}\n\nexport function DateTimeInput({\n  value = '',\n  onChange,\n  placeholder = 'DD/MM/AAAA HH:mm',\n  disabled = false,\n  className,\n  id\n}: DateTimeInputProps) {\n  const [open, setOpen] = useState(false);\n  const [dateValue, setDateValue] = useState<Date | undefined>();\n  const [timeValue, setTimeValue] = useState('');\n  const [inputValue, setInputValue] = useState('');\n\n  // Parse the initial value\n  useEffect(() => {\n    if (value) {\n      try {\n        const date = new Date(value);\n        if (isValid(date)) {\n          setDateValue(date);\n          setTimeValue(format(date, 'HH:mm'));\n          setInputValue(format(date, 'dd/MM/yyyy HH:mm', { locale: ptBR }));\n        }\n      } catch (error) {\n        console.error('Error parsing date:', error);\n      }\n    } else {\n      setDateValue(undefined);\n      setTimeValue('');\n      setInputValue('');\n    }\n  }, [value]);\n\n  const handleDateSelect = (date: Date | undefined) => {\n    if (date) {\n      setDateValue(date);\n\n      // Combine date with existing time or default to current time\n      const timeToUse = timeValue || format(new Date(), 'HH:mm');\n      const [hours, minutes] = timeToUse.split(':').map(Number);\n\n      const newDate = new Date(date);\n      newDate.setHours(hours, minutes, 0, 0);\n\n      const formattedDisplay = format(newDate, 'dd/MM/yyyy HH:mm', { locale: ptBR });\n      const isoValue = toLocalISOString(newDate);\n\n      setInputValue(formattedDisplay);\n      setTimeValue(timeToUse);\n      onChange?.(isoValue);\n    }\n    setOpen(false);\n  };\n\n  const handleTimeChange = (time: string) => {\n    setTimeValue(time);\n\n    if (dateValue && time) {\n      const [hours, minutes] = time.split(':').map(Number);\n      const newDate = new Date(dateValue);\n      newDate.setHours(hours, minutes, 0, 0);\n\n      const formattedDisplay = format(newDate, 'dd/MM/yyyy HH:mm', { locale: ptBR });\n      const isoValue = toLocalISOString(newDate);\n\n      setInputValue(formattedDisplay);\n      onChange?.(isoValue);\n    }\n  };\n\n  const handleInputChange = (inputText: string) => {\n    setInputValue(inputText);\n    \n    // Try to parse the input in Brazilian format\n    try {\n      // Support formats: DD/MM/YYYY HH:mm, DD/MM/YYYY HH:mm:ss, DD/MM/YYYY\n      let parsedDate: Date | null = null;\n      \n      if (inputText.match(/^\\d{2}\\/\\d{2}\\/\\d{4} \\d{2}:\\d{2}$/)) {\n        parsedDate = parse(inputText, 'dd/MM/yyyy HH:mm', new Date(), { locale: ptBR });\n      } else if (inputText.match(/^\\d{2}\\/\\d{2}\\/\\d{4}$/)) {\n        parsedDate = parse(inputText + ' 09:00', 'dd/MM/yyyy HH:mm', new Date(), { locale: ptBR });\n      }\n      \n      if (parsedDate && isValid(parsedDate)) {\n        setDateValue(parsedDate);\n        setTimeValue(format(parsedDate, 'HH:mm'));\n        const isoValue = toLocalISOString(parsedDate);\n        onChange?.(isoValue);\n      }\n    } catch (error) {\n      // Invalid input, just update the display value\n    }\n  };\n\n  const handleInputBlur = () => {\n    // If input is invalid, reset to the last valid value\n    if (dateValue) {\n      const formattedDisplay = format(dateValue, 'dd/MM/yyyy HH:mm', { locale: ptBR });\n      setInputValue(formattedDisplay);\n    }\n  };\n\n  return (\n    <div className=\"flex gap-2\">\n      <Popover open={open} onOpenChange={setOpen}>\n        <PopoverTrigger asChild>\n          <Button\n            variant=\"outline\"\n            className={cn(\n              \"justify-start text-left font-normal flex-1\",\n              !dateValue && \"text-muted-foreground\",\n              className\n            )}\n            disabled={disabled}\n          >\n            <CalendarIcon className=\"mr-2 h-4 w-4\" />\n            <Input\n              id={id}\n              value={inputValue}\n              onChange={(e) => handleInputChange(e.target.value)}\n              onBlur={handleInputBlur}\n              placeholder={placeholder}\n              className=\"border-0 p-0 h-auto bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0\"\n              disabled={disabled}\n            />\n          </Button>\n        </PopoverTrigger>\n        <PopoverContent className=\"w-auto p-0\" align=\"start\">\n          <Calendar\n            mode=\"single\"\n            locale={ptBR}\n            selected={dateValue}\n            onSelect={handleDateSelect}\n            initialFocus\n          />\n          <div className=\"p-3 border-t\">\n            <div className=\"flex items-center gap-2\">\n              <Clock className=\"h-4 w-4\" />\n              <Input\n                type=\"time\"\n                value={timeValue}\n                onChange={(e) => handleTimeChange(e.target.value)}\n                className=\"w-auto\"\n              />\n            </div>\n          </div>\n        </PopoverContent>\n      </Popover>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;AAWO,SAAS,cAAc,EAC5B,QAAQ,EAAE,EACV,QAAQ,EACR,cAAc,kBAAkB,EAChC,WAAW,KAAK,EAChB,SAAS,EACT,EAAE,EACiB;;IACnB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,OAAO;gBACT,IAAI;oBACF,MAAM,OAAO,IAAI,KAAK;oBACtB,IAAI,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;wBACjB,aAAa;wBACb,aAAa,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;wBAC1B,cAAc,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,oBAAoB;4BAAE,QAAQ,oJAAA,CAAA,OAAI;wBAAC;oBAChE;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,uBAAuB;gBACvC;YACF,OAAO;gBACL,aAAa;gBACb,aAAa;gBACb,cAAc;YAChB;QACF;kCAAG;QAAC;KAAM;IAEV,MAAM,mBAAmB,CAAC;QACxB,IAAI,MAAM;YACR,aAAa;YAEb,6DAA6D;YAC7D,MAAM,YAAY,aAAa,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;YAClD,MAAM,CAAC,OAAO,QAAQ,GAAG,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC;YAElD,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,QAAQ,CAAC,OAAO,SAAS,GAAG;YAEpC,MAAM,mBAAmB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;gBAAE,QAAQ,oJAAA,CAAA,OAAI;YAAC;YAC5E,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;YAElC,cAAc;YACd,aAAa;YACb,WAAW;QACb;QACA,QAAQ;IACV;IAEA,MAAM,mBAAmB,CAAC;QACxB,aAAa;QAEb,IAAI,aAAa,MAAM;YACrB,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC;YAC7C,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,QAAQ,CAAC,OAAO,SAAS,GAAG;YAEpC,MAAM,mBAAmB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;gBAAE,QAAQ,oJAAA,CAAA,OAAI;YAAC;YAC5E,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;YAElC,cAAc;YACd,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,cAAc;QAEd,6CAA6C;QAC7C,IAAI;YACF,qEAAqE;YACrE,IAAI,aAA0B;YAE9B,IAAI,UAAU,KAAK,CAAC,sCAAsC;gBACxD,aAAa,CAAA,GAAA,uJAAA,CAAA,QAAK,AAAD,EAAE,WAAW,oBAAoB,IAAI,QAAQ;oBAAE,QAAQ,oJAAA,CAAA,OAAI;gBAAC;YAC/E,OAAO,IAAI,UAAU,KAAK,CAAC,0BAA0B;gBACnD,aAAa,CAAA,GAAA,uJAAA,CAAA,QAAK,AAAD,EAAE,YAAY,UAAU,oBAAoB,IAAI,QAAQ;oBAAE,QAAQ,oJAAA,CAAA,OAAI;gBAAC;YAC1F;YAEA,IAAI,cAAc,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,aAAa;gBACrC,aAAa;gBACb,aAAa,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,YAAY;gBAChC,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;gBAClC,WAAW;YACb;QACF,EAAE,OAAO,OAAO;QACd,+CAA+C;QACjD;IACF;IAEA,MAAM,kBAAkB;QACtB,qDAAqD;QACrD,IAAI,WAAW;YACb,MAAM,mBAAmB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,WAAW,oBAAoB;gBAAE,QAAQ,oJAAA,CAAA,OAAI;YAAC;YAC9E,cAAc;QAChB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sIAAA,CAAA,UAAO;YAAC,MAAM;YAAM,cAAc;;8BACjC,6LAAC,sIAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8CACA,CAAC,aAAa,yBACd;wBAEF,UAAU;;0CAEV,6LAAC,6MAAA,CAAA,WAAY;gCAAC,WAAU;;;;;;0CACxB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAI;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,QAAQ;gCACR,aAAa;gCACb,WAAU;gCACV,UAAU;;;;;;;;;;;;;;;;;8BAIhB,6LAAC,sIAAA,CAAA,iBAAc;oBAAC,WAAU;oBAAa,OAAM;;sCAC3C,6LAAC,uIAAA,CAAA,WAAQ;4BACP,MAAK;4BACL,QAAQ,oJAAA,CAAA,OAAI;4BACZ,UAAU;4BACV,UAAU;4BACV,YAAY;;;;;;sCAEd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAChD,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1B;GAvJgB;KAAA", "debugId": null}}, {"offset": {"line": 3711, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/validations.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// Utility schemas\nconst phoneRegex = /^(\\+55\\s?)?(\\(?\\d{2}\\)?\\s?)?\\d{4,5}-?\\d{4}$/\nconst cpfRegex = /^\\d{3}\\.\\d{3}\\.\\d{3}-\\d{2}$|^\\d{11}$/\nconst crmRegex = /^\\d{4,6}\\/[A-Z]{2}$/\n\n// Patient validation schema\nexport const patientSchema = z.object({\n  name: z.string()\n    .min(2, 'Nome deve ter pelo menos 2 caracteres')\n    .max(100, 'Nome deve ter no máximo 100 caracteres')\n    .regex(/^[a-zA-ZÀ-ÿ\\s]+$/, 'Nome deve conter apenas letras e espaços'),\n  \n  email: z.string()\n    .email('E-mail inválido')\n    .optional()\n    .or(z.literal('')),\n  \n  phone: z.string()\n    .regex(phoneRegex, 'Telefone inválido. Use formato: (11) 99999-9999')\n    .optional()\n    .or(z.literal('')),\n  \n  birth_date: z.string()\n    .optional()\n    .refine((date) => {\n      if (!date) return true\n      const birthDate = new Date(date)\n      const today = new Date()\n      const age = today.getFullYear() - birthDate.getFullYear()\n      return age >= 0 && age <= 150\n    }, 'Data de nascimento inválida'),\n  \n  cpf: z.string()\n    .regex(cpfRegex, 'CPF inválido. Use formato: 000.000.000-00')\n    .optional()\n    .or(z.literal('')),\n  \n  address: z.string()\n    .max(500, 'Endereço deve ter no máximo 500 caracteres')\n    .optional()\n    .or(z.literal('')),\n  \n  notes: z.string()\n    .max(1000, 'Observações devem ter no máximo 1000 caracteres')\n    .optional()\n    .or(z.literal(''))\n})\n\nexport type PatientFormData = z.infer<typeof patientSchema>\n\n// Healthcare Professional validation schema\nexport const healthcareProfessionalSchema = z.object({\n  name: z.string()\n    .min(2, 'Nome deve ter pelo menos 2 caracteres')\n    .max(100, 'Nome deve ter no máximo 100 caracteres')\n    .regex(/^[a-zA-ZÀ-ÿ\\s]+$/, 'Nome deve conter apenas letras e espaços'),\n  \n  specialty: z.string()\n    .max(100, 'Especialidade deve ter no máximo 100 caracteres')\n    .optional()\n    .or(z.literal('')),\n  \n  crm: z.string()\n    .regex(crmRegex, 'CRM inválido. Use formato: 123456/SP')\n    .optional()\n    .or(z.literal('')),\n  \n  phone: z.string()\n    .regex(phoneRegex, 'Telefone inválido. Use formato: (11) 99999-9999')\n    .optional()\n    .or(z.literal('')),\n  \n  email: z.string()\n    .email('E-mail inválido')\n    .optional()\n    .or(z.literal('')),\n  \n  is_active: z.boolean().default(true)\n})\n\nexport type HealthcareProfessionalFormData = z.infer<typeof healthcareProfessionalSchema>\n\n// Appointment validation schema\nexport const appointmentSchema = z.object({\n  title: z.string()\n    .min(2, 'Título deve ter pelo menos 2 caracteres')\n    .max(200, 'Título deve ter no máximo 200 caracteres'),\n  \n  description: z.string()\n    .max(1000, 'Descrição deve ter no máximo 1000 caracteres')\n    .default('')\n    .transform(val => val || ''),\n  \n  patient_id: z.string()\n    .uuid('ID do paciente inválido'),\n  \n  healthcare_professional_id: z.string()\n    .uuid('ID do profissional inválido')\n    .optional()\n    .or(z.literal('')),\n  \n  start_time: z.string()\n    .min(1, 'Data/hora de início é obrigatória')\n    .refine((val) => {\n      console.log('[VALIDATION DEBUG] Validating start_time:', val);\n      // Accept both datetime-local format (YYYY-MM-DDTHH:mm) and ISO format\n      const datetimeLocalRegex = /^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}$/;\n      const isDatetimeLocal = datetimeLocalRegex.test(val);\n      const isValidDate = !isNaN(new Date(val).getTime());\n      console.log('[VALIDATION DEBUG] start_time validation:', { val, isDatetimeLocal, isValidDate });\n      return isDatetimeLocal || isValidDate;\n    }, 'Data/hora de início inválida'),\n\n  end_time: z.string()\n    .min(1, 'Data/hora de fim é obrigatória')\n    .refine((val) => {\n      console.log('[VALIDATION DEBUG] Validating end_time:', val);\n      // Accept both datetime-local format (YYYY-MM-DDTHH:mm) and ISO format\n      const datetimeLocalRegex = /^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}$/;\n      const isDatetimeLocal = datetimeLocalRegex.test(val);\n      const isValidDate = !isNaN(new Date(val).getTime());\n      console.log('[VALIDATION DEBUG] end_time validation:', { val, isDatetimeLocal, isValidDate });\n      return isDatetimeLocal || isValidDate;\n    }, 'Data/hora de fim inválida'),\n  \n  type: z.enum(['consultation', 'follow_up'], {\n    errorMap: () => ({ message: 'Tipo de consulta inválido' })\n  }).optional().default('consultation'),\n\n  status: z.enum(['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled'], {\n    errorMap: () => ({ message: 'Status da consulta inválido' })\n  }).optional(),\n\n  notes: z.string()\n    .max(1000, 'Observações devem ter no máximo 1000 caracteres')\n    .optional()\n    .or(z.literal('')),\n  \n  // Recurrence fields\n  has_recurrence: z.boolean().default(false),\n  \n  recurrence_type: z.enum(['daily', 'weekly', 'monthly']).optional(),\n  \n  recurrence_interval: z.number()\n    .int('Intervalo deve ser um número inteiro')\n    .min(1, 'Intervalo mínimo é 1')\n    .max(12, 'Intervalo máximo é 12')\n    .optional(),\n  \n  recurrence_days: z.array(z.number().int().min(1).max(7)).optional(),\n  \n  recurrence_end_type: z.enum(['never', 'date', 'count']).optional(),\n  \n  recurrence_end_date: z.string().optional(),\n  \n  recurrence_count: z.number()\n    .int('Número de ocorrências deve ser um número inteiro')\n    .min(1, 'Mínimo 1 ocorrência')\n    .max(365, 'Máximo 365 ocorrências')\n    .optional()\n}).refine((data) => {\n  // Validate that end_time is after start_time\n  try {\n    console.log('[DATE VALIDATION DEBUG] Validating dates:', {\n      start_time: data.start_time,\n      end_time: data.end_time,\n      start_time_type: typeof data.start_time,\n      end_time_type: typeof data.end_time\n    });\n\n    // Skip validation if either date is empty (will be caught by required validation)\n    if (!data.start_time || !data.end_time) {\n      console.log('[DATE VALIDATION DEBUG] One or both dates empty, skipping validation');\n      return true\n    }\n\n    const start = new Date(data.start_time)\n    const end = new Date(data.end_time)\n\n    console.log('[DATE VALIDATION DEBUG] Parsed dates:', {\n      start: start.toISOString(),\n      end: end.toISOString(),\n      startTime: start.getTime(),\n      endTime: end.getTime(),\n      startValid: !isNaN(start.getTime()),\n      endValid: !isNaN(end.getTime())\n    });\n\n    // Check if dates are valid\n    if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n      console.log('[DATE VALIDATION DEBUG] Invalid date(s) detected');\n      return false\n    }\n\n    const isValid = end > start;\n    console.log('[DATE VALIDATION DEBUG] Comparison result:', {\n      endGreaterThanStart: isValid,\n      timeDifference: end.getTime() - start.getTime()\n    });\n\n    return isValid\n  } catch (error) {\n    console.error('[DATE VALIDATION DEBUG] Date validation error:', error)\n    return false\n  }\n}, {\n  message: 'Data/hora de fim deve ser posterior à data/hora de início',\n  path: ['end_time']\n}).refine((data) => {\n  // Validate recurrence fields when has_recurrence is true\n  if (data.has_recurrence) {\n    return data.recurrence_type && data.recurrence_interval\n  }\n  return true\n}, {\n  message: 'Campos de recorrência são obrigatórios quando recorrência está ativada',\n  path: ['recurrence_type']\n}).refine((data) => {\n  // Validate recurrence end date when end_type is 'date'\n  if (data.has_recurrence && data.recurrence_end_type === 'date') {\n    return data.recurrence_end_date\n  }\n  return true\n}, {\n  message: 'Data de fim é obrigatória quando tipo de fim é \"data\"',\n  path: ['recurrence_end_date']\n}).refine((data) => {\n  // Validate recurrence count when end_type is 'count'\n  if (data.has_recurrence && data.recurrence_end_type === 'count') {\n    return data.recurrence_count && data.recurrence_count > 0\n  }\n  return true\n}, {\n  message: 'Número de ocorrências é obrigatório quando tipo de fim é \"contagem\"',\n  path: ['recurrence_count']\n})\n\nexport type AppointmentFormData = z.infer<typeof appointmentSchema>\n\n// Clinic Settings validation schema\nexport const clinicSettingsSchema = z.object({\n  clinic_name: z.string()\n    .max(200, 'Nome da clínica deve ter no máximo 200 caracteres')\n    .optional()\n    .or(z.literal('')),\n  \n  working_hours_start: z.string()\n    .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Horário de início inválido'),\n  \n  working_hours_end: z.string()\n    .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Horário de fim inválido'),\n  \n  working_days: z.array(z.number().int().min(1).max(7))\n    .min(1, 'Pelo menos um dia de funcionamento deve ser selecionado'),\n  \n  appointment_duration_minutes: z.number()\n    .int('Duração deve ser um número inteiro')\n    .min(15, 'Duração mínima é 15 minutos')\n    .max(180, 'Duração máxima é 3 horas'),\n  \n  allow_weekend_appointments: z.boolean().default(false),\n  \n  timezone: z.string()\n    .min(1, 'Fuso horário é obrigatório')\n}).refine((data) => {\n  // Validate that end time is after start time\n  const [startHour, startMinute] = data.working_hours_start.split(':').map(Number)\n  const [endHour, endMinute] = data.working_hours_end.split(':').map(Number)\n  \n  const startMinutes = startHour * 60 + startMinute\n  const endMinutes = endHour * 60 + endMinute\n  \n  return endMinutes > startMinutes\n}, {\n  message: 'Horário de fim deve ser posterior ao horário de início',\n  path: ['working_hours_end']\n})\n\nexport type ClinicSettingsFormData = z.infer<typeof clinicSettingsSchema>\n\n// File upload validation\nexport const fileUploadSchema = z.object({\n  file: z.instanceof(File)\n    .refine((file) => file.size <= 50 * 1024 * 1024, 'Arquivo deve ter no máximo 50MB')\n    .refine((file) => {\n      const allowedTypes = [\n        'image/jpeg',\n        'image/png', \n        'image/gif',\n        'application/pdf',\n        'text/plain',\n        'application/msword',\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n      ]\n      return allowedTypes.includes(file.type)\n    }, 'Tipo de arquivo não permitido'),\n  \n  patient_id: z.string().uuid('ID do paciente inválido')\n})\n\nexport type FileUploadFormData = z.infer<typeof fileUploadSchema>\n\n// Common validation utilities\nexport const validateCPF = (cpf: string): boolean => {\n  // Remove formatting\n  const cleanCPF = cpf.replace(/[^\\d]/g, '')\n  \n  if (cleanCPF.length !== 11) return false\n  \n  // Check for repeated digits\n  if (/^(\\d)\\1{10}$/.test(cleanCPF)) return false\n  \n  // Validate check digits\n  let sum = 0\n  for (let i = 0; i < 9; i++) {\n    sum += parseInt(cleanCPF.charAt(i)) * (10 - i)\n  }\n  let remainder = (sum * 10) % 11\n  if (remainder === 10 || remainder === 11) remainder = 0\n  if (remainder !== parseInt(cleanCPF.charAt(9))) return false\n  \n  sum = 0\n  for (let i = 0; i < 10; i++) {\n    sum += parseInt(cleanCPF.charAt(i)) * (11 - i)\n  }\n  remainder = (sum * 10) % 11\n  if (remainder === 10 || remainder === 11) remainder = 0\n  if (remainder !== parseInt(cleanCPF.charAt(10))) return false\n  \n  return true\n}\n\nexport const formatCPF = (cpf: string): string => {\n  const cleanCPF = cpf.replace(/[^\\d]/g, '')\n  return cleanCPF.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{2})/, '$1.$2.$3-$4')\n}\n\nexport const formatPhone = (phone: string): string => {\n  const cleanPhone = phone.replace(/[^\\d]/g, '')\n  if (cleanPhone.length === 11) {\n    return cleanPhone.replace(/(\\d{2})(\\d{5})(\\d{4})/, '($1) $2-$3')\n  } else if (cleanPhone.length === 10) {\n    return cleanPhone.replace(/(\\d{2})(\\d{4})(\\d{4})/, '($1) $2-$3')\n  }\n  return phone\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAEA,kBAAkB;AAClB,MAAM,aAAa;AACnB,MAAM,WAAW;AACjB,MAAM,WAAW;AAGV,MAAM,gBAAgB,qKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,MAAM,qKAAA,CAAA,IAAC,CAAC,MAAM,GACX,GAAG,CAAC,GAAG,yCACP,GAAG,CAAC,KAAK,0CACT,KAAK,CAAC,oBAAoB;IAE7B,OAAO,qKAAA,CAAA,IAAC,CAAC,MAAM,GACZ,KAAK,CAAC,mBACN,QAAQ,GACR,EAAE,CAAC,qKAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAEhB,OAAO,qKAAA,CAAA,IAAC,CAAC,MAAM,GACZ,KAAK,CAAC,YAAY,mDAClB,QAAQ,GACR,EAAE,CAAC,qKAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAEhB,YAAY,qKAAA,CAAA,IAAC,CAAC,MAAM,GACjB,QAAQ,GACR,MAAM,CAAC,CAAC;QACP,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,YAAY,IAAI,KAAK;QAC3B,MAAM,QAAQ,IAAI;QAClB,MAAM,MAAM,MAAM,WAAW,KAAK,UAAU,WAAW;QACvD,OAAO,OAAO,KAAK,OAAO;IAC5B,GAAG;IAEL,KAAK,qKAAA,CAAA,IAAC,CAAC,MAAM,GACV,KAAK,CAAC,UAAU,6CAChB,QAAQ,GACR,EAAE,CAAC,qKAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAEhB,SAAS,qKAAA,CAAA,IAAC,CAAC,MAAM,GACd,GAAG,CAAC,KAAK,8CACT,QAAQ,GACR,EAAE,CAAC,qKAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAEhB,OAAO,qKAAA,CAAA,IAAC,CAAC,MAAM,GACZ,GAAG,CAAC,MAAM,mDACV,QAAQ,GACR,EAAE,CAAC,qKAAA,CAAA,IAAC,CAAC,OAAO,CAAC;AAClB;AAKO,MAAM,+BAA+B,qKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnD,MAAM,qKAAA,CAAA,IAAC,CAAC,MAAM,GACX,GAAG,CAAC,GAAG,yCACP,GAAG,CAAC,KAAK,0CACT,KAAK,CAAC,oBAAoB;IAE7B,WAAW,qKAAA,CAAA,IAAC,CAAC,MAAM,GAChB,GAAG,CAAC,KAAK,mDACT,QAAQ,GACR,EAAE,CAAC,qKAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAEhB,KAAK,qKAAA,CAAA,IAAC,CAAC,MAAM,GACV,KAAK,CAAC,UAAU,wCAChB,QAAQ,GACR,EAAE,CAAC,qKAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAEhB,OAAO,qKAAA,CAAA,IAAC,CAAC,MAAM,GACZ,KAAK,CAAC,YAAY,mDAClB,QAAQ,GACR,EAAE,CAAC,qKAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAEhB,OAAO,qKAAA,CAAA,IAAC,CAAC,MAAM,GACZ,KAAK,CAAC,mBACN,QAAQ,GACR,EAAE,CAAC,qKAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAEhB,WAAW,qKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AACjC;AAKO,MAAM,oBAAoB,qKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,OAAO,qKAAA,CAAA,IAAC,CAAC,MAAM,GACZ,GAAG,CAAC,GAAG,2CACP,GAAG,CAAC,KAAK;IAEZ,aAAa,qKAAA,CAAA,IAAC,CAAC,MAAM,GAClB,GAAG,CAAC,MAAM,gDACV,OAAO,CAAC,IACR,SAAS,CAAC,CAAA,MAAO,OAAO;IAE3B,YAAY,qKAAA,CAAA,IAAC,CAAC,MAAM,GACjB,IAAI,CAAC;IAER,4BAA4B,qKAAA,CAAA,IAAC,CAAC,MAAM,GACjC,IAAI,CAAC,+BACL,QAAQ,GACR,EAAE,CAAC,qKAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAEhB,YAAY,qKAAA,CAAA,IAAC,CAAC,MAAM,GACjB,GAAG,CAAC,GAAG,qCACP,MAAM,CAAC,CAAC;QACP,QAAQ,GAAG,CAAC,6CAA6C;QACzD,sEAAsE;QACtE,MAAM,qBAAqB;QAC3B,MAAM,kBAAkB,mBAAmB,IAAI,CAAC;QAChD,MAAM,cAAc,CAAC,MAAM,IAAI,KAAK,KAAK,OAAO;QAChD,QAAQ,GAAG,CAAC,6CAA6C;YAAE;YAAK;YAAiB;QAAY;QAC7F,OAAO,mBAAmB;IAC5B,GAAG;IAEL,UAAU,qKAAA,CAAA,IAAC,CAAC,MAAM,GACf,GAAG,CAAC,GAAG,kCACP,MAAM,CAAC,CAAC;QACP,QAAQ,GAAG,CAAC,2CAA2C;QACvD,sEAAsE;QACtE,MAAM,qBAAqB;QAC3B,MAAM,kBAAkB,mBAAmB,IAAI,CAAC;QAChD,MAAM,cAAc,CAAC,MAAM,IAAI,KAAK,KAAK,OAAO;QAChD,QAAQ,GAAG,CAAC,2CAA2C;YAAE;YAAK;YAAiB;QAAY;QAC3F,OAAO,mBAAmB;IAC5B,GAAG;IAEL,MAAM,qKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAgB;KAAY,EAAE;QAC1C,UAAU,IAAM,CAAC;gBAAE,SAAS;YAA4B,CAAC;IAC3D,GAAG,QAAQ,GAAG,OAAO,CAAC;IAEtB,QAAQ,qKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAa;QAAa;QAAe;QAAa;KAAY,EAAE;QAClF,UAAU,IAAM,CAAC;gBAAE,SAAS;YAA8B,CAAC;IAC7D,GAAG,QAAQ;IAEX,OAAO,qKAAA,CAAA,IAAC,CAAC,MAAM,GACZ,GAAG,CAAC,MAAM,mDACV,QAAQ,GACR,EAAE,CAAC,qKAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAEhB,oBAAoB;IACpB,gBAAgB,qKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAEpC,iBAAiB,qKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;QAAU;KAAU,EAAE,QAAQ;IAEhE,qBAAqB,qKAAA,CAAA,IAAC,CAAC,MAAM,GAC1B,GAAG,CAAC,wCACJ,GAAG,CAAC,GAAG,wBACP,GAAG,CAAC,IAAI,yBACR,QAAQ;IAEX,iBAAiB,qKAAA,CAAA,IAAC,CAAC,KAAK,CAAC,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ;IAEjE,qBAAqB,qKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;QAAQ;KAAQ,EAAE,QAAQ;IAEhE,qBAAqB,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAExC,kBAAkB,qKAAA,CAAA,IAAC,CAAC,MAAM,GACvB,GAAG,CAAC,oDACJ,GAAG,CAAC,GAAG,uBACP,GAAG,CAAC,KAAK,0BACT,QAAQ;AACb,GAAG,MAAM,CAAC,CAAC;IACT,6CAA6C;IAC7C,IAAI;QACF,QAAQ,GAAG,CAAC,6CAA6C;YACvD,YAAY,KAAK,UAAU;YAC3B,UAAU,KAAK,QAAQ;YACvB,iBAAiB,OAAO,KAAK,UAAU;YACvC,eAAe,OAAO,KAAK,QAAQ;QACrC;QAEA,kFAAkF;QAClF,IAAI,CAAC,KAAK,UAAU,IAAI,CAAC,KAAK,QAAQ,EAAE;YACtC,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,MAAM,QAAQ,IAAI,KAAK,KAAK,UAAU;QACtC,MAAM,MAAM,IAAI,KAAK,KAAK,QAAQ;QAElC,QAAQ,GAAG,CAAC,yCAAyC;YACnD,OAAO,MAAM,WAAW;YACxB,KAAK,IAAI,WAAW;YACpB,WAAW,MAAM,OAAO;YACxB,SAAS,IAAI,OAAO;YACpB,YAAY,CAAC,MAAM,MAAM,OAAO;YAChC,UAAU,CAAC,MAAM,IAAI,OAAO;QAC9B;QAEA,2BAA2B;QAC3B,IAAI,MAAM,MAAM,OAAO,OAAO,MAAM,IAAI,OAAO,KAAK;YAClD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,MAAM,UAAU,MAAM;QACtB,QAAQ,GAAG,CAAC,8CAA8C;YACxD,qBAAqB;YACrB,gBAAgB,IAAI,OAAO,KAAK,MAAM,OAAO;QAC/C;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,OAAO;IACT;AACF,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAW;AACpB,GAAG,MAAM,CAAC,CAAC;IACT,yDAAyD;IACzD,IAAI,KAAK,cAAc,EAAE;QACvB,OAAO,KAAK,eAAe,IAAI,KAAK,mBAAmB;IACzD;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B,GAAG,MAAM,CAAC,CAAC;IACT,uDAAuD;IACvD,IAAI,KAAK,cAAc,IAAI,KAAK,mBAAmB,KAAK,QAAQ;QAC9D,OAAO,KAAK,mBAAmB;IACjC;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAsB;AAC/B,GAAG,MAAM,CAAC,CAAC;IACT,qDAAqD;IACrD,IAAI,KAAK,cAAc,IAAI,KAAK,mBAAmB,KAAK,SAAS;QAC/D,OAAO,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,GAAG;IAC1D;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAmB;AAC5B;AAKO,MAAM,uBAAuB,qKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,aAAa,qKAAA,CAAA,IAAC,CAAC,MAAM,GAClB,GAAG,CAAC,KAAK,qDACT,QAAQ,GACR,EAAE,CAAC,qKAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAEhB,qBAAqB,qKAAA,CAAA,IAAC,CAAC,MAAM,GAC1B,KAAK,CAAC,oCAAoC;IAE7C,mBAAmB,qKAAA,CAAA,IAAC,CAAC,MAAM,GACxB,KAAK,CAAC,oCAAoC;IAE7C,cAAc,qKAAA,CAAA,IAAC,CAAC,KAAK,CAAC,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAC/C,GAAG,CAAC,GAAG;IAEV,8BAA8B,qKAAA,CAAA,IAAC,CAAC,MAAM,GACnC,GAAG,CAAC,sCACJ,GAAG,CAAC,IAAI,+BACR,GAAG,CAAC,KAAK;IAEZ,4BAA4B,qKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAEhD,UAAU,qKAAA,CAAA,IAAC,CAAC,MAAM,GACf,GAAG,CAAC,GAAG;AACZ,GAAG,MAAM,CAAC,CAAC;IACT,6CAA6C;IAC7C,MAAM,CAAC,WAAW,YAAY,GAAG,KAAK,mBAAmB,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;IACzE,MAAM,CAAC,SAAS,UAAU,GAAG,KAAK,iBAAiB,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;IAEnE,MAAM,eAAe,YAAY,KAAK;IACtC,MAAM,aAAa,UAAU,KAAK;IAElC,OAAO,aAAa;AACtB,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAoB;AAC7B;AAKO,MAAM,mBAAmB,qKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,MAAM,qKAAA,CAAA,IAAC,CAAC,UAAU,CAAC,MAChB,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,IAAI,KAAK,OAAO,MAAM,mCAChD,MAAM,CAAC,CAAC;QACP,MAAM,eAAe;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO,aAAa,QAAQ,CAAC,KAAK,IAAI;IACxC,GAAG;IAEL,YAAY,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;AAC9B;AAKO,MAAM,cAAc,CAAC;IAC1B,oBAAoB;IACpB,MAAM,WAAW,IAAI,OAAO,CAAC,UAAU;IAEvC,IAAI,SAAS,MAAM,KAAK,IAAI,OAAO;IAEnC,4BAA4B;IAC5B,IAAI,eAAe,IAAI,CAAC,WAAW,OAAO;IAE1C,wBAAwB;IACxB,IAAI,MAAM;IACV,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,OAAO,SAAS,SAAS,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;IAC/C;IACA,IAAI,YAAY,AAAC,MAAM,KAAM;IAC7B,IAAI,cAAc,MAAM,cAAc,IAAI,YAAY;IACtD,IAAI,cAAc,SAAS,SAAS,MAAM,CAAC,KAAK,OAAO;IAEvD,MAAM;IACN,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;QAC3B,OAAO,SAAS,SAAS,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;IAC/C;IACA,YAAY,AAAC,MAAM,KAAM;IACzB,IAAI,cAAc,MAAM,cAAc,IAAI,YAAY;IACtD,IAAI,cAAc,SAAS,SAAS,MAAM,CAAC,MAAM,OAAO;IAExD,OAAO;AACT;AAEO,MAAM,YAAY,CAAC;IACxB,MAAM,WAAW,IAAI,OAAO,CAAC,UAAU;IACvC,OAAO,SAAS,OAAO,CAAC,gCAAgC;AAC1D;AAEO,MAAM,cAAc,CAAC;IAC1B,MAAM,aAAa,MAAM,OAAO,CAAC,UAAU;IAC3C,IAAI,WAAW,MAAM,KAAK,IAAI;QAC5B,OAAO,WAAW,OAAO,CAAC,yBAAyB;IACrD,OAAO,IAAI,WAAW,MAAM,KAAK,IAAI;QACnC,OAAO,WAAW,OAAO,CAAC,yBAAyB;IACrD;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3976, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/hooks/useAsyncOperation.ts"], "sourcesContent": ["import { useState, useCallback } from 'react'\nimport { useToast } from '@/hooks/use-toast'\n\ninterface UseAsyncOperationOptions {\n  onSuccess?: (data?: any) => void\n  onError?: (error: Error) => void\n  successMessage?: string\n  errorMessage?: string\n  showSuccessToast?: boolean\n  showErrorToast?: boolean\n}\n\ninterface UseAsyncOperationReturn<T> {\n  data: T | null\n  loading: boolean\n  error: Error | null\n  execute: (operation: () => Promise<T>) => Promise<T | null>\n  reset: () => void\n}\n\nexport function useAsyncOperation<T = any>(\n  options: UseAsyncOperationOptions = {}\n): UseAsyncOperationReturn<T> {\n  const [data, setData] = useState<T | null>(null)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<Error | null>(null)\n  const { toast } = useToast()\n\n  const {\n    onSuccess,\n    onError,\n    successMessage,\n    errorMessage,\n    showSuccessToast = true,\n    showErrorToast = true\n  } = options\n\n  const execute = useCallback(async (operation: () => Promise<T>): Promise<T | null> => {\n    try {\n      setLoading(true)\n      setError(null)\n      \n      const result = await operation()\n      setData(result)\n      \n      if (showSuccessToast && successMessage) {\n        toast({\n          title: \"Sucesso!\",\n          description: successMessage,\n        })\n      }\n      \n      onSuccess?.(result)\n      return result\n    } catch (err) {\n      const error = err instanceof Error ? err : new Error('Erro desconhecido')\n      setError(error)\n      \n      if (showErrorToast) {\n        toast({\n          title: \"Erro\",\n          description: errorMessage || error.message || 'Ocorreu um erro inesperado',\n          variant: \"destructive\"\n        })\n      }\n      \n      onError?.(error)\n      return null\n    } finally {\n      setLoading(false)\n    }\n  }, [onSuccess, onError, successMessage, errorMessage, showSuccessToast, showErrorToast, toast])\n\n  const reset = useCallback(() => {\n    setData(null)\n    setError(null)\n    setLoading(false)\n  }, [])\n\n  return {\n    data,\n    loading,\n    error,\n    execute,\n    reset\n  }\n}\n\n// Specialized hook for API operations\nexport function useApiOperation<T = any>(options: UseAsyncOperationOptions = {}) {\n  return useAsyncOperation<T>({\n    showSuccessToast: true,\n    showErrorToast: true,\n    ...options\n  })\n}\n\n// Hook for form submissions\nexport function useFormSubmission<T = any>(options: UseAsyncOperationOptions = {}) {\n  return useAsyncOperation<T>({\n    showSuccessToast: true,\n    showErrorToast: true,\n    successMessage: 'Dados salvos com sucesso!',\n    errorMessage: 'Erro ao salvar dados',\n    ...options\n  })\n}\n\n// Hook for data fetching\nexport function useDataFetching<T = any>(options: UseAsyncOperationOptions = {}) {\n  return useAsyncOperation<T>({\n    showSuccessToast: false,\n    showErrorToast: true,\n    errorMessage: 'Erro ao carregar dados',\n    ...options\n  })\n}\n\n// Hook for delete operations\nexport function useDeleteOperation(options: UseAsyncOperationOptions = {}) {\n  return useAsyncOperation({\n    showSuccessToast: true,\n    showErrorToast: true,\n    successMessage: 'Item excluído com sucesso!',\n    errorMessage: 'Erro ao excluir item',\n    ...options\n  })\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAmBO,SAAS,kBACd,UAAoC,CAAC,CAAC;;IAEtC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,EACJ,SAAS,EACT,OAAO,EACP,cAAc,EACd,YAAY,EACZ,mBAAmB,IAAI,EACvB,iBAAiB,IAAI,EACtB,GAAG;IAEJ,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,OAAO;YACjC,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,MAAM,SAAS,MAAM;gBACrB,QAAQ;gBAER,IAAI,oBAAoB,gBAAgB;oBACtC,MAAM;wBACJ,OAAO;wBACP,aAAa;oBACf;gBACF;gBAEA,YAAY;gBACZ,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,QAAQ,eAAe,QAAQ,MAAM,IAAI,MAAM;gBACrD,SAAS;gBAET,IAAI,gBAAgB;oBAClB,MAAM;wBACJ,OAAO;wBACP,aAAa,gBAAgB,MAAM,OAAO,IAAI;wBAC9C,SAAS;oBACX;gBACF;gBAEA,UAAU;gBACV,OAAO;YACT,SAAU;gBACR,WAAW;YACb;QACF;iDAAG;QAAC;QAAW;QAAS;QAAgB;QAAc;QAAkB;QAAgB;KAAM;IAE9F,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YACxB,QAAQ;YACR,SAAS;YACT,WAAW;QACb;+CAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;GAlEgB;;QAMI,+HAAA,CAAA,WAAQ;;;AA+DrB,SAAS,gBAAyB,UAAoC,CAAC,CAAC;;IAC7E,OAAO,kBAAqB;QAC1B,kBAAkB;QAClB,gBAAgB;QAChB,GAAG,OAAO;IACZ;AACF;IANgB;;QACP;;;AAQF,SAAS,kBAA2B,UAAoC,CAAC,CAAC;;IAC/E,OAAO,kBAAqB;QAC1B,kBAAkB;QAClB,gBAAgB;QAChB,gBAAgB;QAChB,cAAc;QACd,GAAG,OAAO;IACZ;AACF;IARgB;;QACP;;;AAUF,SAAS,gBAAyB,UAAoC,CAAC,CAAC;;IAC7E,OAAO,kBAAqB;QAC1B,kBAAkB;QAClB,gBAAgB;QAChB,cAAc;QACd,GAAG,OAAO;IACZ;AACF;IAPgB;;QACP;;;AASF,SAAS,mBAAmB,UAAoC,CAAC,CAAC;;IACvE,OAAO,kBAAkB;QACvB,kBAAkB;QAClB,gBAAgB;QAChB,gBAAgB;QAChB,cAAc;QACd,GAAG,OAAO;IACZ;AACF;IARgB;;QACP", "debugId": null}}, {"offset": {"line": 4121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/smart-search.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react'\nimport { Search, Plus, User, Phone, Stethoscope } from 'lucide-react'\nimport { Input } from './input'\nimport { Button } from './button'\nimport { Card, CardContent } from './card'\nimport { Badge } from './badge'\nimport { cn } from '@/lib/utils'\n\ninterface Patient {\n  id: string\n  name: string\n  phone?: string\n  email?: string\n}\n\ninterface HealthcareProfessional {\n  id: string\n  name: string\n  phone?: string\n  specialty?: string\n}\n\ninterface SmartSearchProps {\n  placeholder: string\n  value?: string\n  onSelect: (id: string) => void\n  onAddNew?: () => void\n  className?: string\n  disabled?: boolean\n}\n\ninterface PatientSearchProps extends SmartSearchProps {\n  patients: Patient[]\n  selectedPatientId?: string\n}\n\ninterface ProfessionalSearchProps extends SmartSearchProps {\n  professionals: HealthcareProfessional[]\n  selectedProfessionalId?: string\n}\n\n// Patient Search Component\nexport function PatientSearch({\n  patients,\n  selectedPatientId,\n  placeholder = \"Buscar paciente por nome ou telefone...\",\n  onSelect,\n  onAddNew,\n  className,\n  disabled = false\n}: PatientSearchProps) {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [isOpen, setIsOpen] = useState(false)\n  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([])\n  const searchRef = useRef<HTMLDivElement>(null)\n  const inputRef = useRef<HTMLInputElement>(null)\n\n  // Get selected patient for display\n  const selectedPatient = patients.find(p => p.id === selectedPatientId)\n\n  // Filter patients based on search term\n  useEffect(() => {\n    if (!searchTerm.trim()) {\n      setFilteredPatients(patients.slice(0, 10)) // Show first 10 when no search\n      return\n    }\n\n    const filtered = patients.filter(patient => {\n      const searchLower = searchTerm.toLowerCase()\n      const nameMatch = patient.name.toLowerCase().includes(searchLower)\n      \n      // Fix phone filtering - only match if search has digits AND phone contains those digits\n      const cleanSearchPhone = searchTerm.replace(/\\D/g, '')\n      const cleanPatientPhone = patient.phone?.replace(/\\D/g, '') || ''\n      const phoneMatch = cleanSearchPhone.length > 0 && cleanPatientPhone.includes(cleanSearchPhone)\n      \n      const emailMatch = patient.email?.toLowerCase().includes(searchLower)\n      \n      return nameMatch || phoneMatch || emailMatch\n    }).slice(0, 10) // Limit to 10 results\n\n    setFilteredPatients(filtered)\n  }, [searchTerm, patients])\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  const handleSelect = (patient: Patient) => {\n    onSelect(patient.id)\n    setSearchTerm('')\n    setIsOpen(false)\n  }\n\n  const handleInputFocus = () => {\n    setIsOpen(true)\n    if (selectedPatient) {\n      setSearchTerm(selectedPatient.name)\n    }\n  }\n\n  const handleInputChange = (value: string) => {\n    setSearchTerm(value)\n    setIsOpen(true)\n  }\n\n  return (\n    <div ref={searchRef} className={cn(\"relative\", className)}>\n      <div className=\"relative\">\n        <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n        <Input\n          ref={inputRef}\n          type=\"text\"\n          placeholder={selectedPatient ? selectedPatient.name : placeholder}\n          value={searchTerm}\n          onChange={(e) => handleInputChange(e.target.value)}\n          onFocus={handleInputFocus}\n          className=\"pl-10\"\n          disabled={disabled}\n        />\n      </div>\n\n      {isOpen && (\n        <Card className=\"absolute z-50 w-full mt-1 max-h-80 overflow-y-auto\">\n          <CardContent className=\"p-2\">\n            {onAddNew && (\n              <Button\n                variant=\"ghost\"\n                className=\"w-full justify-start mb-2 h-auto p-3\"\n                onClick={() => {\n                  onAddNew()\n                  setIsOpen(false)\n                }}\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                <span>Adicionar novo paciente</span>\n              </Button>\n            )}\n\n            {filteredPatients.length === 0 ? (\n              <div className=\"p-3 text-center text-muted-foreground\">\n                <User className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\n                <p>Nenhum paciente encontrado</p>\n              </div>\n            ) : (\n              <div className=\"space-y-1\">\n                {filteredPatients.map((patient) => (\n                  <Button\n                    key={patient.id}\n                    variant=\"ghost\"\n                    className=\"w-full justify-start h-auto p-3\"\n                    onClick={() => handleSelect(patient)}\n                  >\n                    <div className=\"flex items-center space-x-3 w-full\">\n                      <User className=\"h-4 w-4 text-muted-foreground flex-shrink-0\" />\n                      <div className=\"flex-1 text-left\">\n                        <div className=\"font-medium\">{patient.name}</div>\n                        <div className=\"text-sm text-muted-foreground flex items-center space-x-2\">\n                          {patient.phone && (\n                            <span className=\"flex items-center\">\n                              <Phone className=\"h-3 w-3 mr-1\" />\n                              {patient.phone}\n                            </span>\n                          )}\n                          {patient.email && (\n                            <span className=\"truncate\">{patient.email}</span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </Button>\n                ))}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n\n// Professional Search Component\nexport function ProfessionalSearch({\n  professionals,\n  selectedProfessionalId,\n  placeholder = \"Buscar profissional por nome ou telefone...\",\n  onSelect,\n  onAddNew,\n  className,\n  disabled = false\n}: ProfessionalSearchProps) {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [isOpen, setIsOpen] = useState(false)\n  const [filteredProfessionals, setFilteredProfessionals] = useState<HealthcareProfessional[]>([])\n  const searchRef = useRef<HTMLDivElement>(null)\n  const inputRef = useRef<HTMLInputElement>(null)\n\n  // Get selected professional for display\n  const selectedProfessional = professionals.find(p => p.id === selectedProfessionalId)\n\n  // Filter professionals based on search term\n  useEffect(() => {\n    if (!searchTerm.trim()) {\n      setFilteredProfessionals(professionals.slice(0, 10)) // Show first 10 when no search\n      return\n    }\n\n    const filtered = professionals.filter(professional => {\n      const searchLower = searchTerm.toLowerCase()\n      const nameMatch = professional.name.toLowerCase().includes(searchLower)\n      \n      // Fix phone filtering - only match if search has digits AND phone contains those digits\n      const cleanSearchPhone = searchTerm.replace(/\\D/g, '')\n      const cleanProfessionalPhone = professional.phone?.replace(/\\D/g, '') || ''\n      const phoneMatch = cleanSearchPhone.length > 0 && cleanProfessionalPhone.includes(cleanSearchPhone)\n      \n      const specialtyMatch = professional.specialty?.toLowerCase().includes(searchLower)\n      \n      return nameMatch || phoneMatch || specialtyMatch\n    }).slice(0, 10) // Limit to 10 results\n\n    setFilteredProfessionals(filtered)\n  }, [searchTerm, professionals])\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  const handleSelect = (professional: HealthcareProfessional) => {\n    onSelect(professional.id)\n    setSearchTerm('')\n    setIsOpen(false)\n  }\n\n  const handleInputFocus = () => {\n    setIsOpen(true)\n    if (selectedProfessional) {\n      setSearchTerm(selectedProfessional.name)\n    }\n  }\n\n  const handleInputChange = (value: string) => {\n    setSearchTerm(value)\n    setIsOpen(true)\n  }\n\n  return (\n    <div ref={searchRef} className={cn(\"relative\", className)}>\n      <div className=\"relative\">\n        <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n        <Input\n          ref={inputRef}\n          type=\"text\"\n          placeholder={selectedProfessional ? selectedProfessional.name : placeholder}\n          value={searchTerm}\n          onChange={(e) => handleInputChange(e.target.value)}\n          onFocus={handleInputFocus}\n          className=\"pl-10\"\n          disabled={disabled}\n        />\n      </div>\n\n      {isOpen && (\n        <Card className=\"absolute z-50 w-full mt-1 max-h-80 overflow-y-auto\">\n          <CardContent className=\"p-2\">\n            {onAddNew && (\n              <Button\n                variant=\"ghost\"\n                className=\"w-full justify-start mb-2 h-auto p-3\"\n                onClick={() => {\n                  onAddNew()\n                  setIsOpen(false)\n                }}\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                <span>Adicionar novo profissional</span>\n              </Button>\n            )}\n\n            {filteredProfessionals.length === 0 ? (\n              <div className=\"p-3 text-center text-muted-foreground\">\n                <Stethoscope className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\n                <p>Nenhum profissional encontrado</p>\n              </div>\n            ) : (\n              <div className=\"space-y-1\">\n                {filteredProfessionals.map((professional) => (\n                  <Button\n                    key={professional.id}\n                    variant=\"ghost\"\n                    className=\"w-full justify-start h-auto p-3\"\n                    onClick={() => handleSelect(professional)}\n                  >\n                    <div className=\"flex items-center space-x-3 w-full\">\n                      <Stethoscope className=\"h-4 w-4 text-muted-foreground flex-shrink-0\" />\n                      <div className=\"flex-1 text-left\">\n                        <div className=\"font-medium\">{professional.name}</div>\n                        <div className=\"text-sm text-muted-foreground flex items-center space-x-2\">\n                          {professional.specialty && (\n                            <Badge variant=\"outline\" className=\"text-xs\">\n                              {professional.specialty}\n                            </Badge>\n                          )}\n                          {professional.phone && (\n                            <span className=\"flex items-center\">\n                              <Phone className=\"h-3 w-3 mr-1\" />\n                              {professional.phone}\n                            </span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </Button>\n                ))}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAoCO,SAAS,cAAc,EAC5B,QAAQ,EACR,iBAAiB,EACjB,cAAc,yCAAyC,EACvD,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,WAAW,KAAK,EACG;;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtE,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,mCAAmC;IACnC,MAAM,kBAAkB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEpD,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW,IAAI,IAAI;gBACtB,oBAAoB,SAAS,KAAK,CAAC,GAAG,KAAK,+BAA+B;;gBAC1E;YACF;YAEA,MAAM,WAAW,SAAS,MAAM;oDAAC,CAAA;oBAC/B,MAAM,cAAc,WAAW,WAAW;oBAC1C,MAAM,YAAY,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;oBAEtD,wFAAwF;oBACxF,MAAM,mBAAmB,WAAW,OAAO,CAAC,OAAO;oBACnD,MAAM,oBAAoB,QAAQ,KAAK,EAAE,QAAQ,OAAO,OAAO;oBAC/D,MAAM,aAAa,iBAAiB,MAAM,GAAG,KAAK,kBAAkB,QAAQ,CAAC;oBAE7E,MAAM,aAAa,QAAQ,KAAK,EAAE,cAAc,SAAS;oBAEzD,OAAO,aAAa,cAAc;gBACpC;mDAAG,KAAK,CAAC,GAAG,IAAI,sBAAsB;;YAEtC,oBAAoB;QACtB;kCAAG;QAAC;QAAY;KAAS;IAEzB,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC1E,UAAU;gBACZ;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;2CAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;kCAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,SAAS,QAAQ,EAAE;QACnB,cAAc;QACd,UAAU;IACZ;IAEA,MAAM,mBAAmB;QACvB,UAAU;QACV,IAAI,iBAAiB;YACnB,cAAc,gBAAgB,IAAI;QACpC;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,cAAc;QACd,UAAU;IACZ;IAEA,qBACE,6LAAC;QAAI,KAAK;QAAW,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;0BAC7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC,oIAAA,CAAA,QAAK;wBACJ,KAAK;wBACL,MAAK;wBACL,aAAa,kBAAkB,gBAAgB,IAAI,GAAG;wBACtD,OAAO;wBACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wBACjD,SAAS;wBACT,WAAU;wBACV,UAAU;;;;;;;;;;;;YAIb,wBACC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;wBACpB,0BACC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;gCACP;gCACA,UAAU;4BACZ;;8CAEA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;8CAAK;;;;;;;;;;;;wBAIT,iBAAiB,MAAM,KAAK,kBAC3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;8CAAE;;;;;;;;;;;iDAGL,6LAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,aAAa;8CAE5B,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAe,QAAQ,IAAI;;;;;;kEAC1C,6LAAC;wDAAI,WAAU;;4DACZ,QAAQ,KAAK,kBACZ,6LAAC;gEAAK,WAAU;;kFACd,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAChB,QAAQ,KAAK;;;;;;;4DAGjB,QAAQ,KAAK,kBACZ,6LAAC;gEAAK,WAAU;0EAAY,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;mCAjB5C,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BnC;GAjJgB;KAAA;AAoJT,SAAS,mBAAmB,EACjC,aAAa,EACb,sBAAsB,EACtB,cAAc,6CAA6C,EAC3D,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,WAAW,KAAK,EACQ;;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IAC/F,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,wCAAwC;IACxC,MAAM,uBAAuB,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAE9D,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,WAAW,IAAI,IAAI;gBACtB,yBAAyB,cAAc,KAAK,CAAC,GAAG,KAAK,+BAA+B;;gBACpF;YACF;YAEA,MAAM,WAAW,cAAc,MAAM;yDAAC,CAAA;oBACpC,MAAM,cAAc,WAAW,WAAW;oBAC1C,MAAM,YAAY,aAAa,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;oBAE3D,wFAAwF;oBACxF,MAAM,mBAAmB,WAAW,OAAO,CAAC,OAAO;oBACnD,MAAM,yBAAyB,aAAa,KAAK,EAAE,QAAQ,OAAO,OAAO;oBACzE,MAAM,aAAa,iBAAiB,MAAM,GAAG,KAAK,uBAAuB,QAAQ,CAAC;oBAElF,MAAM,iBAAiB,aAAa,SAAS,EAAE,cAAc,SAAS;oBAEtE,OAAO,aAAa,cAAc;gBACpC;wDAAG,KAAK,CAAC,GAAG,IAAI,sBAAsB;;YAEtC,yBAAyB;QAC3B;uCAAG;QAAC;QAAY;KAAc;IAE9B,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC1E,UAAU;gBACZ;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;gDAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;uCAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,SAAS,aAAa,EAAE;QACxB,cAAc;QACd,UAAU;IACZ;IAEA,MAAM,mBAAmB;QACvB,UAAU;QACV,IAAI,sBAAsB;YACxB,cAAc,qBAAqB,IAAI;QACzC;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,cAAc;QACd,UAAU;IACZ;IAEA,qBACE,6LAAC;QAAI,KAAK;QAAW,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;0BAC7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC,oIAAA,CAAA,QAAK;wBACJ,KAAK;wBACL,MAAK;wBACL,aAAa,uBAAuB,qBAAqB,IAAI,GAAG;wBAChE,OAAO;wBACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wBACjD,SAAS;wBACT,WAAU;wBACV,UAAU;;;;;;;;;;;;YAIb,wBACC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;wBACpB,0BACC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;gCACP;gCACA,UAAU;4BACZ;;8CAEA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;8CAAK;;;;;;;;;;;;wBAIT,sBAAsB,MAAM,KAAK,kBAChC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;8CAAE;;;;;;;;;;;iDAGL,6LAAC;4BAAI,WAAU;sCACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,aAAa;8CAE5B,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAe,aAAa,IAAI;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;;4DACZ,aAAa,SAAS,kBACrB,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAChC,aAAa,SAAS;;;;;;4DAG1B,aAAa,KAAK,kBACjB,6LAAC;gEAAK,WAAU;;kFACd,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAChB,aAAa,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;mCAlBxB,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCxC;IAnJgB;MAAA", "debugId": null}}, {"offset": {"line": 4673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/AppointmentForm.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useForm, Controller } from 'react-hook-form';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';\r\nimport { DateTimeInput } from '@/components/ui/datetime-input';\r\nimport { Calendar, Clock, User, Plus, Trash2, Search, AlertCircle } from 'lucide-react';\r\nimport { useToast } from '@/hooks/use-toast';\r\nimport { format, addMinutes } from 'date-fns';\r\nimport { ptBR } from 'date-fns/locale';\r\nimport { formatDateTimeForInput } from '@/lib/date-utils';\r\nimport { appointmentSchema, type AppointmentFormData } from '@/lib/validations';\r\nimport { useFormSubmission } from '@/hooks/useAsyncOperation';\r\nimport { PatientSearch, ProfessionalSearch } from '@/components/ui/smart-search';\r\n\r\n// Utility functions for Brazilian timezone (UTC-3)\r\nconst brazilianTimezoneOffset = -3 * 60; // -3 hours in minutes\r\n\r\nconst toBrazilianTime = (date: Date | string | null): Date | null => {\r\n  if (!date) return null;\r\n  \r\n  // Se for string, converte para Date\r\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\r\n  \r\n  // Cria uma nova data ajustada para UTC-3\r\n  const utcTime = dateObj.getTime() + (dateObj.getTimezoneOffset() * 60000);\r\n  const brazilianTime = new Date(utcTime + (brazilianTimezoneOffset * 60000));\r\n  \r\n  return brazilianTime;\r\n};\r\n\r\nconst formatDateTimeForBrazilianInput = (dateTime: Date | string | null | undefined): string => {\r\n  if (!dateTime) return '';\r\n  \r\n  try {\r\n    const date = toBrazilianTime(dateTime);\r\n    if (!date || isNaN(date.getTime())) return '';\r\n    \r\n    // Formato para datetime-local: YYYY-MM-DDTHH:mm\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    const hours = String(date.getHours()).padStart(2, '0');\r\n    const minutes = String(date.getMinutes()).padStart(2, '0');\r\n    \r\n    return `${year}-${month}-${day}T${hours}:${minutes}`;\r\n  } catch (error) {\r\n    console.error('[DATETIME] Error formatting date:', error);\r\n    return '';\r\n  }\r\n};\r\n\r\nconst parseBrazilianDateTime = (dateTimeString: string): Date | null => {\r\n  if (!dateTimeString) return null;\r\n  \r\n  try {\r\n    // Se já tem timezone, usa diretamente\r\n    if (dateTimeString.includes('T') && (dateTimeString.includes('Z') || dateTimeString.includes('-03:00'))) {\r\n      return new Date(dateTimeString);\r\n    }\r\n    \r\n    // Se é formato datetime-local (YYYY-MM-DDTHH:mm), adiciona timezone brasileiro\r\n    if (dateTimeString.includes('T')) {\r\n      return new Date(dateTimeString + ':00-03:00');\r\n    }\r\n    \r\n    // Fallback para outros formatos\r\n    const date = new Date(dateTimeString);\r\n    return toBrazilianTime(date);\r\n  } catch (error) {\r\n    console.error('[DATETIME] Error parsing date:', error);\r\n    return null;\r\n  }\r\n};\r\n\r\ntype Patient = {\r\n  id: string;\r\n  name: string;\r\n  email?: string;\r\n  phone?: string;\r\n};\r\n\r\ntype HealthcareProfessional = {\r\n  id: string;\r\n  name: string;\r\n  specialty: string | undefined;\r\n  is_active: boolean;\r\n};\r\n\r\ninterface AppointmentFormProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  patients: Patient[];\r\n  healthcareProfessionals: HealthcareProfessional[];\r\n  initialData?: Partial<AppointmentFormData & { id?: string }>;\r\n  onSubmit: (data: AppointmentFormData & { id?: string }) => Promise<void>;\r\n  loading?: boolean;\r\n}\r\n\r\nconst AppointmentForm: React.FC<AppointmentFormProps> = ({\r\n  open,\r\n  onOpenChange,\r\n  patients,\r\n  healthcareProfessionals,\r\n  initialData,\r\n  onSubmit,\r\n  loading = false\r\n}) => {\r\n  const { toast } = useToast();\r\n  const [activeTab, setActiveTab] = useState('scheduling');\r\n\r\n  // Check if we're in editing mode\r\n  const isEditing = Boolean(initialData?.id);\r\n\r\n  const { execute: submitForm, loading: submitting } = useFormSubmission({\r\n    successMessage: isEditing ? 'Consulta atualizada com sucesso!' : 'Consulta agendada com sucesso!',\r\n    errorMessage: isEditing ? 'Erro ao atualizar consulta' : 'Erro ao agendar consulta'\r\n  });\r\n\r\n  const form = useForm({\r\n    resolver: zodResolver(appointmentSchema),\r\n    defaultValues: {\r\n      title: '',\r\n      description: '',\r\n      patient_id: '',\r\n      healthcare_professional_id: '',\r\n      start_time: '',\r\n      end_time: '',\r\n      status: 'scheduled',\r\n      notes: '',\r\n      has_recurrence: false,\r\n      recurrence_type: 'weekly',\r\n      recurrence_interval: 1,\r\n      recurrence_days: [],\r\n      recurrence_end_type: 'never',\r\n      recurrence_end_date: '',\r\n      recurrence_count: 1,\r\n    }\r\n  });\r\n\r\n  const { control, handleSubmit, watch, setValue, reset, formState: { errors } } = form;\r\n\r\n  const watchedValues = watch();\r\n\r\n  // Initialize form with initial data\r\n  useEffect(() => {\r\n    if (initialData && open) {\r\n      console.log('[DATETIME] Initializing form with data:', initialData);\r\n      \r\n      const formattedData = {\r\n        ...initialData,\r\n        start_time: formatDateTimeForBrazilianInput(initialData.start_time),\r\n        end_time: formatDateTimeForBrazilianInput(initialData.end_time),\r\n        patient_id: initialData.patient_id || '',\r\n        healthcare_professional_id: initialData.healthcare_professional_id || '',\r\n      };\r\n      \r\n      console.log('[DATETIME] Formatted data for form:', formattedData);\r\n      reset(formattedData);\r\n    }\r\n  }, [initialData, open, reset]);\r\n\r\n  // Auto-generate title when patient changes\r\n  useEffect(() => {\r\n    if (watchedValues.patient_id) {\r\n      const patient = patients.find(p => p.id === watchedValues.patient_id);\r\n      if (patient) {\r\n        setValue('title', `Consulta - ${patient.name}`);\r\n      }\r\n    }\r\n  }, [watchedValues.patient_id, patients, setValue]);\r\n\r\n  // Auto-calculate end time based on start time (Brazilian timezone)\r\n  // Only auto-calculate if end_time is not already set (e.g., from FullCalendar selection)\r\n  useEffect(() => {\r\n    console.log('[DATETIME] Auto-calculating end time, start_time:', watchedValues.start_time, 'end_time:', watchedValues.end_time);\r\n\r\n    if (watchedValues.start_time && !watchedValues.end_time) {\r\n      try {\r\n        const startTime = parseBrazilianDateTime(watchedValues.start_time);\r\n\r\n        console.log('[DATETIME] Parsed start time:', startTime, 'Valid:', startTime && !isNaN(startTime.getTime()));\r\n\r\n        if (startTime && !isNaN(startTime.getTime())) {\r\n          // Default 60 minutes duration for consultations\r\n          const defaultDuration = 60;\r\n\r\n          console.log('[DATETIME] Total duration:', defaultDuration, 'minutes');\r\n\r\n          const endTime = addMinutes(startTime, defaultDuration);\r\n          const formattedEndTime = formatDateTimeForBrazilianInput(endTime);\r\n\r\n          console.log('[DATETIME] Calculated end time:', endTime, 'Formatted:', formattedEndTime);\r\n          setValue('end_time', formattedEndTime);\r\n        }\r\n      } catch (error) {\r\n        console.error('[DATETIME] Error calculating end time:', error);\r\n      }\r\n    }\r\n  }, [watchedValues.start_time, watchedValues.end_time, setValue]);\r\n\r\n  const onFormSubmit = async (data: AppointmentFormData) => {\r\n    console.log('[DATETIME] Form submitted with data:', data);\r\n    \r\n    try {\r\n      // Parse and validate dates in Brazilian timezone\r\n      const startTime = parseBrazilianDateTime(data.start_time);\r\n      const endTime = parseBrazilianDateTime(data.end_time);\r\n      \r\n      if (!startTime || !endTime) {\r\n        toast({\r\n          title: 'Erro',\r\n          description: 'Datas inválidas. Por favor, verifique os horários.',\r\n          variant: 'destructive'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // Convert to ISO string with Brazilian timezone\r\n      const formattedData = {\r\n        ...data,\r\n        type: 'consultation' as const, // Default type since field was removed\r\n        start_time: startTime.toISOString(),\r\n        end_time: endTime.toISOString(),\r\n      };\r\n      \r\n      console.log('[DATETIME] Formatted data for submission:', formattedData);\r\n\r\n      await submitForm(async () => {\r\n        const appointmentData = {\r\n          ...formattedData,\r\n          status: formattedData.status || 'scheduled',\r\n          ...(isEditing && initialData?.id && { id: initialData.id }),\r\n        };\r\n\r\n        console.log('[DATETIME] Final appointment data:', appointmentData);\r\n        await onSubmit(appointmentData);\r\n\r\n        // Reset form\r\n        reset();\r\n        setActiveTab('scheduling');\r\n        onOpenChange(false);\r\n      });\r\n    } catch (error) {\r\n      console.error('[DATETIME] Error submitting form:', error);\r\n      toast({\r\n        title: 'Erro',\r\n        description: 'Erro ao processar os dados. Por favor, tente novamente.',\r\n        variant: 'destructive'\r\n      });\r\n    }\r\n  };\r\n\r\n  const toggleRecurrenceDay = (day: number) => {\r\n    const currentDays = watchedValues.recurrence_days || [];\r\n    const newDays = currentDays.includes(day)\r\n      ? currentDays.filter(d => d !== day)\r\n      : [...currentDays, day];\r\n    setValue('recurrence_days', newDays);\r\n  };\r\n\r\n  const weekDays = [\r\n    { value: 1, label: 'D' }, // Domingo\r\n    { value: 2, label: 'S' }, // Segunda\r\n    { value: 3, label: 'T' }, // Terça\r\n    { value: 4, label: 'Q' }, // Quarta\r\n    { value: 5, label: 'Q' }, // Quinta\r\n    { value: 6, label: 'S' }, // Sexta\r\n    { value: 7, label: 'S' }, // Sábado\r\n  ];\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-[800px] max-h-[90vh] overflow-y-auto\">\r\n        <DialogHeader>\r\n          <DialogTitle>{isEditing ? 'Editar Consulta' : 'Agendar Nova Consulta'}</DialogTitle>\r\n          <DialogDescription>\r\n            {isEditing ? 'Edite os dados da consulta' : 'Preencha os dados para agendar uma nova consulta'}\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <form onSubmit={handleSubmit(onFormSubmit)}>\r\n          <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\r\n            <TabsList className=\"grid w-full grid-cols-1\">\r\n              <TabsTrigger value=\"scheduling\" className=\"flex items-center gap-2\">\r\n                <Calendar className=\"h-4 w-4\" />\r\n                Agendamento\r\n              </TabsTrigger>\r\n            </TabsList>\r\n\r\n            <TabsContent value=\"scheduling\" className=\"space-y-4 mt-4\">\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor=\"patient\">Paciente *</Label>\r\n                  <Controller\r\n                    name=\"patient_id\"\r\n                    control={control}\r\n                    render={({ field }) => (\r\n                      <PatientSearch\r\n                        patients={patients}\r\n                        selectedPatientId={field.value}\r\n                        onSelect={field.onChange}\r\n                        placeholder=\"Buscar paciente por nome ou telefone...\"\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.patient_id && (\r\n                    <p className=\"text-sm text-destructive flex items-center gap-1\">\r\n                      <AlertCircle className=\"h-3 w-3\" />\r\n                      {errors.patient_id.message}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor=\"professional\">Profissional</Label>\r\n                  <Controller\r\n                    name=\"healthcare_professional_id\"\r\n                    control={control}\r\n                    render={({ field }) => (\r\n                      <ProfessionalSearch\r\n                        professionals={healthcareProfessionals.filter(prof => prof.is_active)}\r\n                        selectedProfessionalId={field.value}\r\n                        onSelect={field.onChange}\r\n                        placeholder=\"Buscar profissional por nome ou especialidade...\"\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.healthcare_professional_id && (\r\n                    <p className=\"text-sm text-destructive flex items-center gap-1\">\r\n                      <AlertCircle className=\"h-3 w-3\" />\r\n                      {errors.healthcare_professional_id.message}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"title\">Título *</Label>\r\n                <Controller\r\n                    name=\"title\"\r\n                    control={control}\r\n                    render={({ field }) => (\r\n                      <Input\r\n                        id=\"title\"\r\n                        {...field}\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.title && (\r\n                    <p className=\"text-sm text-destructive flex items-center gap-1\">\r\n                      <AlertCircle className=\"h-3 w-3\" />\r\n                      {errors.title.message}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor=\"status\">Status</Label>\r\n                  <Controller\r\n                    name=\"status\"\r\n                    control={control}\r\n                    render={({ field }) => (\r\n                      <Select value={field.value} onValueChange={field.onChange}>\r\n                        <SelectTrigger>\r\n                          <SelectValue />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          <SelectItem value=\"scheduled\">Agendado</SelectItem>\r\n                          <SelectItem value=\"confirmed\">Confirmado</SelectItem>\r\n                          <SelectItem value=\"in_progress\">Em Andamento</SelectItem>\r\n                          <SelectItem value=\"cancelled\">Cancelado</SelectItem>\r\n                        </SelectContent>\r\n                      </Select>\r\n                    )}\r\n                  />\r\n                  {errors.status && (\r\n                    <p className=\"text-sm text-destructive flex items-center gap-1\">\r\n                      <AlertCircle className=\"h-3 w-3\" />\r\n                      {errors.status.message}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"space-y-2\">\r\n                  {/* Empty space for layout balance */}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor=\"start_time\">Data/Hora Início *</Label>\r\n                  <Controller\r\n                    name=\"start_time\"\r\n                    control={control}\r\n                    render={({ field }) => (\r\n                      <DateTimeInput\r\n                        id=\"start_time\"\r\n                        value={field.value}\r\n                        onChange={field.onChange}\r\n                        placeholder=\"DD/MM/AAAA HH:mm\"\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.start_time && (\r\n                    <p className=\"text-sm text-destructive flex items-center gap-1\">\r\n                      <AlertCircle className=\"h-3 w-3\" />\r\n                      {errors.start_time.message}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor=\"end_time\">Data/Hora Fim *</Label>\r\n                  <Controller\r\n                    name=\"end_time\"\r\n                    control={control}\r\n                    render={({ field }) => (\r\n                      <DateTimeInput\r\n                        id=\"end_time\"\r\n                        value={field.value}\r\n                        onChange={field.onChange}\r\n                        placeholder=\"DD/MM/AAAA HH:mm\"\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.end_time && (\r\n                    <p className=\"text-sm text-destructive flex items-center gap-1\">\r\n                      <AlertCircle className=\"h-3 w-3\" />\r\n                      {errors.end_time.message}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"description\">Descrição</Label>\r\n                <Controller\r\n                  name=\"description\"\r\n                  control={control}\r\n                  render={({ field }) => (\r\n                    <Textarea\r\n                      id=\"description\"\r\n                      placeholder=\"Descrição da consulta...\"\r\n                      {...field}\r\n                    />\r\n                  )}\r\n                />\r\n                {errors.description && (\r\n                  <p className=\"text-sm text-destructive flex items-center gap-1\">\r\n                    <AlertCircle className=\"h-3 w-3\" />\r\n                    {errors.description.message}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              {/* Recurrence Section */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <Controller\r\n                      name=\"has_recurrence\"\r\n                      control={control}\r\n                      render={({ field }) => (\r\n                        <Checkbox\r\n                          id=\"has_recurrence\"\r\n                          checked={field.value}\r\n                          onCheckedChange={field.onChange}\r\n                        />\r\n                      )}\r\n                    />\r\n                    <Label htmlFor=\"has_recurrence\" className=\"text-sm font-medium\">\r\n                      Recorrência Personalizada\r\n                    </Label>\r\n                  </div>\r\n                </CardHeader>\r\n\r\n                {watchedValues.has_recurrence && (\r\n                  <CardContent className=\"space-y-4\">\r\n                    <div className=\"grid grid-cols-2 gap-4\">\r\n                      <div className=\"space-y-2\">\r\n                        <Label>Repetir a cada:</Label>\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <Controller\r\n                            name=\"recurrence_interval\"\r\n                            control={control}\r\n                            render={({ field }) => (\r\n                              <Input\r\n                                type=\"number\"\r\n                                min=\"1\"\r\n                                {...field}\r\n                                onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}\r\n                                className=\"w-20\"\r\n                              />\r\n                            )}\r\n                          />\r\n                          <Controller\r\n                            name=\"recurrence_type\"\r\n                            control={control}\r\n                            render={({ field }) => (\r\n                              <Select value={field.value} onValueChange={field.onChange}>\r\n                                <SelectTrigger>\r\n                                  <SelectValue />\r\n                                </SelectTrigger>\r\n                                <SelectContent>\r\n                                  <SelectItem value=\"daily\">dia(s)</SelectItem>\r\n                                  <SelectItem value=\"weekly\">semana(s)</SelectItem>\r\n                                  <SelectItem value=\"monthly\">mês(es)</SelectItem>\r\n                                </SelectContent>\r\n                              </Select>\r\n                            )}\r\n                          />\r\n                        </div>\r\n                      </div>\r\n\r\n                      {watchedValues.recurrence_type === 'weekly' && (\r\n                        <div className=\"space-y-2\">\r\n                          <Label>Repetir:</Label>\r\n                          <div className=\"flex gap-1\">\r\n                            {weekDays.map(day => (\r\n                              <Button\r\n                                key={day.value}\r\n                                type=\"button\"\r\n                                variant={(watchedValues.recurrence_days || []).includes(day.value) ? 'default' : 'outline'}\r\n                                size=\"sm\"\r\n                                className=\"w-8 h-8 p-0\"\r\n                                onClick={() => toggleRecurrenceDay(day.value)}\r\n                              >\r\n                                {day.label}\r\n                              </Button>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <Label>Termina em:</Label>\r\n                      <Controller\r\n                        name=\"recurrence_end_type\"\r\n                        control={control}\r\n                        render={({ field }) => (\r\n                          <RadioGroup value={field.value} onValueChange={field.onChange}>\r\n\r\n                        <div className=\"flex items-center space-x-2\">\r\n                          <RadioGroupItem value=\"never\" id=\"never\" />\r\n                          <Label htmlFor=\"never\">Nunca</Label>\r\n                        </div>\r\n                        \r\n                            <div className=\"flex items-center space-x-2\">\r\n                              <RadioGroupItem value=\"date\" id=\"end_date\" />\r\n                              <Label htmlFor=\"end_date\">Em</Label>\r\n                              <Controller\r\n                                name=\"recurrence_end_date\"\r\n                                control={control}\r\n                                render={({ field }) => (\r\n                                  <Input\r\n                                    type=\"date\"\r\n                                    {...field}\r\n                                    disabled={watchedValues.recurrence_end_type !== 'date'}\r\n                                    className=\"w-40\"\r\n                                  />\r\n                                )}\r\n                              />\r\n                            </div>\r\n\r\n                            <div className=\"flex items-center space-x-2\">\r\n                              <RadioGroupItem value=\"count\" id=\"end_count\" />\r\n                              <Label htmlFor=\"end_count\">Após</Label>\r\n                              <Controller\r\n                                name=\"recurrence_count\"\r\n                                control={control}\r\n                                render={({ field }) => (\r\n                                  <Input\r\n                                    type=\"number\"\r\n                                    min=\"1\"\r\n                                    {...field}\r\n                                    onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}\r\n                                    disabled={watchedValues.recurrence_end_type !== 'count'}\r\n                                    className=\"w-20\"\r\n                                  />\r\n                                )}\r\n                              />\r\n                              <Label>ocorrências</Label>\r\n                            </div>\r\n                          </RadioGroup>\r\n                        )}\r\n                      />\r\n                    </div>\r\n                  </CardContent>\r\n                )}\r\n              </Card>\r\n            </TabsContent>\r\n\r\n          </Tabs>\r\n\r\n          <DialogFooter className=\"mt-6\">\r\n            <Button type=\"button\" variant=\"outline\" onClick={() => onOpenChange(false)}>\r\n              Cancelar\r\n            </Button>\r\n            <Button type=\"submit\" disabled={submitting || loading}>\r\n              {submitting\r\n                ? (isEditing ? 'Salvando...' : 'Agendando...')\r\n                : (isEditing ? 'Salvar Alterações' : 'Agendar Consulta')\r\n              }\r\n            </Button>\r\n          </DialogFooter>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default AppointmentForm;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAGA;AACA;AACA;;;AAxBA;;;;;;;;;;;;;;;;;;;;;AA0BA,mDAAmD;AACnD,MAAM,0BAA0B,CAAC,IAAI,IAAI,sBAAsB;AAE/D,MAAM,kBAAkB,CAAC;IACvB,IAAI,CAAC,MAAM,OAAO;IAElB,oCAAoC;IACpC,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,yCAAyC;IACzC,MAAM,UAAU,QAAQ,OAAO,KAAM,QAAQ,iBAAiB,KAAK;IACnE,MAAM,gBAAgB,IAAI,KAAK,UAAW,0BAA0B;IAEpE,OAAO;AACT;AAEA,MAAM,kCAAkC,CAAC;IACvC,IAAI,CAAC,UAAU,OAAO;IAEtB,IAAI;QACF,MAAM,OAAO,gBAAgB;QAC7B,IAAI,CAAC,QAAQ,MAAM,KAAK,OAAO,KAAK,OAAO;QAE3C,gDAAgD;QAChD,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,QAAQ,OAAO,KAAK,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;QACtD,MAAM,MAAM,OAAO,KAAK,OAAO,IAAI,QAAQ,CAAC,GAAG;QAC/C,MAAM,QAAQ,OAAO,KAAK,QAAQ,IAAI,QAAQ,CAAC,GAAG;QAClD,MAAM,UAAU,OAAO,KAAK,UAAU,IAAI,QAAQ,CAAC,GAAG;QAEtD,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS;IACtD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAEA,MAAM,yBAAyB,CAAC;IAC9B,IAAI,CAAC,gBAAgB,OAAO;IAE5B,IAAI;QACF,sCAAsC;QACtC,IAAI,eAAe,QAAQ,CAAC,QAAQ,CAAC,eAAe,QAAQ,CAAC,QAAQ,eAAe,QAAQ,CAAC,SAAS,GAAG;YACvG,OAAO,IAAI,KAAK;QAClB;QAEA,+EAA+E;QAC/E,IAAI,eAAe,QAAQ,CAAC,MAAM;YAChC,OAAO,IAAI,KAAK,iBAAiB;QACnC;QAEA,gCAAgC;QAChC,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,gBAAgB;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AA0BA,MAAM,kBAAkD,CAAC,EACvD,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,uBAAuB,EACvB,WAAW,EACX,QAAQ,EACR,UAAU,KAAK,EAChB;;IACC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,iCAAiC;IACjC,MAAM,YAAY,QAAQ,aAAa;IAEvC,MAAM,EAAE,SAAS,UAAU,EAAE,SAAS,UAAU,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE;QACrE,gBAAgB,YAAY,qCAAqC;QACjE,cAAc,YAAY,+BAA+B;IAC3D;IAEA,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACnB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE,4HAAA,CAAA,oBAAiB;QACvC,eAAe;YACb,OAAO;YACP,aAAa;YACb,YAAY;YACZ,4BAA4B;YAC5B,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,OAAO;YACP,gBAAgB;YAChB,iBAAiB;YACjB,qBAAqB;YACrB,iBAAiB,EAAE;YACnB,qBAAqB;YACrB,qBAAqB;YACrB,kBAAkB;QACpB;IACF;IAEA,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,GAAG;IAEjF,MAAM,gBAAgB;IAEtB,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,eAAe,MAAM;gBACvB,QAAQ,GAAG,CAAC,2CAA2C;gBAEvD,MAAM,gBAAgB;oBACpB,GAAG,WAAW;oBACd,YAAY,gCAAgC,YAAY,UAAU;oBAClE,UAAU,gCAAgC,YAAY,QAAQ;oBAC9D,YAAY,YAAY,UAAU,IAAI;oBACtC,4BAA4B,YAAY,0BAA0B,IAAI;gBACxE;gBAEA,QAAQ,GAAG,CAAC,uCAAuC;gBACnD,MAAM;YACR;QACF;oCAAG;QAAC;QAAa;QAAM;KAAM;IAE7B,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,cAAc,UAAU,EAAE;gBAC5B,MAAM,UAAU,SAAS,IAAI;yDAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc,UAAU;;gBACpE,IAAI,SAAS;oBACX,SAAS,SAAS,CAAC,WAAW,EAAE,QAAQ,IAAI,EAAE;gBAChD;YACF;QACF;oCAAG;QAAC,cAAc,UAAU;QAAE;QAAU;KAAS;IAEjD,mEAAmE;IACnE,yFAAyF;IACzF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,QAAQ,GAAG,CAAC,qDAAqD,cAAc,UAAU,EAAE,aAAa,cAAc,QAAQ;YAE9H,IAAI,cAAc,UAAU,IAAI,CAAC,cAAc,QAAQ,EAAE;gBACvD,IAAI;oBACF,MAAM,YAAY,uBAAuB,cAAc,UAAU;oBAEjE,QAAQ,GAAG,CAAC,iCAAiC,WAAW,UAAU,aAAa,CAAC,MAAM,UAAU,OAAO;oBAEvG,IAAI,aAAa,CAAC,MAAM,UAAU,OAAO,KAAK;wBAC5C,gDAAgD;wBAChD,MAAM,kBAAkB;wBAExB,QAAQ,GAAG,CAAC,8BAA8B,iBAAiB;wBAE3D,MAAM,UAAU,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,WAAW;wBACtC,MAAM,mBAAmB,gCAAgC;wBAEzD,QAAQ,GAAG,CAAC,mCAAmC,SAAS,cAAc;wBACtE,SAAS,YAAY;oBACvB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0CAA0C;gBAC1D;YACF;QACF;oCAAG;QAAC,cAAc,UAAU;QAAE,cAAc,QAAQ;QAAE;KAAS;IAE/D,MAAM,eAAe,OAAO;QAC1B,QAAQ,GAAG,CAAC,wCAAwC;QAEpD,IAAI;YACF,iDAAiD;YACjD,MAAM,YAAY,uBAAuB,KAAK,UAAU;YACxD,MAAM,UAAU,uBAAuB,KAAK,QAAQ;YAEpD,IAAI,CAAC,aAAa,CAAC,SAAS;gBAC1B,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;gBACA;YACF;YAEA,gDAAgD;YAChD,MAAM,gBAAgB;gBACpB,GAAG,IAAI;gBACP,MAAM;gBACN,YAAY,UAAU,WAAW;gBACjC,UAAU,QAAQ,WAAW;YAC/B;YAEA,QAAQ,GAAG,CAAC,6CAA6C;YAEzD,MAAM,WAAW;gBACf,MAAM,kBAAkB;oBACtB,GAAG,aAAa;oBAChB,QAAQ,cAAc,MAAM,IAAI;oBAChC,GAAI,aAAa,aAAa,MAAM;wBAAE,IAAI,YAAY,EAAE;oBAAC,CAAC;gBAC5D;gBAEA,QAAQ,GAAG,CAAC,sCAAsC;gBAClD,MAAM,SAAS;gBAEf,aAAa;gBACb;gBACA,aAAa;gBACb,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,cAAc,cAAc,eAAe,IAAI,EAAE;QACvD,MAAM,UAAU,YAAY,QAAQ,CAAC,OACjC,YAAY,MAAM,CAAC,CAAA,IAAK,MAAM,OAC9B;eAAI;YAAa;SAAI;QACzB,SAAS,mBAAmB;IAC9B;IAEA,MAAM,WAAW;QACf;YAAE,OAAO;YAAG,OAAO;QAAI;QACvB;YAAE,OAAO;YAAG,OAAO;QAAI;QACvB;YAAE,OAAO;YAAG,OAAO;QAAI;QACvB;YAAE,OAAO;YAAG,OAAO;QAAI;QACvB;YAAE,OAAO;YAAG,OAAO;QAAI;QACvB;YAAE,OAAO;YAAG,OAAO;QAAI;QACvB;YAAE,OAAO;YAAG,OAAO;QAAI;KACxB;IAED,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;sCAAE,YAAY,oBAAoB;;;;;;sCAC9C,6LAAC,qIAAA,CAAA,oBAAiB;sCACf,YAAY,+BAA+B;;;;;;;;;;;;8BAIhD,6LAAC;oBAAK,UAAU,aAAa;;sCAC3B,6LAAC,mIAAA,CAAA,OAAI;4BAAC,OAAO;4BAAW,eAAe;4BAAc,WAAU;;8CAC7D,6LAAC,mIAAA,CAAA,WAAQ;oCAAC,WAAU;8CAClB,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAa,WAAU;;0DACxC,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAKpC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAa,WAAU;;sDACxC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAU;;;;;;sEACzB,6LAAC,iKAAA,CAAA,aAAU;4DACT,MAAK;4DACL,SAAS;4DACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,8IAAA,CAAA,gBAAa;oEACZ,UAAU;oEACV,mBAAmB,MAAM,KAAK;oEAC9B,UAAU,MAAM,QAAQ;oEACxB,aAAY;;;;;;;;;;;wDAIjB,OAAO,UAAU,kBAChB,6LAAC;4DAAE,WAAU;;8EACX,6LAAC,uNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEACtB,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;;8DAKhC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAe;;;;;;sEAC9B,6LAAC,iKAAA,CAAA,aAAU;4DACT,MAAK;4DACL,SAAS;4DACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,8IAAA,CAAA,qBAAkB;oEACjB,eAAe,wBAAwB,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS;oEACpE,wBAAwB,MAAM,KAAK;oEACnC,UAAU,MAAM,QAAQ;oEACxB,aAAY;;;;;;;;;;;wDAIjB,OAAO,0BAA0B,kBAChC,6LAAC;4DAAE,WAAU;;8EACX,6LAAC,uNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEACtB,OAAO,0BAA0B,CAAC,OAAO;;;;;;;;;;;;;;;;;;;sDAMlD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,6LAAC,iKAAA,CAAA,aAAU;oDACP,MAAK;oDACL,SAAS;oDACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACF,GAAG,KAAK;;;;;;;;;;;gDAId,OAAO,KAAK,kBACX,6LAAC;oDAAE,WAAU;;sEACX,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACtB,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;sDAK7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAS;;;;;;sEACxB,6LAAC,iKAAA,CAAA,aAAU;4DACT,MAAK;4DACL,SAAS;4DACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO,MAAM,KAAK;oEAAE,eAAe,MAAM,QAAQ;;sFACvD,6LAAC,qIAAA,CAAA,gBAAa;sFACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sFAEd,6LAAC,qIAAA,CAAA,gBAAa;;8FACZ,6LAAC,qIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAY;;;;;;8FAC9B,6LAAC,qIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAY;;;;;;8FAC9B,6LAAC,qIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAc;;;;;;8FAChC,6LAAC,qIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAY;;;;;;;;;;;;;;;;;;;;;;;wDAKrC,OAAO,MAAM,kBACZ,6LAAC;4DAAE,WAAU;;8EACX,6LAAC,uNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEACtB,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;8DAK5B,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAKjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAa;;;;;;sEAC5B,6LAAC,iKAAA,CAAA,aAAU;4DACT,MAAK;4DACL,SAAS;4DACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,gJAAA,CAAA,gBAAa;oEACZ,IAAG;oEACH,OAAO,MAAM,KAAK;oEAClB,UAAU,MAAM,QAAQ;oEACxB,aAAY;;;;;;;;;;;wDAIjB,OAAO,UAAU,kBAChB,6LAAC;4DAAE,WAAU;;8EACX,6LAAC,uNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEACtB,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;;8DAKhC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAW;;;;;;sEAC1B,6LAAC,iKAAA,CAAA,aAAU;4DACT,MAAK;4DACL,SAAS;4DACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,gJAAA,CAAA,gBAAa;oEACZ,IAAG;oEACH,OAAO,MAAM,KAAK;oEAClB,UAAU,MAAM,QAAQ;oEACxB,aAAY;;;;;;;;;;;wDAIjB,OAAO,QAAQ,kBACd,6LAAC;4DAAE,WAAU;;8EACX,6LAAC,uNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEACtB,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;;sDAMhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,6LAAC,iKAAA,CAAA,aAAU;oDACT,MAAK;oDACL,SAAS;oDACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,uIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,aAAY;4DACX,GAAG,KAAK;;;;;;;;;;;gDAId,OAAO,WAAW,kBACjB,6LAAC;oDAAE,WAAU;;sEACX,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACtB,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;;sDAMjC,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;8DACT,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iKAAA,CAAA,aAAU;gEACT,MAAK;gEACL,SAAS;gEACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,uIAAA,CAAA,WAAQ;wEACP,IAAG;wEACH,SAAS,MAAM,KAAK;wEACpB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;0EAIrC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAiB,WAAU;0EAAsB;;;;;;;;;;;;;;;;;gDAMnE,cAAc,cAAc,kBAC3B,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;sFAAC;;;;;;sFACP,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,iKAAA,CAAA,aAAU;oFACT,MAAK;oFACL,SAAS;oFACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,oIAAA,CAAA,QAAK;4FACJ,MAAK;4FACL,KAAI;4FACH,GAAG,KAAK;4FACT,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4FAC5D,WAAU;;;;;;;;;;;8FAIhB,6LAAC,iKAAA,CAAA,aAAU;oFACT,MAAK;oFACL,SAAS;oFACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,qIAAA,CAAA,SAAM;4FAAC,OAAO,MAAM,KAAK;4FAAE,eAAe,MAAM,QAAQ;;8GACvD,6LAAC,qIAAA,CAAA,gBAAa;8GACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;8GAEd,6LAAC,qIAAA,CAAA,gBAAa;;sHACZ,6LAAC,qIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAQ;;;;;;sHAC1B,6LAAC,qIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAS;;;;;;sHAC3B,6LAAC,qIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gEAQvC,cAAc,eAAe,KAAK,0BACjC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;sFAAC;;;;;;sFACP,6LAAC;4EAAI,WAAU;sFACZ,SAAS,GAAG,CAAC,CAAA,oBACZ,6LAAC,qIAAA,CAAA,SAAM;oFAEL,MAAK;oFACL,SAAS,CAAC,cAAc,eAAe,IAAI,EAAE,EAAE,QAAQ,CAAC,IAAI,KAAK,IAAI,YAAY;oFACjF,MAAK;oFACL,WAAU;oFACV,SAAS,IAAM,oBAAoB,IAAI,KAAK;8FAE3C,IAAI,KAAK;mFAPL,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sEAe1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,oIAAA,CAAA,QAAK;8EAAC;;;;;;8EACP,6LAAC,iKAAA,CAAA,aAAU;oEACT,MAAK;oEACL,SAAS;oEACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,6IAAA,CAAA,aAAU;4EAAC,OAAO,MAAM,KAAK;4EAAE,eAAe,MAAM,QAAQ;;8FAE/D,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,6IAAA,CAAA,iBAAc;4FAAC,OAAM;4FAAQ,IAAG;;;;;;sGACjC,6LAAC,oIAAA,CAAA,QAAK;4FAAC,SAAQ;sGAAQ;;;;;;;;;;;;8FAGrB,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,6IAAA,CAAA,iBAAc;4FAAC,OAAM;4FAAO,IAAG;;;;;;sGAChC,6LAAC,oIAAA,CAAA,QAAK;4FAAC,SAAQ;sGAAW;;;;;;sGAC1B,6LAAC,iKAAA,CAAA,aAAU;4FACT,MAAK;4FACL,SAAS;4FACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,oIAAA,CAAA,QAAK;oGACJ,MAAK;oGACJ,GAAG,KAAK;oGACT,UAAU,cAAc,mBAAmB,KAAK;oGAChD,WAAU;;;;;;;;;;;;;;;;;8FAMlB,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,6IAAA,CAAA,iBAAc;4FAAC,OAAM;4FAAQ,IAAG;;;;;;sGACjC,6LAAC,oIAAA,CAAA,QAAK;4FAAC,SAAQ;sGAAY;;;;;;sGAC3B,6LAAC,iKAAA,CAAA,aAAU;4FACT,MAAK;4FACL,SAAS;4FACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,oIAAA,CAAA,QAAK;oGACJ,MAAK;oGACL,KAAI;oGACH,GAAG,KAAK;oGACT,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oGAC5D,UAAU,cAAc,mBAAmB,KAAK;oGAChD,WAAU;;;;;;;;;;;sGAIhB,6LAAC,oIAAA,CAAA,QAAK;sGAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAa3B,6LAAC,qIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,SAAS,IAAM,aAAa;8CAAQ;;;;;;8CAG5E,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU,cAAc;8CAC3C,aACI,YAAY,gBAAgB,iBAC5B,YAAY,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;GAjgBM;;QASc,+HAAA,CAAA,WAAQ;QAM2B,oIAAA,CAAA,oBAAiB;QAKzD,iKAAA,CAAA,UAAO;;;KApBhB;uCAmgBS", "debugId": null}}, {"offset": {"line": 5900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/app/dashboard/agenda/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState, useEffect } from 'react';\nimport { Calendar } from '@/components/ui/calendar';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Calendar as CalendarIcon, Clock, Plus, Users, Grid3X3, X, Play, FileText, CheckCircle } from 'lucide-react';\nimport ConfirmDialog from '@/components/ConfirmDialog';\nimport TimeSlotCard from '@/components/TimeSlotCard';\nimport { useToast } from '@/hooks/use-toast';\nimport { format } from 'date-fns';\nimport { ptBR } from 'date-fns/locale';\nimport { formatDateBR, formatTimeBR, formatDateTimeBR, getAppointmentStatusBR, getAppointmentTypeBR, toLocalISOString } from '@/lib/date-utils';\nimport { makeAuthenticatedRequest } from '@/lib/api-client';\nimport { getStatusBadgeVariant, getStatusCardClasses, getStatusTextBR, type AppointmentStatus } from '@/lib/status-colors';\nimport { generateTimeSlots, type TimeSlot } from '@/lib/time-slots';\nimport FullCalendarView from '@/components/FullCalendarView';\nimport AppointmentForm from '@/components/AppointmentForm';\nimport type { DateSelectArg } from '@fullcalendar/core';\nimport { useRouter } from 'next/navigation';\nimport { usePermissions } from '@/hooks/usePermissions';\nimport { useSelectedProfessional } from '@/contexts/SelectedProfessionalContext';\nimport { debugLog } from '@/lib/debug-utils';\n\ntype Patient = {\n  id: string;\n  name: string;\n  email?: string;\n  phone?: string;\n};\n\ntype HealthcareProfessional = {\n  id: string;\n  name: string;\n  specialty: string | null;\n  is_active: boolean;\n  user_id: string;\n};\n\ntype Appointment = {\n  id: string;\n  title: string;\n  description: string | null;\n  patient_id: string;\n  patient_name?: string;\n  healthcare_professional_id: string | null;\n  healthcare_professional_name?: string;\n  start_time: string;\n  end_time: string;\n  type: string;\n  status: string;\n  total_price: number | null;\n};\n\ntype WorkingDay = {\n  enabled: boolean;\n  working_hours_start: string;\n  working_hours_end: string;\n  break_intervals: { start: string; end: string; }[];\n};\n\ntype ClinicSettings = {\n  working_hours_start?: string;\n  working_hours_end?: string;\n  working_days?: number[];\n  appointment_duration_minutes: number;\n  allow_weekend_appointments?: boolean;\n  clinic_name?: string;\n  timezone?: string;\n  weekly_schedule?: {\n    monday: WorkingDay;\n    tuesday: WorkingDay;\n    wednesday: WorkingDay;\n    thursday: WorkingDay;\n    friday: WorkingDay;\n    saturday: WorkingDay;\n    sunday: WorkingDay;\n  };\n};\n\nconst AgendaPage = () => {\n  const [selectedDate, setSelectedDate] = useState<Date>(new Date());\n  const [appointments, setAppointments] = useState<Appointment[]>([]);\n  const [allAppointments, setAllAppointments] = useState<Appointment[]>([]);\n  const [patients, setPatients] = useState<Patient[]>([]);\n  const [healthcareProfessionals, setHealthcareProfessionals] = useState<HealthcareProfessional[]>([]);\n  const [clinicSettings, setClinicSettings] = useState<ClinicSettings | null>(null);\n  const [appointmentBlocks, setAppointmentBlocks] = useState<any[]>([]);\n  const [allAppointmentBlocks, setAllAppointmentBlocks] = useState<any[]>([]);\n  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [appointmentFormOpen, setAppointmentFormOpen] = useState(false);\n  const [currentView, setCurrentView] = useState<'calendar' | 'fullcalendar'>('calendar');\n  const [appointmentFormData, setAppointmentFormData] = useState<any>(null);\n\n  // Use global selected professional state\n  const { selectedProfessional, setSelectedProfessional } = useSelectedProfessional();\n\n  // Confirmation dialog states\n  const [confirmDialog, setConfirmDialog] = useState<{\n    open: boolean;\n    title: string;\n    description: string;\n    onConfirm: () => void;\n    variant?: 'default' | 'destructive';\n  }>({\n    open: false,\n    title: '',\n    description: '',\n    onConfirm: () => {},\n    variant: 'default'\n  });\n  const { toast } = useToast();\n  const router = useRouter();\n  const { \n    canStartConsultation, \n    canViewMedicalRecords, \n    isAdmin, \n    isHealthcareProfessional, \n    isSecretary,\n    accessibleHealthcareProfessionals,\n    userRole \n  } = usePermissions();\n\n  // Fetch initial data that doesn't depend on selected date\n  useEffect(() => {\n    fetchAllAppointments();\n    fetchAllAppointmentBlocks();\n    fetchPatients();\n    // Don't fetch clinic settings here - we'll fetch them when professional is selected\n  }, []);\n\n  // Fetch healthcare professionals when permissions are ready\n  useEffect(() => {\n    if (userRole) {\n      fetchHealthcareProfessionals();\n    }\n  }, [userRole, isSecretary, accessibleHealthcareProfessionals]);\n\n  // Auto-select first healthcare professional for better performance\n  useEffect(() => {\n    if (healthcareProfessionals.length > 0 && selectedProfessional === 'current') {\n      const firstProfessional = healthcareProfessionals.find(prof => prof.is_active);\n      if (firstProfessional) {\n        setSelectedProfessional(firstProfessional.id);\n      }\n    }\n  }, [healthcareProfessionals, selectedProfessional, setSelectedProfessional]);\n\n  // Fetch date-specific appointments when selected date or professional changes\n  useEffect(() => {\n    if (selectedProfessional !== 'current' && selectedProfessional) {\n      fetchAppointments();\n      fetchAppointmentBlocks();\n      fetchClinicSettings(); // Fetch clinic settings for the selected professional\n    }\n  }, [selectedDate, selectedProfessional]);\n\n  // Generate time slots when appointments, blocks, or clinic settings change\n  useEffect(() => {\n    if (clinicSettings && selectedDate) {\n      const slots = generateTimeSlots(\n        selectedDate,\n        clinicSettings as any,\n        appointments,\n        appointmentBlocks\n      );\n      setTimeSlots(slots);\n    }\n  }, [appointments, appointmentBlocks, clinicSettings, selectedDate]);\n\n  // Calculate available days for calendar highlighting\n  const calculateAvailableDays = React.useMemo(() => {\n    if (!clinicSettings || !selectedProfessional || selectedProfessional === 'current') {\n      return { hasAvailability: [], appointmentCounts: {} };\n    }\n\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    \n    const hasAvailability: Date[] = [];\n    const appointmentCounts: Record<string, number> = {};\n\n    // Filter appointments and blocks for the selected professional\n    const professionalAppointments = allAppointments.filter(apt => \n      apt.healthcare_professional_id === selectedProfessional\n    );\n    \n    const professionalBlocks = allAppointmentBlocks.filter(block => \n      block.healthcare_professional_id === selectedProfessional\n    );\n\n    // Calculate for next 90 days\n    for (let i = 0; i < 90; i++) {\n      const checkDate = new Date(today);\n      checkDate.setDate(checkDate.getDate() + i);\n      \n      // Skip past dates (except today)\n      if (checkDate < today) continue;\n\n      const dateStr = checkDate.toISOString().split('T')[0];\n      \n      // Count non-cancelled appointments for this date and professional\n      const dayAppointments = professionalAppointments.filter(apt => {\n        const aptDate = new Date(apt.start_time).toISOString().split('T')[0];\n        return aptDate === dateStr && apt.status !== 'cancelled';\n      });\n      \n      appointmentCounts[dateStr] = dayAppointments.length;\n\n      // Check if this day has availability based on new clinic settings format\n      const dayOfWeek = checkDate.getDay(); // 0 = Sunday, 1 = Monday, etc.\n      const dayMapping = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];\n      const dayKey = dayMapping[dayOfWeek];\n\n      let dayHasAvailability = false;\n\n      // Handle both new and legacy clinic settings formats\n      if ('weekly_schedule' in clinicSettings && clinicSettings.weekly_schedule) {\n        // New format with weekly_schedule\n        const daySchedule = (clinicSettings.weekly_schedule as any)[dayKey];\n        \n        if (daySchedule && daySchedule.enabled) {\n          // Calculate available time slots for this day\n          const slots = generateTimeSlots(\n            checkDate,\n            clinicSettings as any,\n            dayAppointments,\n            professionalBlocks.filter(block => {\n              const blockDate = new Date(block.start_time).toISOString().split('T')[0];\n              return blockDate === dateStr;\n            })\n          );\n          \n          // Check if there are any available slots\n          const availableSlots = slots.filter(slot => slot.type === 'available');\n          dayHasAvailability = availableSlots.length > 0;\n        }\n      } else {\n        // Legacy format for backward compatibility\n        const legacySettings = clinicSettings as any;\n        if (legacySettings.working_days && legacySettings.working_days.includes(dayOfWeek)) {\n          // Calculate available time slots for this day using legacy format\n          const slots = generateTimeSlots(\n            checkDate,\n            clinicSettings as any,\n            dayAppointments,\n            professionalBlocks.filter(block => {\n              const blockDate = new Date(block.start_time).toISOString().split('T')[0];\n              return blockDate === dateStr;\n            })\n          );\n          \n          // Check if there are any available slots\n          const availableSlots = slots.filter(slot => slot.type === 'available');\n          dayHasAvailability = availableSlots.length > 0;\n        }\n      }\n\n      if (dayHasAvailability) {\n        hasAvailability.push(checkDate);\n      }\n    }\n\n    debugLog.info('🗓️ Calendar availability calculation:', {\n      totalDaysChecked: 90,\n      daysWithAvailability: hasAvailability.length,\n      hasAvailabilityDates: hasAvailability.map(d => d.toISOString().split('T')[0]),\n      appointmentCounts,\n      selectedProfessional,\n      clinicSettingsType: 'weekly_schedule' in clinicSettings ? 'new' : 'legacy'\n    });\n\n    return { hasAvailability, appointmentCounts };\n  }, [clinicSettings, allAppointments, allAppointmentBlocks, selectedProfessional]);\n\n  const fetchAppointments = async () => {\n    try {\n      setLoading(true);\n      const dateStr = format(selectedDate, 'yyyy-MM-dd');\n      let url = `/api/appointments?date=${dateStr}`;\n      \n      // Add healthcare professional filter if specific one is selected\n      if (selectedProfessional && selectedProfessional !== 'current') {\n        url += `&healthcare_professional_id=${selectedProfessional}`;\n      }\n      \n      const response = await makeAuthenticatedRequest(url);\n      if (!response.ok) throw new Error('Failed to fetch appointments');\n      const result = await response.json();\n      const data = result.data || result;\n      setAppointments(Array.isArray(data) ? data : []);\n    } catch (error) {\n      debugLog.error('Error fetching appointments:', error);\n      setAppointments([]);\n      toast({\n        title: \"Erro ao carregar consultas\",\n        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n        variant: \"destructive\"\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchPatients = async () => {\n    try {\n      const response = await makeAuthenticatedRequest('/api/patients');\n      if (!response.ok) throw new Error('Failed to fetch patients');\n      const result = await response.json();\n      debugLog.info('Patients API response:', result);\n      const data = result.data || result;\n      setPatients(Array.isArray(data) ? data : []);\n    } catch (error) {\n      debugLog.error('Error fetching patients:', error);\n      setPatients([]);\n      toast({\n        title: \"Erro ao carregar pacientes\",\n        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const fetchHealthcareProfessionals = async () => {\n    try {\n      const response = await makeAuthenticatedRequest('/api/healthcare-professionals');\n      if (!response.ok) throw new Error('Failed to fetch healthcare professionals');\n      const result = await response.json();\n      const data = result.data || result;\n      \n      // For secretaries, filter only accessible healthcare professionals\n      let filteredProfessionals = Array.isArray(data) ? data : [];\n      \n      if (isSecretary && accessibleHealthcareProfessionals && accessibleHealthcareProfessionals.length > 0) {\n        filteredProfessionals = filteredProfessionals.filter(prof => \n          accessibleHealthcareProfessionals.includes(prof.id)\n        );\n        debugLog.info('🔒 Filtered healthcare professionals for secretary:', filteredProfessionals.length);\n      }\n      \n      setHealthcareProfessionals(filteredProfessionals);\n    } catch (error) {\n      debugLog.error('Error fetching healthcare professionals:', error);\n      setHealthcareProfessionals([]);\n    }\n  };\n\n  const fetchAllAppointments = async () => {\n    try {\n      const response = await makeAuthenticatedRequest('/api/appointments');\n      if (!response.ok) throw new Error('Failed to fetch all appointments');\n      const result = await response.json();\n      const data = result.data || result;\n      setAllAppointments(Array.isArray(data) ? data : []);\n    } catch (error) {\n      debugLog.error('Error fetching all appointments:', error);\n      setAllAppointments([]);\n    }\n  };\n\n  const fetchAllAppointmentBlocks = async () => {\n    try {\n      const response = await makeAuthenticatedRequest('/api/appointment-blocks');\n      if (!response.ok) {\n        if (response.status === 404) {\n          setAllAppointmentBlocks([]);\n          return;\n        }\n        throw new Error('Failed to fetch all appointment blocks');\n      }\n      const result = await response.json();\n      const data = result.data || result;\n      setAllAppointmentBlocks(Array.isArray(data) ? data : []);\n    } catch (error) {\n      debugLog.error('Error fetching all appointment blocks:', error);\n      setAllAppointmentBlocks([]);\n    }\n  };\n\n  const fetchClinicSettings = async () => {\n    try {\n      let url = '/api/clinic-settings';\n      \n      // Add healthcare professional filter if specific one is selected\n      if (selectedProfessional && selectedProfessional !== 'current') {\n        url += `?healthcare_professional_id=${selectedProfessional}`;\n      }\n      \n      const response = await makeAuthenticatedRequest(url);\n      if (!response.ok) throw new Error('Failed to fetch clinic settings');\n      const result = await response.json();\n      const data = result.data || result;\n      setClinicSettings(data);\n      \n      debugLog.info('✅ Clinic settings loaded for professional:', {\n        selectedProfessional,\n        appointmentDuration: data.appointment_duration_minutes,\n        hasWeeklySchedule: !!data.weekly_schedule\n      });\n    } catch (error) {\n      debugLog.error('Error fetching clinic settings:', error);\n      // Set default settings if fetch fails\n      setClinicSettings({\n        working_hours_start: '08:00',\n        working_hours_end: '18:00',\n        working_days: [1, 2, 3, 4, 5],\n        appointment_duration_minutes: 30,\n        allow_weekend_appointments: false\n      });\n    }\n  };\n\n  const fetchAppointmentBlocks = async () => {\n    try {\n      const dateStr = format(selectedDate, 'yyyy-MM-dd');\n      let url = `/api/appointment-blocks?date=${dateStr}`;\n      \n      // Add healthcare professional filter if specific one is selected\n      if (selectedProfessional && selectedProfessional !== 'current') {\n        url += `&healthcare_professional_id=${selectedProfessional}`;\n      }\n      \n      const response = await makeAuthenticatedRequest(url);\n      if (!response.ok) {\n        // If the API doesn't exist yet, just set empty array\n        if (response.status === 404) {\n          setAppointmentBlocks([]);\n          return;\n        }\n        throw new Error('Failed to fetch appointment blocks');\n      }\n      const result = await response.json();\n      const data = result.data || result;\n      setAppointmentBlocks(Array.isArray(data) ? data : []);\n    } catch (error) {\n      debugLog.error('Error fetching appointment blocks:', error);\n      setAppointmentBlocks([]);\n    }\n  };\n\n  const handleAppointmentCreate = (selectInfo?: DateSelectArg) => {\n    const initialData: any = {};\n\n    if (selectInfo) {\n      // FullCalendar provides dates in local timezone, use them directly\n      initialData.start_time = toLocalISOString(selectInfo.start);\n      initialData.end_time = toLocalISOString(selectInfo.end);\n    } else if (selectedDate) {\n      // Create appointment for selected date at 9:00 AM\n      const startTime = new Date(selectedDate);\n      startTime.setHours(9, 0, 0, 0);\n      const endTime = new Date(startTime);\n      endTime.setMinutes(endTime.getMinutes() + 30);\n\n      initialData.start_time = toLocalISOString(startTime);\n      initialData.end_time = toLocalISOString(endTime);\n    }\n\n    // Auto-assign to selected healthcare professional if specific one is selected\n    if (selectedProfessional && selectedProfessional !== 'current') {\n      initialData.healthcare_professional_id = selectedProfessional;\n    }\n\n    setAppointmentFormData(initialData);\n    setAppointmentFormOpen(true);\n  };\n\n  const handleSlotClick = (slot: TimeSlot) => {\n    if (slot.type === 'available') {\n      // Open appointment form with the slot time pre-filled\n      const initialData: any = {\n        start_time: toLocalISOString(slot.start_time),\n        end_time: toLocalISOString(slot.end_time),\n      };\n      \n      // Auto-assign to selected healthcare professional if specific one is selected\n      if (selectedProfessional && selectedProfessional !== 'current') {\n        initialData.healthcare_professional_id = selectedProfessional;\n      }\n      \n      setAppointmentFormData(initialData);\n      setAppointmentFormOpen(true);\n    } else if (slot.type === 'appointment' && slot.appointment) {\n      // Handle existing appointment click\n      handleAppointmentClick(slot.appointment);\n    }\n  };\n\n  const handleBlockSlot = async (slot: TimeSlot, event: React.MouseEvent) => {\n    event.stopPropagation();\n    \n    setConfirmDialog({\n      open: true,\n      title: 'Bloquear Horário',\n      description: `Deseja bloquear o horário de ${formatTimeBR(slot.start_time.toISOString())} até ${formatTimeBR(slot.end_time.toISOString())}? Este horário ficará indisponível para agendamentos.`,\n      variant: 'destructive',\n      onConfirm: () => performBlockSlot(slot)\n    });\n  };\n\n  const handleUnblockSlot = async (slot: TimeSlot, event: React.MouseEvent) => {\n    event.stopPropagation();\n    \n    if (!slot.block || !slot.block.id) {\n      toast({\n        title: \"Erro\",\n        description: \"Não foi possível identificar o bloqueio para remover.\",\n        variant: \"destructive\"\n      });\n      return;\n    }\n    \n    setConfirmDialog({\n      open: true,\n      title: 'Desbloquear Horário',\n      description: `Deseja desbloquear o horário de ${formatTimeBR(slot.start_time.toISOString())} até ${formatTimeBR(slot.end_time.toISOString())}? Este horário ficará disponível para agendamentos.`,\n      variant: 'default',\n      onConfirm: () => performUnblockSlot(slot)\n    });\n  };\n\n  const performBlockSlot = async (slot: TimeSlot) => {\n    try {\n      const blockData: any = {\n        start_time: slot.start_time.toISOString(),\n        end_time: slot.end_time.toISOString(),\n        reason: 'Horário bloqueado pelo médico'\n      };\n\n      // Add healthcare professional if one is selected\n      if (selectedProfessional && selectedProfessional !== 'current') {\n        blockData.healthcare_professional_id = selectedProfessional;\n      }\n\n      const response = await makeAuthenticatedRequest('/api/appointment-blocks', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(blockData)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => null);\n        \n        if (response.status === 409) {\n          // Conflict - slot already occupied\n          toast({\n            title: \"Horário Ocupado\",\n            description: errorData?.message || \"Este horário já está ocupado ou bloqueado.\",\n            variant: \"destructive\"\n          });\n          return;\n        }\n\n        if (response.status === 404) {\n          toast({\n            title: \"Funcionalidade em desenvolvimento\",\n            description: \"A funcionalidade de bloqueio de horários ainda está sendo implementada.\",\n            variant: \"default\"\n          });\n          return;\n        }\n\n        throw new Error(errorData?.message || 'Failed to block time slot');\n      }\n\n      const result = await response.json();\n      \n      toast({\n        title: \"Sucesso!\",\n        description: result.message || \"Horário bloqueado com sucesso.\",\n      });\n\n      // Refresh data\n      fetchAppointmentBlocks();\n      fetchAllAppointmentBlocks();\n      fetchAppointments();\n    } catch (error) {\n      debugLog.error('Error blocking time slot:', error);\n      toast({\n        title: \"Erro\",\n        description: error instanceof Error ? error.message : \"Erro ao bloquear horário.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const performUnblockSlot = async (slot: TimeSlot) => {\n    try {\n      if (!slot.block || !slot.block.id) {\n        throw new Error('ID do bloqueio não encontrado');\n      }\n\n      const response = await makeAuthenticatedRequest(`/api/appointment-blocks/${slot.block.id}`, {\n        method: 'DELETE'\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => null);\n        throw new Error(errorData?.message || 'Failed to unblock time slot');\n      }\n\n      toast({\n        title: \"Sucesso!\",\n        description: \"Horário desbloqueado com sucesso.\",\n      });\n\n      // Refresh data\n      fetchAppointmentBlocks();\n      fetchAllAppointmentBlocks();\n      fetchAppointments();\n    } catch (error) {\n      debugLog.error('Error unblocking time slot:', error);\n      toast({\n        title: \"Erro\",\n        description: error instanceof Error ? error.message : \"Erro ao desbloquear horário.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const handleAppointmentClick = (appointment: Appointment) => {\n    // For in-progress and completed appointments, navigate to medical record screen (only for healthcare professionals)\n    if ((appointment.status === 'in_progress' || appointment.status === 'completed') && canViewMedicalRecords) {\n      debugLog.info('Navigating to medical record for appointment:', appointment.id);\n      router.push(`/dashboard/prontuario/${appointment.patient_id}?appointment_id=${appointment.id}`);\n      return;\n    }\n\n    // For other statuses, open edit form with appointment data\n    debugLog.info('Appointment clicked for editing:', appointment);\n\n    const editData = {\n      id: appointment.id,\n      title: appointment.title,\n      description: appointment.description,\n      patient_id: appointment.patient_id,\n      healthcare_professional_id: appointment.healthcare_professional_id,\n      start_time: appointment.start_time,\n      end_time: appointment.end_time,\n      type: appointment.type,\n      status: appointment.status,\n      total_price: appointment.total_price,\n    };\n\n    setAppointmentFormData(editData);\n    setAppointmentFormOpen(true);\n  };\n\n  const handleAppointmentUpdate = async (appointmentId: string, newStart: Date, newEnd: Date) => {\n    try {\n      const response = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          start_time: newStart.toISOString(),\n          end_time: newEnd.toISOString(),\n        })\n      });\n\n      if (!response.ok) throw new Error('Failed to update appointment');\n\n      // Refresh both daily and all appointments for calendar views\n      fetchAppointments();\n      fetchAllAppointments();\n    } catch (error) {\n      debugLog.error('Error updating appointment:', error);\n      throw error;\n    }\n  };\n\n  const handleAppointmentSubmit = async (data: any) => {\n    try {\n      const isEditing = data.id;\n      const url = isEditing ? `/api/appointments/${data.id}` : '/api/appointments';\n      const method = isEditing ? 'PUT' : 'POST';\n\n      // Remove id from data for API call\n      const { id, ...submitData } = data;\n\n      const response = await makeAuthenticatedRequest(url, {\n        method,\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(submitData)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => null);\n        \n        if (response.status === 409) {\n          // Conflict error - show specific message\n          throw new Error(errorData?.message || 'Este horário já está ocupado. Escolha outro horário.');\n        }\n        \n        throw new Error(errorData?.message || `Failed to ${isEditing ? 'update' : 'create'} appointment`);\n      }\n\n      const result = await response.json();\n      \n      // Show success message\n      toast({\n        title: \"Sucesso!\",\n        description: result.message || `Consulta ${isEditing ? 'atualizada' : 'agendada'} com sucesso.`,\n      });\n\n      // Close the form\n      setAppointmentFormOpen(false);\n      setAppointmentFormData(null);\n\n      // Refresh both daily and all appointments for calendar views\n      fetchAppointments();\n      fetchAllAppointments();\n      fetchAppointmentBlocks(); // Also refresh blocks\n      fetchAllAppointmentBlocks();\n    } catch (error) {\n      debugLog.error(`Error ${data.id ? 'updating' : 'creating'} appointment:`, error);\n      \n      // Show error toast\n      toast({\n        title: \"Erro\",\n        description: error instanceof Error ? error.message : \"Erro ao processar consulta.\",\n        variant: \"destructive\"\n      });\n      \n      throw error;\n    }\n  };\n\n  // Calculate appointment counts for calendar indicators\n  const appointmentCounts = React.useMemo(() => {\n    const counts: Record<string, number> = {};\n    appointments.forEach(appointment => {\n      const date = new Date(appointment.start_time).toISOString().split('T')[0];\n      counts[date] = (counts[date] || 0) + 1;\n    });\n    return counts;\n  }, [appointments]);\n\n  const updateAppointmentStatus = async (appointmentId: string, status: string) => {\n    try {\n      const response = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ status })\n      });\n\n      if (!response.ok) throw new Error('Failed to update appointment status');\n\n      toast({\n        title: \"Sucesso!\",\n        description: \"Status da consulta atualizado.\",\n      });\n\n      fetchAppointments();\n      fetchAllAppointments();\n    } catch (error) {\n      debugLog.error('Error updating appointment status:', error);\n      toast({\n        title: \"Erro\",\n        description: \"Erro ao atualizar status da consulta.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const handleDeleteAppointment = (appointmentId: string) => {\n    setConfirmDialog({\n      open: true,\n      title: 'Excluir Consulta',\n      description: 'Tem certeza que deseja excluir esta consulta? Esta ação não pode ser desfeita.',\n      variant: 'destructive',\n      onConfirm: () => performDeleteAppointment(appointmentId)\n    });\n  };\n\n  const performDeleteAppointment = async (appointmentId: string) => {\n\n    try {\n      const response = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`, {\n        method: 'DELETE'\n      });\n\n      if (!response.ok) throw new Error('Failed to delete appointment');\n\n      toast({\n        title: \"Sucesso!\",\n        description: \"Consulta excluída com sucesso.\",\n      });\n\n      fetchAppointments();\n      fetchAllAppointments();\n    } catch (error) {\n      debugLog.error('Error deleting appointment:', error);\n      toast({\n        title: \"Erro\",\n        description: \"Erro ao excluir consulta.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const handleCancelAppointment = async (appointment: Appointment, event: React.MouseEvent) => {\n    event.stopPropagation(); // Prevent triggering the edit click handler\n\n    setConfirmDialog({\n      open: true,\n      title: 'Cancelar Consulta',\n      description: `Tem certeza que deseja cancelar a consulta de ${appointment.patient_name}?`,\n      variant: 'destructive',\n      onConfirm: () => performCancelAppointment(appointment.id)\n    });\n  };\n\n  const performCancelAppointment = async (appointmentId: string) => {\n    try {\n      const response = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ status: 'cancelled' })\n      });\n\n      if (!response.ok) throw new Error('Failed to cancel appointment');\n\n      toast({\n        title: \"Sucesso!\",\n        description: \"Consulta cancelada com sucesso.\",\n      });\n\n      fetchAppointments();\n      fetchAllAppointments();\n    } catch (error) {\n      debugLog.error('Error canceling appointment:', error);\n      toast({\n        title: \"Erro\",\n        description: \"Erro ao cancelar consulta.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const handleStartConsultation = async (appointment: Appointment, event: React.MouseEvent) => {\n    event.stopPropagation(); // Prevent triggering the edit click handler\n\n    setConfirmDialog({\n      open: true,\n      title: 'Iniciar Atendimento',\n      description: `Deseja iniciar o atendimento para ${appointment.patient_name}?`,\n      variant: 'default',\n      onConfirm: () => performStartConsultation(appointment)\n    });\n  };\n\n  const performStartConsultation = async (appointment: Appointment) => {\n\n    try {\n      // Update appointment status to in_progress\n      const response = await makeAuthenticatedRequest(`/api/appointments/${appointment.id}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ status: 'in_progress' })\n      });\n\n      if (!response.ok) throw new Error('Failed to start consultation');\n\n      toast({\n        title: \"Sucesso!\",\n        description: \"Atendimento iniciado com sucesso.\",\n      });\n\n      // Navigate to medical record screen\n      router.push(`/dashboard/prontuario/${appointment.patient_id}?appointment_id=${appointment.id}`);\n    } catch (error) {\n      debugLog.error('Error starting consultation:', error);\n      toast({\n        title: \"Erro\",\n        description: \"Erro ao iniciar atendimento.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-foreground\">Agenda</h1>\n          <p className=\"text-muted-foreground\">Gerencie suas consultas e horários</p>\n        </div>\n\n        <div className=\"flex items-center gap-2\">\n          <Button onClick={() => handleAppointmentCreate()}>\n            <Plus className=\"mr-2 h-4 w-4\" />\n            Nova Consulta\n          </Button>\n        </div>\n      </div>\n\n      <Tabs\n        value={currentView}\n        onValueChange={(value) => {\n          // Prevent tab switching during loading to avoid race conditions\n          if (loading) {\n            toast({\n              title: \"Aguarde\",\n              description: \"Aguarde o carregamento dos dados antes de trocar de aba.\",\n              variant: \"default\",\n            });\n            return;\n          }\n          setCurrentView(value as 'calendar' | 'fullcalendar');\n        }}\n        className=\"w-full\"\n      >\n        <TabsList className=\"grid w-full grid-cols-2\">\n          <TabsTrigger\n            value=\"calendar\"\n            className={`flex items-center gap-2 text-xs sm:text-sm ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}\n            disabled={loading}\n          >\n            <CalendarIcon className=\"h-4 w-4\" />\n            <span className=\"hidden xs:inline\">Calendário</span>\n            <span className=\"xs:hidden\">Cal.</span>\n            {loading && <div className=\"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"></div>}\n          </TabsTrigger>\n          <TabsTrigger\n            value=\"fullcalendar\"\n            className={`flex items-center gap-2 text-xs sm:text-sm ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}\n            disabled={loading}\n          >\n            <Grid3X3 className=\"h-4 w-4\" />\n            <span className=\"hidden xs:inline\">Agenda Completa</span>\n            <span className=\"xs:hidden\">Agenda</span>\n            {loading && <div className=\"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"></div>}\n          </TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"calendar\" className=\"space-y-6 mt-6\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n            <Card className=\"lg:col-span-1\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center text-lg\">\n                  <CalendarIcon className=\"mr-2 h-5 w-5 text-primary\" />\n                  Calendário\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"p-2 sm:p-6 pt-0\">\n                <div className=\"flex justify-center\">\n                  <Calendar\n                    mode=\"single\"\n                    locale={ptBR}\n                    selected={selectedDate}\n                    onSelect={(date) => date && setSelectedDate(date)}\n                    modifiers={{\n                      hasAvailability: calculateAvailableDays.hasAvailability,\n                      hasAppointments: Object.keys(calculateAvailableDays.appointmentCounts).map(dateStr => new Date(dateStr))\n                    }}\n                    modifiersClassNames={{\n                      hasAvailability: \"has-availability\",\n                      hasAppointments: \"has-appointments\"\n                    }}\n                    className=\"rounded-md border-0 shadow-none w-full\"\n                  />\n                </div>\n              </CardContent>\n            </Card>\n\n            <div className=\"lg:col-span-2\">\n              <Card>\n                <CardHeader>\n                  <div className=\"flex items-center justify-between\">\n                    <CardTitle className=\"flex items-center text-lg\">\n                      <Clock className=\"mr-2 h-5 w-5 text-primary\" />\n                      <span className=\"hidden sm:inline\">Horários - {formatDateBR(selectedDate)}</span>\n                      <span className=\"sm:hidden\">Horários</span>\n                    </CardTitle>\n                    \n                    {/* Healthcare Professional Filter */}\n                    <div className=\"flex items-center gap-2\">\n                      <Select value={selectedProfessional} onValueChange={setSelectedProfessional}>\n                        <SelectTrigger className=\"w-[180px] sm:w-[200px]\">\n                          <SelectValue placeholder=\"Selecionar médico\" />\n                        </SelectTrigger>\n                        <SelectContent>\n                          {healthcareProfessionals\n                            .filter(prof => prof.is_active)\n                            .map(professional => (\n                              <SelectItem key={professional.id} value={professional.id}>\n                                {professional.name}\n                                {professional.specialty && ` - ${professional.specialty}`}\n                              </SelectItem>\n                            ))}\n                        </SelectContent>\n                      </Select>\n                    </div>\n                  </div>\n                  <CardDescription>\n                    {timeSlots.filter(slot => slot.type === 'appointment' && slot.appointment?.status !== 'cancelled').length} consulta(s) agendada(s) • {timeSlots.filter(slot => slot.type === 'available').length} horário(s) disponível(is)\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  {loading ? (\n                    <div className=\"text-center py-8 text-muted-foreground\">\n                      <Clock className=\"mx-auto h-12 w-12 mb-4 opacity-50 animate-spin\" />\n                      <p>Carregando consultas...</p>\n                    </div>\n                  ) : timeSlots.length === 0 ? (\n                    <div className=\"text-center py-8 text-muted-foreground\">\n                      <Users className=\"mx-auto h-12 w-12 mb-4 opacity-50\" />\n                      <p>Nenhum horário disponível para este dia</p>\n                      <p className=\"text-xs mt-2\">Verifique as configurações da clínica</p>\n                    </div>\n                  ) : (\n                    <div className=\"space-y-3\">\n                      {/* Time slots grid */}\n                      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\n                        {timeSlots.map((slot) => (\n                          <TimeSlotCard\n                            key={slot.id}\n                            slot={slot}\n                            onSlotClick={handleSlotClick}\n                            onBlockSlot={handleBlockSlot}\n                            onUnblockSlot={handleUnblockSlot}\n                            onConfirmAppointment={(appointment, event) => updateAppointmentStatus(appointment.id, 'confirmed')}\n                            onStartConsultation={handleStartConsultation}\n                            onCancelAppointment={handleCancelAppointment}\n                            canBlockSlots={isAdmin || isHealthcareProfessional || isSecretary}\n                            canStartConsultation={canStartConsultation}\n                            className=\"min-h-[80px]\"\n                          />\n                        ))}\n                      </div>\n                      \n                      {/* Legend */}\n                      <div className=\"mt-4 pt-4 border-t\">\n                        <div className=\"flex flex-wrap gap-4 text-xs\">\n                          <div className=\"flex items-center gap-2\">\n                            <div className=\"w-3 h-3 border-2 border-dashed border-green-300 bg-green-50 rounded\"></div>\n                            <span>Disponível</span>\n                          </div>\n                          <div className=\"flex items-center gap-2\">\n                            <div className=\"w-3 h-3 border border-blue-300 bg-blue-50 rounded\"></div>\n                            <span>Consulta</span>\n                          </div>\n                          <div className=\"flex items-center gap-2\">\n                            <div className=\"w-3 h-3 border border-red-300 bg-red-50 rounded\"></div>\n                            <span>Bloqueado</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        </TabsContent>\n\n        <TabsContent value=\"fullcalendar\" className=\"space-y-6 mt-6\">\n          <FullCalendarView\n            appointments={allAppointments}\n            appointmentBlocks={allAppointmentBlocks}\n            healthcareProfessionals={healthcareProfessionals}\n            onAppointmentCreate={handleAppointmentCreate}\n            onAppointmentClick={handleAppointmentClick}\n            onAppointmentUpdate={handleAppointmentUpdate}\n            loading={loading}\n            selectedProfessional={selectedProfessional}\n            onSelectedProfessionalChange={setSelectedProfessional}\n            onUnblockSlot={(block) => handleUnblockSlot({ \n              id: block.id, \n              start_time: new Date(block.start_time), \n              end_time: new Date(block.end_time), \n              isAvailable: false,\n              isBlocked: true,\n              type: 'blocked',\n              block: block \n            }, {} as React.MouseEvent)}\n          />\n        </TabsContent>\n      </Tabs>\n\n      <AppointmentForm\n        open={appointmentFormOpen}\n        onOpenChange={setAppointmentFormOpen}\n        patients={patients}\n        healthcareProfessionals={healthcareProfessionals.map(prof => ({\n          ...prof,\n          specialty: prof.specialty || undefined\n        }))}\n        initialData={appointmentFormData}\n        onSubmit={handleAppointmentSubmit}\n        loading={loading}\n      />\n\n      <ConfirmDialog\n        open={confirmDialog.open}\n        onOpenChange={(open) => setConfirmDialog(prev => ({ ...prev, open }))}\n        title={confirmDialog.title}\n        description={confirmDialog.description}\n        variant={confirmDialog.variant}\n        onConfirm={confirmDialog.onConfirm}\n      />\n    </div>\n  );\n};\n\nexport default AgendaPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AAzBA;;;;;;;;;;;;;;;;;;;;;;AAmFA,MAAM,aAAa;;IACjB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACxE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IACnG,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IAC5E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAEpE,yCAAyC;IACzC,MAAM,EAAE,oBAAoB,EAAE,uBAAuB,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,0BAAuB,AAAD;IAEhF,6BAA6B;IAC7B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAM9C;QACD,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;mCAAE,KAAO;;QAClB,SAAS;IACX;IACA,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EACJ,oBAAoB,EACpB,qBAAqB,EACrB,OAAO,EACP,wBAAwB,EACxB,WAAW,EACX,iCAAiC,EACjC,QAAQ,EACT,GAAG,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IAEjB,0DAA0D;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;YACA;YACA;QACA,oFAAoF;QACtF;+BAAG,EAAE;IAEL,4DAA4D;IAC5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,UAAU;gBACZ;YACF;QACF;+BAAG;QAAC;QAAU;QAAa;KAAkC;IAE7D,mEAAmE;IACnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,wBAAwB,MAAM,GAAG,KAAK,yBAAyB,WAAW;gBAC5E,MAAM,oBAAoB,wBAAwB,IAAI;8DAAC,CAAA,OAAQ,KAAK,SAAS;;gBAC7E,IAAI,mBAAmB;oBACrB,wBAAwB,kBAAkB,EAAE;gBAC9C;YACF;QACF;+BAAG;QAAC;QAAyB;QAAsB;KAAwB;IAE3E,8EAA8E;IAC9E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,yBAAyB,aAAa,sBAAsB;gBAC9D;gBACA;gBACA,uBAAuB,sDAAsD;YAC/E;QACF;+BAAG;QAAC;QAAc;KAAqB;IAEvC,2EAA2E;IAC3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,kBAAkB,cAAc;gBAClC,MAAM,QAAQ,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAC5B,cACA,gBACA,cACA;gBAEF,aAAa;YACf;QACF;+BAAG;QAAC;QAAc;QAAmB;QAAgB;KAAa;IAElE,qDAAqD;IACrD,MAAM,yBAAyB,6JAAA,CAAA,UAAK,CAAC,OAAO;sDAAC;YAC3C,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,yBAAyB,WAAW;gBAClF,OAAO;oBAAE,iBAAiB,EAAE;oBAAE,mBAAmB,CAAC;gBAAE;YACtD;YAEA,MAAM,QAAQ,IAAI;YAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;YAExB,MAAM,kBAA0B,EAAE;YAClC,MAAM,oBAA4C,CAAC;YAEnD,+DAA+D;YAC/D,MAAM,2BAA2B,gBAAgB,MAAM;uFAAC,CAAA,MACtD,IAAI,0BAA0B,KAAK;;YAGrC,MAAM,qBAAqB,qBAAqB,MAAM;iFAAC,CAAA,QACrD,MAAM,0BAA0B,KAAK;;YAGvC,6BAA6B;YAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;gBAC3B,MAAM,YAAY,IAAI,KAAK;gBAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;gBAExC,iCAAiC;gBACjC,IAAI,YAAY,OAAO;gBAEvB,MAAM,UAAU,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAErD,kEAAkE;gBAClE,MAAM,kBAAkB,yBAAyB,MAAM;kFAAC,CAAA;wBACtD,MAAM,UAAU,IAAI,KAAK,IAAI,UAAU,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;wBACpE,OAAO,YAAY,WAAW,IAAI,MAAM,KAAK;oBAC/C;;gBAEA,iBAAiB,CAAC,QAAQ,GAAG,gBAAgB,MAAM;gBAEnD,yEAAyE;gBACzE,MAAM,YAAY,UAAU,MAAM,IAAI,+BAA+B;gBACrE,MAAM,aAAa;oBAAC;oBAAU;oBAAU;oBAAW;oBAAa;oBAAY;oBAAU;iBAAW;gBACjG,MAAM,SAAS,UAAU,CAAC,UAAU;gBAEpC,IAAI,qBAAqB;gBAEzB,qDAAqD;gBACrD,IAAI,qBAAqB,kBAAkB,eAAe,eAAe,EAAE;oBACzE,kCAAkC;oBAClC,MAAM,cAAc,AAAC,eAAe,eAAe,AAAQ,CAAC,OAAO;oBAEnE,IAAI,eAAe,YAAY,OAAO,EAAE;wBACtC,8CAA8C;wBAC9C,MAAM,QAAQ,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAC5B,WACA,gBACA,iBACA,mBAAmB,MAAM;gFAAC,CAAA;gCACxB,MAAM,YAAY,IAAI,KAAK,MAAM,UAAU,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gCACxE,OAAO,cAAc;4BACvB;;wBAGF,yCAAyC;wBACzC,MAAM,iBAAiB,MAAM,MAAM;yFAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;;wBAC1D,qBAAqB,eAAe,MAAM,GAAG;oBAC/C;gBACF,OAAO;oBACL,2CAA2C;oBAC3C,MAAM,iBAAiB;oBACvB,IAAI,eAAe,YAAY,IAAI,eAAe,YAAY,CAAC,QAAQ,CAAC,YAAY;wBAClF,kEAAkE;wBAClE,MAAM,QAAQ,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAC5B,WACA,gBACA,iBACA,mBAAmB,MAAM;gFAAC,CAAA;gCACxB,MAAM,YAAY,IAAI,KAAK,MAAM,UAAU,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gCACxE,OAAO,cAAc;4BACvB;;wBAGF,yCAAyC;wBACzC,MAAM,iBAAiB,MAAM,MAAM;yFAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;;wBAC1D,qBAAqB,eAAe,MAAM,GAAG;oBAC/C;gBACF;gBAEA,IAAI,oBAAoB;oBACtB,gBAAgB,IAAI,CAAC;gBACvB;YACF;YAEA,+HAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,0CAA0C;gBACtD,kBAAkB;gBAClB,sBAAsB,gBAAgB,MAAM;gBAC5C,sBAAsB,gBAAgB,GAAG;kEAAC,CAAA,IAAK,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;;gBAC5E;gBACA;gBACA,oBAAoB,qBAAqB,iBAAiB,QAAQ;YACpE;YAEA,OAAO;gBAAE;gBAAiB;YAAkB;QAC9C;qDAAG;QAAC;QAAgB;QAAiB;QAAsB;KAAqB;IAEhF,MAAM,oBAAoB;QACxB,IAAI;YACF,WAAW;YACX,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;YACrC,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS;YAE7C,iEAAiE;YACjE,IAAI,wBAAwB,yBAAyB,WAAW;gBAC9D,OAAO,CAAC,4BAA4B,EAAE,sBAAsB;YAC9D;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,OAAO,OAAO,IAAI,IAAI;YAC5B,gBAAgB,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;QACjD,EAAE,OAAO,OAAO;YACd,+HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,gCAAgC;YAC/C,gBAAgB,EAAE;YAClB,MAAM;gBACJ,OAAO;gBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACtD,SAAS;YACX;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,+HAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,0BAA0B;YACxC,MAAM,OAAO,OAAO,IAAI,IAAI;YAC5B,YAAY,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;QAC7C,EAAE,OAAO,OAAO;YACd,+HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,4BAA4B;YAC3C,YAAY,EAAE;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACtD,SAAS;YACX;QACF;IACF;IAEA,MAAM,+BAA+B;QACnC,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,OAAO,OAAO,IAAI,IAAI;YAE5B,mEAAmE;YACnE,IAAI,wBAAwB,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;YAE3D,IAAI,eAAe,qCAAqC,kCAAkC,MAAM,GAAG,GAAG;gBACpG,wBAAwB,sBAAsB,MAAM,CAAC,CAAA,OACnD,kCAAkC,QAAQ,CAAC,KAAK,EAAE;gBAEpD,+HAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,uDAAuD,sBAAsB,MAAM;YACnG;YAEA,2BAA2B;QAC7B,EAAE,OAAO,OAAO;YACd,+HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,4CAA4C;YAC3D,2BAA2B,EAAE;QAC/B;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,OAAO,OAAO,IAAI,IAAI;YAC5B,mBAAmB,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;QACpD,EAAE,OAAO,OAAO;YACd,+HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,oCAAoC;YACnD,mBAAmB,EAAE;QACvB;IACF;IAEA,MAAM,4BAA4B;QAChC,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC3B,wBAAwB,EAAE;oBAC1B;gBACF;gBACA,MAAM,IAAI,MAAM;YAClB;YACA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,OAAO,OAAO,IAAI,IAAI;YAC5B,wBAAwB,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;QACzD,EAAE,OAAO,OAAO;YACd,+HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,0CAA0C;YACzD,wBAAwB,EAAE;QAC5B;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,IAAI,MAAM;YAEV,iEAAiE;YACjE,IAAI,wBAAwB,yBAAyB,WAAW;gBAC9D,OAAO,CAAC,4BAA4B,EAAE,sBAAsB;YAC9D;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,OAAO,OAAO,IAAI,IAAI;YAC5B,kBAAkB;YAElB,+HAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,8CAA8C;gBAC1D;gBACA,qBAAqB,KAAK,4BAA4B;gBACtD,mBAAmB,CAAC,CAAC,KAAK,eAAe;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,+HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,mCAAmC;YAClD,sCAAsC;YACtC,kBAAkB;gBAChB,qBAAqB;gBACrB,mBAAmB;gBACnB,cAAc;oBAAC;oBAAG;oBAAG;oBAAG;oBAAG;iBAAE;gBAC7B,8BAA8B;gBAC9B,4BAA4B;YAC9B;QACF;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI;YACF,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;YACrC,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS;YAEnD,iEAAiE;YACjE,IAAI,wBAAwB,yBAAyB,WAAW;gBAC9D,OAAO,CAAC,4BAA4B,EAAE,sBAAsB;YAC9D;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,qDAAqD;gBACrD,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC3B,qBAAqB,EAAE;oBACvB;gBACF;gBACA,MAAM,IAAI,MAAM;YAClB;YACA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,OAAO,OAAO,IAAI,IAAI;YAC5B,qBAAqB,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;QACtD,EAAE,OAAO,OAAO;YACd,+HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,sCAAsC;YACrD,qBAAqB,EAAE;QACzB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,MAAM,cAAmB,CAAC;QAE1B,IAAI,YAAY;YACd,mEAAmE;YACnE,YAAY,UAAU,GAAG,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,KAAK;YAC1D,YAAY,QAAQ,GAAG,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,GAAG;QACxD,OAAO,IAAI,cAAc;YACvB,kDAAkD;YAClD,MAAM,YAAY,IAAI,KAAK;YAC3B,UAAU,QAAQ,CAAC,GAAG,GAAG,GAAG;YAC5B,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,UAAU,CAAC,QAAQ,UAAU,KAAK;YAE1C,YAAY,UAAU,GAAG,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;YAC1C,YAAY,QAAQ,GAAG,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;QAC1C;QAEA,8EAA8E;QAC9E,IAAI,wBAAwB,yBAAyB,WAAW;YAC9D,YAAY,0BAA0B,GAAG;QAC3C;QAEA,uBAAuB;QACvB,uBAAuB;IACzB;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,sDAAsD;YACtD,MAAM,cAAmB;gBACvB,YAAY,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,UAAU;gBAC5C,UAAU,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,QAAQ;YAC1C;YAEA,8EAA8E;YAC9E,IAAI,wBAAwB,yBAAyB,WAAW;gBAC9D,YAAY,0BAA0B,GAAG;YAC3C;YAEA,uBAAuB;YACvB,uBAAuB;QACzB,OAAO,IAAI,KAAK,IAAI,KAAK,iBAAiB,KAAK,WAAW,EAAE;YAC1D,oCAAoC;YACpC,uBAAuB,KAAK,WAAW;QACzC;IACF;IAEA,MAAM,kBAAkB,OAAO,MAAgB;QAC7C,MAAM,eAAe;QAErB,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,aAAa,CAAC,6BAA6B,EAAE,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU,CAAC,WAAW,IAAI,KAAK,EAAE,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,KAAK,QAAQ,CAAC,WAAW,IAAI,qDAAqD,CAAC;YAChM,SAAS;YACT,WAAW,IAAM,iBAAiB;QACpC;IACF;IAEA,MAAM,oBAAoB,OAAO,MAAgB;QAC/C,MAAM,eAAe;QAErB,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE,EAAE;YACjC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,aAAa,CAAC,gCAAgC,EAAE,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU,CAAC,WAAW,IAAI,KAAK,EAAE,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,KAAK,QAAQ,CAAC,WAAW,IAAI,mDAAmD,CAAC;YACjM,SAAS;YACT,WAAW,IAAM,mBAAmB;QACtC;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,YAAiB;gBACrB,YAAY,KAAK,UAAU,CAAC,WAAW;gBACvC,UAAU,KAAK,QAAQ,CAAC,WAAW;gBACnC,QAAQ;YACV;YAEA,iDAAiD;YACjD,IAAI,wBAAwB,yBAAyB,WAAW;gBAC9D,UAAU,0BAA0B,GAAG;YACzC;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,2BAA2B;gBACzE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM;gBAEpD,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC3B,mCAAmC;oBACnC,MAAM;wBACJ,OAAO;wBACP,aAAa,WAAW,WAAW;wBACnC,SAAS;oBACX;oBACA;gBACF;gBAEA,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC3B,MAAM;wBACJ,OAAO;wBACP,aAAa;wBACb,SAAS;oBACX;oBACA;gBACF;gBAEA,MAAM,IAAI,MAAM,WAAW,WAAW;YACxC;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,MAAM;gBACJ,OAAO;gBACP,aAAa,OAAO,OAAO,IAAI;YACjC;YAEA,eAAe;YACf;YACA;YACA;QACF,EAAE,OAAO,OAAO;YACd,+HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,6BAA6B;YAC5C,MAAM;gBACJ,OAAO;gBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACtD,SAAS;YACX;QACF;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE,EAAE;gBACjC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,wBAAwB,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE,EAAE;gBAC1F,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM;gBACpD,MAAM,IAAI,MAAM,WAAW,WAAW;YACxC;YAEA,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,eAAe;YACf;YACA;YACA;QACF,EAAE,OAAO,OAAO;YACd,+HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,+BAA+B;YAC9C,MAAM;gBACJ,OAAO;gBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACtD,SAAS;YACX;QACF;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,oHAAoH;QACpH,IAAI,CAAC,YAAY,MAAM,KAAK,iBAAiB,YAAY,MAAM,KAAK,WAAW,KAAK,uBAAuB;YACzG,+HAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iDAAiD,YAAY,EAAE;YAC7E,OAAO,IAAI,CAAC,CAAC,sBAAsB,EAAE,YAAY,UAAU,CAAC,gBAAgB,EAAE,YAAY,EAAE,EAAE;YAC9F;QACF;QAEA,2DAA2D;QAC3D,+HAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,oCAAoC;QAElD,MAAM,WAAW;YACf,IAAI,YAAY,EAAE;YAClB,OAAO,YAAY,KAAK;YACxB,aAAa,YAAY,WAAW;YACpC,YAAY,YAAY,UAAU;YAClC,4BAA4B,YAAY,0BAA0B;YAClE,YAAY,YAAY,UAAU;YAClC,UAAU,YAAY,QAAQ;YAC9B,MAAM,YAAY,IAAI;YACtB,QAAQ,YAAY,MAAM;YAC1B,aAAa,YAAY,WAAW;QACtC;QAEA,uBAAuB;QACvB,uBAAuB;IACzB;IAEA,MAAM,0BAA0B,OAAO,eAAuB,UAAgB;QAC5E,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,kBAAkB,EAAE,eAAe,EAAE;gBACpF,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY,SAAS,WAAW;oBAChC,UAAU,OAAO,WAAW;gBAC9B;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,6DAA6D;YAC7D;YACA;QACF,EAAE,OAAO,OAAO;YACd,+HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,+BAA+B;YAC9C,MAAM;QACR;IACF;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,MAAM,YAAY,KAAK,EAAE;YACzB,MAAM,MAAM,YAAY,CAAC,kBAAkB,EAAE,KAAK,EAAE,EAAE,GAAG;YACzD,MAAM,SAAS,YAAY,QAAQ;YAEnC,mCAAmC;YACnC,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,GAAG;YAE9B,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,KAAK;gBACnD;gBACA,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM;gBAEpD,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC3B,yCAAyC;oBACzC,MAAM,IAAI,MAAM,WAAW,WAAW;gBACxC;gBAEA,MAAM,IAAI,MAAM,WAAW,WAAW,CAAC,UAAU,EAAE,YAAY,WAAW,SAAS,YAAY,CAAC;YAClG;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,uBAAuB;YACvB,MAAM;gBACJ,OAAO;gBACP,aAAa,OAAO,OAAO,IAAI,CAAC,SAAS,EAAE,YAAY,eAAe,WAAW,aAAa,CAAC;YACjG;YAEA,iBAAiB;YACjB,uBAAuB;YACvB,uBAAuB;YAEvB,6DAA6D;YAC7D;YACA;YACA,0BAA0B,sBAAsB;YAChD;QACF,EAAE,OAAO,OAAO;YACd,+HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,aAAa,WAAW,aAAa,CAAC,EAAE;YAE1E,mBAAmB;YACnB,MAAM;gBACJ,OAAO;gBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACtD,SAAS;YACX;YAEA,MAAM;QACR;IACF;IAEA,uDAAuD;IACvD,MAAM,oBAAoB,6JAAA,CAAA,UAAK,CAAC,OAAO;iDAAC;YACtC,MAAM,SAAiC,CAAC;YACxC,aAAa,OAAO;yDAAC,CAAA;oBACnB,MAAM,OAAO,IAAI,KAAK,YAAY,UAAU,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBACzE,MAAM,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI;gBACvC;;YACA,OAAO;QACT;gDAAG;QAAC;KAAa;IAEjB,MAAM,0BAA0B,OAAO,eAAuB;QAC5D,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,kBAAkB,EAAE,eAAe,EAAE;gBACpF,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA;YACA;QACF,EAAE,OAAO,OAAO;YACd,+HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,sCAAsC;YACrD,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;YACT,WAAW,IAAM,yBAAyB;QAC5C;IACF;IAEA,MAAM,2BAA2B,OAAO;QAEtC,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,kBAAkB,EAAE,eAAe,EAAE;gBACpF,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA;YACA;QACF,EAAE,OAAO,OAAO;YACd,+HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,+BAA+B;YAC9C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,0BAA0B,OAAO,aAA0B;QAC/D,MAAM,eAAe,IAAI,4CAA4C;QAErE,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,aAAa,CAAC,8CAA8C,EAAE,YAAY,YAAY,CAAC,CAAC,CAAC;YACzF,SAAS;YACT,WAAW,IAAM,yBAAyB,YAAY,EAAE;QAC1D;IACF;IAEA,MAAM,2BAA2B,OAAO;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,kBAAkB,EAAE,eAAe,EAAE;gBACpF,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,QAAQ;gBAAY;YAC7C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA;YACA;QACF,EAAE,OAAO,OAAO;YACd,+HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,gCAAgC;YAC/C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,0BAA0B,OAAO,aAA0B;QAC/D,MAAM,eAAe,IAAI,4CAA4C;QAErE,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,aAAa,CAAC,kCAAkC,EAAE,YAAY,YAAY,CAAC,CAAC,CAAC;YAC7E,SAAS;YACT,WAAW,IAAM,yBAAyB;QAC5C;IACF;IAEA,MAAM,2BAA2B,OAAO;QAEtC,IAAI;YACF,2CAA2C;YAC3C,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,kBAAkB,EAAE,YAAY,EAAE,EAAE,EAAE;gBACrF,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,QAAQ;gBAAc;YAC/C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,oCAAoC;YACpC,OAAO,IAAI,CAAC,CAAC,sBAAsB,EAAE,YAAY,UAAU,CAAC,gBAAgB,EAAE,YAAY,EAAE,EAAE;QAChG,EAAE,OAAO,OAAO;YACd,+HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,gCAAgC;YAC/C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAGvC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM;;8CACrB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAMvC,6LAAC,mIAAA,CAAA,OAAI;gBACH,OAAO;gBACP,eAAe,CAAC;oBACd,gEAAgE;oBAChE,IAAI,SAAS;wBACX,MAAM;4BACJ,OAAO;4BACP,aAAa;4BACb,SAAS;wBACX;wBACA;oBACF;oBACA,eAAe;gBACjB;gBACA,WAAU;;kCAEV,6LAAC,mIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,mIAAA,CAAA,cAAW;gCACV,OAAM;gCACN,WAAW,CAAC,2CAA2C,EAAE,UAAU,kCAAkC,IAAI;gCACzG,UAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDAAY;;;;;;oCAC3B,yBAAW,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAE7B,6LAAC,mIAAA,CAAA,cAAW;gCACV,OAAM;gCACN,WAAW,CAAC,2CAA2C,EAAE,UAAU,kCAAkC,IAAI;gCACzG,UAAU;;kDAEV,6LAAC,+MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDAAY;;;;;;oCAC3B,yBAAW,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;kCAI/B,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;kCACtC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,6MAAA,CAAA,WAAY;wDAAC,WAAU;;;;;;oDAA8B;;;;;;;;;;;;sDAI1D,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;oDACP,MAAK;oDACL,QAAQ,oJAAA,CAAA,OAAI;oDACZ,UAAU;oDACV,UAAU,CAAC,OAAS,QAAQ,gBAAgB;oDAC5C,WAAW;wDACT,iBAAiB,uBAAuB,eAAe;wDACvD,iBAAiB,OAAO,IAAI,CAAC,uBAAuB,iBAAiB,EAAE,GAAG,CAAC,CAAA,UAAW,IAAI,KAAK;oDACjG;oDACA,qBAAqB;wDACnB,iBAAiB;wDACjB,iBAAiB;oDACnB;oDACA,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAMlB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;;kEACT,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;;kFACnB,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,6LAAC;wEAAK,WAAU;;4EAAmB;4EAAY,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE;;;;;;;kFAC5D,6LAAC;wEAAK,WAAU;kFAAY;;;;;;;;;;;;0EAI9B,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAsB,eAAe;;sFAClD,6LAAC,qIAAA,CAAA,gBAAa;4EAAC,WAAU;sFACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;sFAE3B,6LAAC,qIAAA,CAAA,gBAAa;sFACX,wBACE,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAC7B,GAAG,CAAC,CAAA,6BACH,6LAAC,qIAAA,CAAA,aAAU;oFAAuB,OAAO,aAAa,EAAE;;wFACrD,aAAa,IAAI;wFACjB,aAAa,SAAS,IAAI,CAAC,GAAG,EAAE,aAAa,SAAS,EAAE;;mFAF1C,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAS5C,6LAAC,mIAAA,CAAA,kBAAe;;4DACb,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,iBAAiB,KAAK,WAAW,EAAE,WAAW,aAAa,MAAM;4DAAC;4DAA4B,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,aAAa,MAAM;4DAAC;;;;;;;;;;;;;0DAGrM,6LAAC,mIAAA,CAAA,cAAW;0DACT,wBACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;sEAAE;;;;;;;;;;;2DAEH,UAAU,MAAM,KAAK,kBACvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;sEAAE;;;;;;sEACH,6LAAC;4DAAE,WAAU;sEAAe;;;;;;;;;;;yEAG9B,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;sEACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,qIAAA,CAAA,UAAY;oEAEX,MAAM;oEACN,aAAa;oEACb,aAAa;oEACb,eAAe;oEACf,sBAAsB,CAAC,aAAa,QAAU,wBAAwB,YAAY,EAAE,EAAE;oEACtF,qBAAqB;oEACrB,qBAAqB;oEACrB,eAAe,WAAW,4BAA4B;oEACtD,sBAAsB;oEACtB,WAAU;mEAVL,KAAK,EAAE;;;;;;;;;;sEAgBlB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;;;;;0FACf,6LAAC;0FAAK;;;;;;;;;;;;kFAER,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;;;;;0FACf,6LAAC;0FAAK;;;;;;;;;;;;kFAER,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;;;;;0FACf,6LAAC;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAY1B,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAe,WAAU;kCAC1C,cAAA,6LAAC,yIAAA,CAAA,UAAgB;4BACf,cAAc;4BACd,mBAAmB;4BACnB,yBAAyB;4BACzB,qBAAqB;4BACrB,oBAAoB;4BACpB,qBAAqB;4BACrB,SAAS;4BACT,sBAAsB;4BACtB,8BAA8B;4BAC9B,eAAe,CAAC,QAAU,kBAAkB;oCAC1C,IAAI,MAAM,EAAE;oCACZ,YAAY,IAAI,KAAK,MAAM,UAAU;oCACrC,UAAU,IAAI,KAAK,MAAM,QAAQ;oCACjC,aAAa;oCACb,WAAW;oCACX,MAAM;oCACN,OAAO;gCACT,GAAG,CAAC;;;;;;;;;;;;;;;;;0BAKV,6LAAC,wIAAA,CAAA,UAAe;gBACd,MAAM;gBACN,cAAc;gBACd,UAAU;gBACV,yBAAyB,wBAAwB,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAC5D,GAAG,IAAI;wBACP,WAAW,KAAK,SAAS,IAAI;oBAC/B,CAAC;gBACD,aAAa;gBACb,UAAU;gBACV,SAAS;;;;;;0BAGX,6LAAC,sIAAA,CAAA,UAAa;gBACZ,MAAM,cAAc,IAAI;gBACxB,cAAc,CAAC,OAAS,iBAAiB,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE;wBAAK,CAAC;gBACnE,OAAO,cAAc,KAAK;gBAC1B,aAAa,cAAc,WAAW;gBACtC,SAAS,cAAc,OAAO;gBAC9B,WAAW,cAAc,SAAS;;;;;;;;;;;;AAI1C;GAhgCM;;QAgBsD,kJAAA,CAAA,0BAAuB;QAgB/D,+HAAA,CAAA,WAAQ;QACX,qIAAA,CAAA,YAAS;QASpB,kIAAA,CAAA,iBAAc;;;KA1Cd;uCAkgCS", "debugId": null}}]}