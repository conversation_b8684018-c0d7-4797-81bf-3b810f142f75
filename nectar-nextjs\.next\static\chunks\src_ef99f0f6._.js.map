{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/tabs.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface TextareaProps\r\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\r\n  ({ className, ...props }, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nTextarea.displayName = \"Textarea\"\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/lib/date-utils.ts"], "sourcesContent": ["import { format, parseISO, isValid } from 'date-fns';\nimport { ptBR } from 'date-fns/locale';\n\n/**\n * Utility functions for Brazilian date and time formatting\n */\n\n/**\n * Format date to Brazilian format (DD/MM/YYYY)\n */\nexport function formatDateBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'dd/MM/yyyy', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format time to Brazilian format (HH:mm)\n */\nexport function formatTimeBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'HH:mm', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format date and time to Brazilian format (DD/MM/YYYY HH:mm)\n */\nexport function formatDateTimeBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'dd/MM/yyyy HH:mm', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format date to Brazilian format with day of week (Segunda, DD/MM/YYYY)\n */\nexport function formatDateWithDayBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'EEEE, dd/MM/yyyy', { locale: ptBR });\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format date for input fields (YYYY-MM-DD)\n */\nexport function formatDateForInput(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    return format(dateObj, 'yyyy-MM-dd');\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Format datetime for input fields (YYYY-MM-DDTHH:mm)\n * This function handles Brazilian timezone properly to avoid UTC conversion issues\n */\nexport function formatDateTimeForInput(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n\n    // Create a new date in local timezone to avoid UTC conversion\n    const localDate = new Date(dateObj.getTime() - (dateObj.getTimezoneOffset() * 60000));\n    return format(localDate, \"yyyy-MM-dd'T'HH:mm\");\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Convert local datetime to ISO string without timezone conversion\n * This prevents the 3-hour offset issue in Brazilian timezone\n */\nexport function toLocalISOString(date: Date): string {\n  try {\n    if (!isValid(date)) return '';\n\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    const hours = String(date.getHours()).padStart(2, '0');\n    const minutes = String(date.getMinutes()).padStart(2, '0');\n\n    return `${year}-${month}-${day}T${hours}:${minutes}`;\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Get relative time in Portuguese (hoje, ontem, amanhã, etc.)\n */\nexport function getRelativeTimeBR(date: Date | string): string {\n  try {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    if (!isValid(dateObj)) return '';\n    \n    const today = new Date();\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    \n    const dateStr = format(dateObj, 'yyyy-MM-dd');\n    const todayStr = format(today, 'yyyy-MM-dd');\n    const tomorrowStr = format(tomorrow, 'yyyy-MM-dd');\n    const yesterdayStr = format(yesterday, 'yyyy-MM-dd');\n    \n    if (dateStr === todayStr) return 'Hoje';\n    if (dateStr === tomorrowStr) return 'Amanhã';\n    if (dateStr === yesterdayStr) return 'Ontem';\n    \n    return formatDateBR(dateObj);\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Parse Brazilian date format (DD/MM/YYYY) to Date object\n */\nexport function parseBRDate(dateStr: string): Date | null {\n  try {\n    const parts = dateStr.split('/');\n    if (parts.length !== 3) return null;\n    \n    const day = parseInt(parts[0], 10);\n    const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed\n    const year = parseInt(parts[2], 10);\n    \n    const date = new Date(year, month, day);\n    if (!isValid(date)) return null;\n    \n    return date;\n  } catch {\n    return null;\n  }\n}\n\n/**\n * Get appointment status in Portuguese\n */\nexport function getAppointmentStatusBR(status: string): string {\n  const statusMap: Record<string, string> = {\n    'scheduled': 'Agendado',\n    'confirmed': 'Confirmado',\n    'in_progress': 'Em Andamento',\n    'completed': 'Concluído',\n    'cancelled': 'Cancelado',\n    'no_show': 'Faltou'\n  };\n  \n  return statusMap[status] || status;\n}\n\n/**\n * Get appointment type in Portuguese\n */\nexport function getAppointmentTypeBR(type: string): string {\n  const typeMap: Record<string, string> = {\n    'consultation': 'Consulta',\n    'return': 'Retorno',\n    'teleconsultation': 'Teleconsulta',\n    'procedure': 'Procedimento',\n    'exam': 'Exame'\n  };\n  \n  return typeMap[type] || type;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;;;AASO,SAAS,aAAa,IAAmB;IAC9C,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,cAAc;YAAE,QAAQ,oJAAA,CAAA,OAAI;QAAC;IACtD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,aAAa,IAAmB;IAC9C,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,SAAS;YAAE,QAAQ,oJAAA,CAAA,OAAI;QAAC;IACjD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,iBAAiB,IAAmB;IAClD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;YAAE,QAAQ,oJAAA,CAAA,OAAI;QAAC;IAC5D,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,oBAAoB,IAAmB;IACrD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,oBAAoB;YAAE,QAAQ,oJAAA,CAAA,OAAI;QAAC;IAC5D,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,mBAAmB,IAAmB;IACpD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;IACzB,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAMO,SAAS,uBAAuB,IAAmB;IACxD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAE9B,8DAA8D;QAC9D,MAAM,YAAY,IAAI,KAAK,QAAQ,OAAO,KAAM,QAAQ,iBAAiB,KAAK;QAC9E,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;IAC3B,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAMO,SAAS,iBAAiB,IAAU;IACzC,IAAI;QACF,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;QAE3B,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,QAAQ,OAAO,KAAK,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;QACtD,MAAM,MAAM,OAAO,KAAK,OAAO,IAAI,QAAQ,CAAC,GAAG;QAC/C,MAAM,QAAQ,OAAO,KAAK,QAAQ,IAAI,QAAQ,CAAC,GAAG;QAClD,MAAM,UAAU,OAAO,KAAK,UAAU,IAAI,QAAQ,CAAC,GAAG;QAEtD,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS;IACtD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,kBAAkB,IAAmB;IACnD,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QAC5D,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO;QAE9B,MAAM,QAAQ,IAAI;QAClB,MAAM,WAAW,IAAI,KAAK;QAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;QACtC,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;QAChC,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,OAAO;QAC/B,MAAM,cAAc,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;QACrC,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;QAEvC,IAAI,YAAY,UAAU,OAAO;QACjC,IAAI,YAAY,aAAa,OAAO;QACpC,IAAI,YAAY,cAAc,OAAO;QAErC,OAAO,aAAa;IACtB,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,YAAY,OAAe;IACzC,IAAI;QACF,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAC5B,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAE/B,MAAM,MAAM,SAAS,KAAK,CAAC,EAAE,EAAE;QAC/B,MAAM,QAAQ,SAAS,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,qBAAqB;QAC/D,MAAM,OAAO,SAAS,KAAK,CAAC,EAAE,EAAE;QAEhC,MAAM,OAAO,IAAI,KAAK,MAAM,OAAO;QACnC,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;QAE3B,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,uBAAuB,MAAc;IACnD,MAAM,YAAoC;QACxC,aAAa;QACb,aAAa;QACb,eAAe;QACf,aAAa;QACb,aAAa;QACb,WAAW;IACb;IAEA,OAAO,SAAS,CAAC,OAAO,IAAI;AAC9B;AAKO,SAAS,qBAAqB,IAAY;IAC/C,MAAM,UAAkC;QACtC,gBAAgB;QAChB,UAAU;QACV,oBAAoB;QACpB,aAAa;QACb,QAAQ;IACV;IAEA,OAAO,OAAO,CAAC,KAAK,IAAI;AAC1B", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/next-js/nectar/nectar-nextjs/src/app/dashboard/pacientes/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState, useEffect } from 'react';\nimport { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport {\n  User,\n  Calendar,\n  FileText,\n  Edit,\n  Save,\n  X,\n  Upload,\n  Download,\n  Trash2,\n  Phone,\n  Mail,\n  MapPin,\n  Clock,\n  Plus,\n  Stethoscope,\n  ArrowLeft\n} from 'lucide-react';\nimport { useToast } from '@/hooks/use-toast';\nimport { format } from 'date-fns';\nimport { ptBR } from 'date-fns/locale';\nimport { formatDateBR, formatTimeBR, formatDateTimeBR, getAppointmentStatusBR, getAppointmentTypeBR } from '@/lib/date-utils';\nimport { makeAuthenticatedRequest } from '@/lib/api-client';\nimport { ConsultationHistorySkeleton, FileAttachmentsSkeleton } from '@/components/ui/skeleton-components';\nimport { usePermissions } from '@/hooks/usePermissions';\n\ntype Patient = {\n  id: string;\n  name: string;\n  email: string | null;\n  phone: string | null;\n  birth_date: string | null;\n  cpf: string | null;\n  address: string | null;\n  notes: string | null;\n  created_at: string;\n  updated_at: string;\n};\n\ntype Appointment = {\n  id: string;\n  title: string;\n  description: string | null;\n  start_time: string;\n  end_time: string;\n  type: string;\n  status: string;\n  price: number | null;\n  healthcare_professional_name?: string;\n};\n\ntype PatientAttachment = {\n  id: string;\n  file_name: string;\n  file_path: string;\n  file_size: number | null;\n  mime_type: string | null;\n  created_at: string;\n};\n\nconst PatientDetailPage = () => {\n  const params = useParams();\n  const router = useRouter();\n  const { toast } = useToast();\n  const patientId = params.id as string;\n  const { canViewMedicalRecords, loading: permissionsLoading } = usePermissions();\n\n  const [patient, setPatient] = useState<Patient | null>(null);\n  const [appointments, setAppointments] = useState<Appointment[]>([]);\n  const [attachments, setAttachments] = useState<PatientAttachment[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [editMode, setEditMode] = useState(false);\n  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [medicalRecordOpen, setMedicalRecordOpen] = useState(false);\n  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);\n  const [medicalRecords, setMedicalRecords] = useState<any[]>([]);\n  const [newRecord, setNewRecord] = useState('');\n\n  const [editForm, setEditForm] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    birth_date: '',\n    cpf: '',\n    address: '',\n    notes: ''\n  });\n\n  useEffect(() => {\n    if (patientId) {\n      fetchPatientData();\n      fetchAppointments();\n      fetchAttachments();\n    }\n  }, [patientId]);\n\n  const fetchPatientData = async () => {\n    try {\n      const response = await makeAuthenticatedRequest(`/api/patients/${patientId}`);\n      if (!response.ok) throw new Error('Failed to fetch patient');\n      const result = await response.json();\n      const data = result.data || result;\n      setPatient(data);\n      setEditForm({\n        name: data.name || '',\n        email: data.email || '',\n        phone: data.phone || '',\n        birth_date: data.birth_date || '',\n        cpf: data.cpf || '',\n        address: data.address || '',\n        notes: data.notes || ''\n      });\n    } catch (error) {\n      console.error('Error fetching patient:', error);\n      toast({\n        title: \"Erro\",\n        description: \"Erro ao carregar dados do paciente.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const fetchAppointments = async () => {\n    try {\n      const response = await makeAuthenticatedRequest(`/api/appointments?patient_id=${patientId}`);\n      if (!response.ok) throw new Error('Failed to fetch appointments');\n      const result = await response.json();\n      const data = result.data || result;\n      setAppointments(Array.isArray(data) ? data : []);\n    } catch (error) {\n      console.error('Error fetching appointments:', error);\n      setAppointments([]);\n    }\n  };\n\n  const fetchAttachments = async () => {\n    try {\n      const response = await makeAuthenticatedRequest(`/api/patient-attachments?patient_id=${patientId}`);\n      if (!response.ok) throw new Error('Failed to fetch attachments');\n      const result = await response.json();\n      const data = result.data || result;\n      setAttachments(Array.isArray(data) ? data : []);\n    } catch (error) {\n      console.error('Error fetching attachments:', error);\n      setAttachments([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSavePatient = async () => {\n    try {\n      const response = await makeAuthenticatedRequest(`/api/patients/${patientId}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(editForm)\n      });\n\n      if (!response.ok) throw new Error('Failed to update patient');\n      \n      toast({\n        title: \"Sucesso!\",\n        description: \"Dados do paciente atualizados.\",\n      });\n      \n      setEditMode(false);\n      fetchPatientData();\n    } catch (error) {\n      console.error('Error updating patient:', error);\n      toast({\n        title: \"Erro\",\n        description: \"Erro ao atualizar paciente.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('patient_id', patientId);\n\n      const response = await makeAuthenticatedRequest('/api/patient-attachments', {\n        method: 'POST',\n        body: formData\n      });\n\n      if (!response.ok) throw new Error('Failed to upload file');\n      \n      toast({\n        title: \"Sucesso!\",\n        description: \"Arquivo enviado com sucesso.\",\n      });\n      \n      setUploadDialogOpen(false);\n      setSelectedFile(null);\n      fetchAttachments();\n    } catch (error) {\n      console.error('Error uploading file:', error);\n      toast({\n        title: \"Erro\",\n        description: \"Erro ao enviar arquivo.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const handleDeleteAttachment = async (attachmentId: string) => {\n    if (!confirm('Tem certeza que deseja excluir este arquivo?')) return;\n\n    try {\n      const response = await makeAuthenticatedRequest(`/api/patient-attachments/${attachmentId}`, {\n        method: 'DELETE'\n      });\n\n      if (!response.ok) throw new Error('Failed to delete attachment');\n      \n      toast({\n        title: \"Sucesso!\",\n        description: \"Arquivo excluído com sucesso.\",\n      });\n      \n      fetchAttachments();\n    } catch (error) {\n      console.error('Error deleting attachment:', error);\n      toast({\n        title: \"Erro\",\n        description: \"Erro ao excluir arquivo.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const updateAppointmentStatus = async (appointmentId: string, status: string) => {\n    try {\n      const response = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ status })\n      });\n\n      if (!response.ok) throw new Error('Failed to update appointment status');\n      \n      toast({\n        title: \"Sucesso!\",\n        description: \"Status da consulta atualizado.\",\n      });\n      \n      fetchAppointments();\n    } catch (error) {\n      console.error('Error updating appointment status:', error);\n      toast({\n        title: \"Erro\",\n        description: \"Erro ao atualizar status da consulta.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const handleDownloadAttachment = async (attachmentId: string, fileName: string) => {\n    try {\n      const response = await makeAuthenticatedRequest(`/api/patient-attachments/${attachmentId}`);\n      if (!response.ok) throw new Error('Failed to get download URL');\n\n      const result = await response.json();\n      const data = result.data || result;\n\n      // Create a temporary link to download the file\n      const link = document.createElement('a');\n      link.href = data.download_url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n      toast({\n        title: \"Erro\",\n        description: \"Erro ao baixar arquivo.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const formatFileSize = (bytes: number | null): string => {\n    if (!bytes) return 'Tamanho desconhecido';\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n\n  const calculateAge = (birthDate: string | null): string => {\n    if (!birthDate) return 'Idade não informada';\n    const today = new Date();\n    const birth = new Date(birthDate);\n    const age = today.getFullYear() - birth.getFullYear();\n    const monthDiff = today.getMonth() - birth.getMonth();\n\n    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {\n      return `${age - 1} anos`;\n    }\n    return `${age} anos`;\n  };\n\n  const handleOpenMedicalRecord = async (appointment: Appointment) => {\n    setSelectedAppointment(appointment);\n    setMedicalRecordOpen(true);\n    await fetchMedicalRecords(appointment.id);\n  };\n\n  const fetchMedicalRecords = async (appointmentId: string) => {\n    try {\n      const response = await makeAuthenticatedRequest(`/api/medical-records?appointment_id=${appointmentId}`);\n      if (!response.ok) throw new Error('Failed to fetch medical records');\n      const result = await response.json();\n      const data = result.data || result;\n      setMedicalRecords(Array.isArray(data) ? data : []);\n    } catch (error) {\n      console.error('Error fetching medical records:', error);\n      setMedicalRecords([]);\n    }\n  };\n\n  const handleAddMedicalRecord = async () => {\n    if (!newRecord.trim() || !selectedAppointment) return;\n\n    try {\n      const response = await makeAuthenticatedRequest('/api/medical-records', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          appointment_id: selectedAppointment.id,\n          patient_id: patientId,\n          notes: newRecord.trim()\n        })\n      });\n\n      if (!response.ok) throw new Error('Failed to add medical record');\n\n      toast({\n        title: \"Sucesso!\",\n        description: \"Registro médico adicionado.\",\n      });\n\n      setNewRecord('');\n      await fetchMedicalRecords(selectedAppointment.id);\n    } catch (error) {\n      console.error('Error adding medical record:', error);\n      toast({\n        title: \"Erro\",\n        description: \"Erro ao adicionar registro médico.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const handleStartTreatment = async () => {\n    if (!selectedAppointment) return;\n\n    try {\n      await updateAppointmentStatus(selectedAppointment.id, 'in_progress');\n      toast({\n        title: \"Atendimento iniciado\",\n        description: \"O status da consulta foi atualizado para 'Em Andamento'.\",\n      });\n\n      // Refresh appointments to update the status\n      await fetchAppointments();\n\n      // Update the selected appointment status\n      setSelectedAppointment({\n        ...selectedAppointment,\n        status: 'in_progress'\n      });\n    } catch (error) {\n      console.error('Error starting treatment:', error);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        {/* Header Skeleton */}\n        <div className=\"flex justify-between items-start\">\n          <div className=\"space-y-2\">\n            <div className=\"h-8 w-[250px] bg-muted animate-pulse rounded\" />\n            <div className=\"h-4 w-[200px] bg-muted animate-pulse rounded\" />\n          </div>\n          <div className=\"flex space-x-2\">\n            <div className=\"h-10 w-[80px] bg-muted animate-pulse rounded\" />\n            <div className=\"h-10 w-[80px] bg-muted animate-pulse rounded\" />\n          </div>\n        </div>\n\n        {/* Tabs Skeleton */}\n        <div className=\"space-y-4\">\n          <div className=\"flex space-x-4 border-b\">\n            <div className=\"h-10 w-[120px] bg-muted animate-pulse rounded\" />\n            <div className=\"h-10 w-[150px] bg-muted animate-pulse rounded\" />\n            <div className=\"h-10 w-[100px] bg-muted animate-pulse rounded\" />\n          </div>\n\n          {/* Content Skeleton */}\n          <div className=\"space-y-6\">\n            <div className=\"grid md:grid-cols-2 gap-6\">\n              <div className=\"space-y-4\">\n                {Array.from({ length: 4 }).map((_, i) => (\n                  <div key={i} className=\"space-y-2\">\n                    <div className=\"h-4 w-[80px] bg-muted animate-pulse rounded\" />\n                    <div className=\"h-10 w-full bg-muted animate-pulse rounded\" />\n                  </div>\n                ))}\n              </div>\n              <div className=\"space-y-4\">\n                {Array.from({ length: 4 }).map((_, i) => (\n                  <div key={i} className=\"space-y-2\">\n                    <div className=\"h-4 w-[80px] bg-muted animate-pulse rounded\" />\n                    <div className=\"h-10 w-full bg-muted animate-pulse rounded\" />\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!patient) {\n    return (\n      <div className=\"text-center py-8\">\n        <p className=\"text-muted-foreground\">Paciente não encontrado.</p>\n        <Button onClick={() => router.back()} className=\"mt-4\">\n          Voltar\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-start\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">{patient.name}</h1>\n          <p className=\"text-muted-foreground\">\n            Paciente desde {formatDateBR(patient.created_at)}\n          </p>\n        </div>\n        \n        <div className=\"flex items-center gap-2\">\n          {editMode ? (\n            <>\n              <Button onClick={handleSavePatient} size=\"sm\">\n                <Save className=\"mr-2 h-4 w-4\" />\n                Salvar\n              </Button>\n              <Button onClick={() => setEditMode(false)} variant=\"outline\" size=\"sm\">\n                <X className=\"mr-2 h-4 w-4\" />\n                Cancelar\n              </Button>\n            </>\n          ) : (\n            <Button onClick={() => setEditMode(true)} size=\"sm\">\n              <Edit className=\"mr-2 h-4 w-4\" />\n              Editar\n            </Button>\n          )}\n          <Button onClick={() => router.back()} variant=\"outline\" size=\"sm\">\n            Voltar\n          </Button>\n        </div>\n      </div>\n\n      <Tabs defaultValue=\"dados\" className=\"w-full\">\n        <TabsList className=\"grid w-full grid-cols-3\">\n          <TabsTrigger value=\"dados\" className=\"flex items-center gap-2\">\n            <User className=\"h-4 w-4\" />\n            Dados Principais\n          </TabsTrigger>\n          <TabsTrigger value=\"consultas\" className=\"flex items-center gap-2\">\n            <Calendar className=\"h-4 w-4\" />\n            Histórico de Consultas\n          </TabsTrigger>\n          <TabsTrigger value=\"anexos\" className=\"flex items-center gap-2\">\n            <FileText className=\"h-4 w-4\" />\n            Anexos\n          </TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"dados\" className=\"space-y-6 mt-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Informações Pessoais</CardTitle>\n              <CardDescription>\n                Dados básicos do paciente\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"name\">Nome Completo</Label>\n                  {editMode ? (\n                    <Input\n                      id=\"name\"\n                      value={editForm.name}\n                      onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}\n                    />\n                  ) : (\n                    <p className=\"text-sm\">{patient.name}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"birth_date\">Data de Nascimento</Label>\n                  {editMode ? (\n                    <Input\n                      id=\"birth_date\"\n                      type=\"date\"\n                      value={editForm.birth_date}\n                      onChange={(e) => setEditForm({ ...editForm, birth_date: e.target.value })}\n                    />\n                  ) : (\n                    <p className=\"text-sm\">\n                      {patient.birth_date\n                        ? `${formatDateBR(patient.birth_date)} (${calculateAge(patient.birth_date)})`\n                        : 'Não informado'\n                      }\n                    </p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"cpf\">CPF</Label>\n                  {editMode ? (\n                    <Input\n                      id=\"cpf\"\n                      value={editForm.cpf}\n                      onChange={(e) => setEditForm({ ...editForm, cpf: e.target.value })}\n                    />\n                  ) : (\n                    <p className=\"text-sm\">{patient.cpf || 'Não informado'}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"phone\">Telefone</Label>\n                  {editMode ? (\n                    <Input\n                      id=\"phone\"\n                      value={editForm.phone}\n                      onChange={(e) => setEditForm({ ...editForm, phone: e.target.value })}\n                    />\n                  ) : (\n                    <div className=\"flex items-center gap-2\">\n                      <Phone className=\"h-4 w-4 text-muted-foreground\" />\n                      <p className=\"text-sm\">{patient.phone || 'Não informado'}</p>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"email\">E-mail</Label>\n                  {editMode ? (\n                    <Input\n                      id=\"email\"\n                      type=\"email\"\n                      value={editForm.email}\n                      onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}\n                    />\n                  ) : (\n                    <div className=\"flex items-center gap-2\">\n                      <Mail className=\"h-4 w-4 text-muted-foreground\" />\n                      <p className=\"text-sm\">{patient.email || 'Não informado'}</p>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"space-y-2 md:col-span-2\">\n                  <Label htmlFor=\"address\">Endereço</Label>\n                  {editMode ? (\n                    <Textarea\n                      id=\"address\"\n                      value={editForm.address}\n                      onChange={(e) => setEditForm({ ...editForm, address: e.target.value })}\n                    />\n                  ) : (\n                    <div className=\"flex items-start gap-2\">\n                      <MapPin className=\"h-4 w-4 text-muted-foreground mt-0.5\" />\n                      <p className=\"text-sm\">{patient.address || 'Não informado'}</p>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"space-y-2 md:col-span-2\">\n                  <Label htmlFor=\"notes\">Observações</Label>\n                  {editMode ? (\n                    <Textarea\n                      id=\"notes\"\n                      value={editForm.notes}\n                      onChange={(e) => setEditForm({ ...editForm, notes: e.target.value })}\n                      rows={3}\n                    />\n                  ) : (\n                    <p className=\"text-sm\">{patient.notes || 'Nenhuma observação'}</p>\n                  )}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"consultas\" className=\"space-y-6 mt-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Histórico de Consultas</CardTitle>\n              <CardDescription>\n                {appointments.length} consulta(s) registrada(s)\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              {appointments.length === 0 ? (\n                <div className=\"text-center py-8 text-muted-foreground\">\n                  <Calendar className=\"mx-auto h-12 w-12 mb-4 opacity-50\" />\n                  <p>Nenhuma consulta registrada para este paciente</p>\n                </div>\n              ) : (\n                <div className=\"space-y-4\">\n                  {appointments.map((appointment) => (\n                    <div\n                      key={appointment.id}\n                      className=\"flex items-center justify-between p-4 rounded-lg border bg-card/50\"\n                    >\n                      <div className=\"flex items-center space-x-4\">\n                        <div className=\"flex flex-col items-center\">\n                          <span className=\"text-sm font-medium\">\n                            {formatDateBR(appointment.start_time)}\n                          </span>\n                          <span className=\"text-xs text-muted-foreground\">\n                            {formatTimeBR(appointment.start_time)}\n                          </span>\n                        </div>\n                        <div className=\"flex-1\">\n                          <h3 className=\"font-medium\">{appointment.title}</h3>\n                          <p className=\"text-sm text-muted-foreground\">\n                            {getAppointmentTypeBR(appointment.type)}\n                          </p>\n                          {appointment.healthcare_professional_name && (\n                            <p className=\"text-xs text-muted-foreground\">\n                              {appointment.healthcare_professional_name}\n                            </p>\n                          )}\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <Badge variant={\n                          appointment.status === 'in_progress' ? 'default' :\n                          appointment.status === 'completed' ? 'secondary' :\n                          appointment.status === 'cancelled' ? 'destructive' :\n                          'outline'\n                        }>\n                          {getAppointmentStatusBR(appointment.status)}\n                        </Badge>\n                        {canViewMedicalRecords && !permissionsLoading && (\n                          <Button\n                            size=\"sm\"\n                            variant=\"outline\"\n                            onClick={() => handleOpenMedicalRecord(appointment)}\n                          >\n                            <Stethoscope className=\"mr-2 h-4 w-4\" />\n                            Prontuário\n                          </Button>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"anexos\" className=\"space-y-6 mt-6\">\n          <Card>\n            <CardHeader>\n              <div className=\"flex justify-between items-center\">\n                <div>\n                  <CardTitle>Anexos</CardTitle>\n                  <CardDescription>\n                    {attachments.length} arquivo(s) anexado(s)\n                  </CardDescription>\n                </div>\n                <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>\n                  <DialogTrigger asChild>\n                    <Button size=\"sm\">\n                      <Plus className=\"mr-2 h-4 w-4\" />\n                      Adicionar Arquivo\n                    </Button>\n                  </DialogTrigger>\n                  <DialogContent>\n                    <DialogHeader>\n                      <DialogTitle>Enviar Arquivo</DialogTitle>\n                      <DialogDescription>\n                        Selecione um arquivo para anexar ao prontuário do paciente\n                      </DialogDescription>\n                    </DialogHeader>\n                    <div className=\"space-y-4\">\n                      <div className=\"space-y-2\">\n                        <Label htmlFor=\"file\">Arquivo</Label>\n                        <Input\n                          id=\"file\"\n                          type=\"file\"\n                          onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}\n                        />\n                      </div>\n                    </div>\n                    <DialogFooter>\n                      <Button variant=\"outline\" onClick={() => setUploadDialogOpen(false)}>\n                        Cancelar\n                      </Button>\n                      <Button onClick={handleFileUpload} disabled={!selectedFile}>\n                        <Upload className=\"mr-2 h-4 w-4\" />\n                        Enviar\n                      </Button>\n                    </DialogFooter>\n                  </DialogContent>\n                </Dialog>\n              </div>\n            </CardHeader>\n            <CardContent>\n              {attachments.length === 0 ? (\n                <div className=\"text-center py-8 text-muted-foreground\">\n                  <FileText className=\"mx-auto h-12 w-12 mb-4 opacity-50\" />\n                  <p>Nenhum arquivo anexado</p>\n                </div>\n              ) : (\n                <div className=\"space-y-4\">\n                  {attachments.map((attachment) => (\n                    <div\n                      key={attachment.id}\n                      className=\"flex items-center justify-between p-4 rounded-lg border bg-card/50\"\n                    >\n                      <div className=\"flex items-center space-x-4\">\n                        <FileText className=\"h-8 w-8 text-muted-foreground\" />\n                        <div>\n                          <h3 className=\"font-medium\">{attachment.file_name}</h3>\n                          <p className=\"text-sm text-muted-foreground\">\n                            {formatFileSize(attachment.file_size)} • {formatDateTimeBR(attachment.created_at)}\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={() => handleDownloadAttachment(attachment.id, attachment.file_name)}\n                        >\n                          <Download className=\"mr-2 h-4 w-4\" />\n                          Baixar\n                        </Button>\n                        <Button\n                          size=\"sm\"\n                          variant=\"destructive\"\n                          onClick={() => handleDeleteAttachment(attachment.id)}\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n\n      {/* Medical Record Dialog */}\n      <Dialog open={medicalRecordOpen} onOpenChange={setMedicalRecordOpen}>\n        <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n          <DialogHeader>\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setMedicalRecordOpen(false)}\n              >\n                <ArrowLeft className=\"h-4 w-4\" />\n              </Button>\n              <div>\n                <DialogTitle>Prontuário Médico</DialogTitle>\n                <DialogDescription>\n                  {selectedAppointment && (\n                    <>\n                      {selectedAppointment.title} - {formatDateTimeBR(selectedAppointment.start_time)}\n                    </>\n                  )}\n                </DialogDescription>\n              </div>\n            </div>\n          </DialogHeader>\n\n          <div className=\"space-y-6\">\n            {/* Appointment Info */}\n            {selectedAppointment && (\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Informações da Consulta</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-2\">\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <div>\n                      <Label className=\"text-sm font-medium\">Data</Label>\n                      <p className=\"text-sm\">{formatDateBR(selectedAppointment.start_time)}</p>\n                    </div>\n                    <div>\n                      <Label className=\"text-sm font-medium\">Horário</Label>\n                      <p className=\"text-sm\">{formatTimeBR(selectedAppointment.start_time)} - {formatTimeBR(selectedAppointment.end_time)}</p>\n                    </div>\n                    <div>\n                      <Label className=\"text-sm font-medium\">Tipo</Label>\n                      <p className=\"text-sm\">{getAppointmentTypeBR(selectedAppointment.type)}</p>\n                    </div>\n                    <div>\n                      <Label className=\"text-sm font-medium\">Status</Label>\n                      <Badge variant={\n                        selectedAppointment.status === 'confirmed' ? 'default' :\n                        selectedAppointment.status === 'completed' ? 'secondary' :\n                        selectedAppointment.status === 'cancelled' ? 'destructive' :\n                        selectedAppointment.status === 'in_progress' ? 'default' :\n                        'outline'\n                      }>\n                        {getAppointmentStatusBR(selectedAppointment.status)}\n                      </Badge>\n                    </div>\n                  </div>\n                  {selectedAppointment.healthcare_professional_name && (\n                    <div>\n                      <Label className=\"text-sm font-medium\">Profissional</Label>\n                      <p className=\"text-sm\">{selectedAppointment.healthcare_professional_name}</p>\n                    </div>\n                  )}\n                  {selectedAppointment.description && (\n                    <div>\n                      <Label className=\"text-sm font-medium\">Descrição</Label>\n                      <p className=\"text-sm\">{selectedAppointment.description}</p>\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Start Treatment Button */}\n            {selectedAppointment && selectedAppointment.status === 'confirmed' && (\n              <Card>\n                <CardContent className=\"pt-6\">\n                  <Button onClick={handleStartTreatment} className=\"w-full\">\n                    <Clock className=\"mr-2 h-4 w-4\" />\n                    Iniciar Atendimento\n                  </Button>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Medical Records History */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"text-lg\">Histórico de Registros</CardTitle>\n                <CardDescription>\n                  Registros médicos desta consulta\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                {medicalRecords.length === 0 ? (\n                  <div className=\"text-center py-8 text-muted-foreground\">\n                    <FileText className=\"mx-auto h-12 w-12 mb-4 opacity-50\" />\n                    <p>Nenhum registro médico encontrado</p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    {medicalRecords.map((record, index) => (\n                      <div key={index} className=\"border-l-4 border-primary pl-4 py-2\">\n                        <div className=\"flex justify-between items-start mb-2\">\n                          <span className=\"text-sm font-medium\">\n                            {formatDateTimeBR(record.created_at)}\n                          </span>\n                          <Badge variant=\"outline\" className=\"text-xs\">\n                            {record.created_by_name || 'Sistema'}\n                          </Badge>\n                        </div>\n                        <p className=\"text-sm text-muted-foreground whitespace-pre-wrap\">\n                          {record.notes}\n                        </p>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n\n            {/* Add New Record */}\n            {selectedAppointment && (selectedAppointment.status === 'in_progress' || selectedAppointment.status === 'confirmed') && (\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Adicionar Registro</CardTitle>\n                  <CardDescription>\n                    Adicione observações e anotações sobre esta consulta\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <Textarea\n                    placeholder=\"Digite suas observações sobre a consulta...\"\n                    value={newRecord}\n                    onChange={(e) => setNewRecord(e.target.value)}\n                    rows={4}\n                  />\n                  <Button\n                    onClick={handleAddMedicalRecord}\n                    disabled={!newRecord.trim()}\n                    className=\"w-full\"\n                  >\n                    <Plus className=\"mr-2 h-4 w-4\" />\n                    Adicionar Registro\n                  </Button>\n                </CardContent>\n              </Card>\n            )}\n          </div>\n        </DialogContent>\n      </Dialog>\n    </div>\n  );\n};\n\nexport default PatientDetailPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAGA;AACA;AAEA;;;AApCA;;;;;;;;;;;;;;;;AAwEA,MAAM,oBAAoB;;IACxB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,YAAY,OAAO,EAAE;IAC3B,MAAM,EAAE,qBAAqB,EAAE,SAAS,kBAAkB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IAE5E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,YAAY;QACZ,KAAK;QACL,SAAS;QACT,OAAO;IACT;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,WAAW;gBACb;gBACA;gBACA;YACF;QACF;sCAAG;QAAC;KAAU;IAEd,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,cAAc,EAAE,WAAW;YAC5E,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,OAAO,OAAO,IAAI,IAAI;YAC5B,WAAW;YACX,YAAY;gBACV,MAAM,KAAK,IAAI,IAAI;gBACnB,OAAO,KAAK,KAAK,IAAI;gBACrB,OAAO,KAAK,KAAK,IAAI;gBACrB,YAAY,KAAK,UAAU,IAAI;gBAC/B,KAAK,KAAK,GAAG,IAAI;gBACjB,SAAS,KAAK,OAAO,IAAI;gBACzB,OAAO,KAAK,KAAK,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,6BAA6B,EAAE,WAAW;YAC3F,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,OAAO,OAAO,IAAI,IAAI;YAC5B,gBAAgB,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;QACjD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,gBAAgB,EAAE;QACpB;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,oCAAoC,EAAE,WAAW;YAClG,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,OAAO,OAAO,IAAI,IAAI;YAC5B,eAAe,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;QAChD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,eAAe,EAAE;QACnB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE;gBAC5E,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,YAAY;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,cAAc;YAE9B,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,4BAA4B;gBAC1E,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,oBAAoB;YACpB,gBAAgB;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI,CAAC,QAAQ,iDAAiD;QAE9D,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,yBAAyB,EAAE,cAAc,EAAE;gBAC1F,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,0BAA0B,OAAO,eAAuB;QAC5D,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,kBAAkB,EAAE,eAAe,EAAE;gBACpF,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,2BAA2B,OAAO,cAAsB;QAC5D,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,yBAAyB,EAAE,cAAc;YAC1F,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,OAAO,OAAO,IAAI,IAAI;YAE5B,+CAA+C;YAC/C,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG,KAAK,YAAY;YAC7B,KAAK,QAAQ,GAAG;YAChB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,OAAO,OAAO;QACnB,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;IAC3E;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,WAAW,OAAO;QACvB,MAAM,QAAQ,IAAI;QAClB,MAAM,QAAQ,IAAI,KAAK;QACvB,MAAM,MAAM,MAAM,WAAW,KAAK,MAAM,WAAW;QACnD,MAAM,YAAY,MAAM,QAAQ,KAAK,MAAM,QAAQ;QAEnD,IAAI,YAAY,KAAM,cAAc,KAAK,MAAM,OAAO,KAAK,MAAM,OAAO,IAAK;YAC3E,OAAO,GAAG,MAAM,EAAE,KAAK,CAAC;QAC1B;QACA,OAAO,GAAG,IAAI,KAAK,CAAC;IACtB;IAEA,MAAM,0BAA0B,OAAO;QACrC,uBAAuB;QACvB,qBAAqB;QACrB,MAAM,oBAAoB,YAAY,EAAE;IAC1C;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC,oCAAoC,EAAE,eAAe;YACtG,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,OAAO,OAAO,IAAI,IAAI;YAC5B,kBAAkB,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,kBAAkB,EAAE;QACtB;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,qBAAqB;QAE/C,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,wBAAwB;gBACtE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,gBAAgB,oBAAoB,EAAE;oBACtC,YAAY;oBACZ,OAAO,UAAU,IAAI;gBACvB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,aAAa;YACb,MAAM,oBAAoB,oBAAoB,EAAE;QAClD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,qBAAqB;QAE1B,IAAI;YACF,MAAM,wBAAwB,oBAAoB,EAAE,EAAE;YACtD,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,4CAA4C;YAC5C,MAAM;YAEN,yCAAyC;YACzC,uBAAuB;gBACrB,GAAG,mBAAmB;gBACtB,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAKnB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;gDAAY,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;;+CAFP;;;;;;;;;;kDAMd,6LAAC;wCAAI,WAAU;kDACZ,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;gDAAY,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;;+CAFP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAW1B;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;8BACrC,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAS,IAAM,OAAO,IAAI;oBAAI,WAAU;8BAAO;;;;;;;;;;;;IAK7D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAqC,QAAQ,IAAI;;;;;;0CAC/D,6LAAC;gCAAE,WAAU;;oCAAwB;oCACnB,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,UAAU;;;;;;;;;;;;;kCAInD,6LAAC;wBAAI,WAAU;;4BACZ,yBACC;;kDACE,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAmB,MAAK;;0DACvC,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,YAAY;wCAAQ,SAAQ;wCAAU,MAAK;;0DAChE,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;6DAKlC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,YAAY;gCAAO,MAAK;;kDAC7C,6LAAC,8MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIrC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,OAAO,IAAI;gCAAI,SAAQ;gCAAU,MAAK;0CAAK;;;;;;;;;;;;;;;;;;0BAMtE,6LAAC,mIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAQ,WAAU;;kCACnC,6LAAC,mIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAQ,WAAU;;kDACnC,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG9B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;;kDACvC,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGlC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAS,WAAU;;kDACpC,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAKpC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAQ,WAAU;kCACnC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAO;;;;;;oDACrB,yBACC,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,IAAI;wDACpB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAC;;;;;6EAGnE,6LAAC;wDAAE,WAAU;kEAAW,QAAQ,IAAI;;;;;;;;;;;;0DAIxC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAa;;;;;;oDAC3B,yBACC,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,UAAU;wDAC1B,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,YAAY,EAAE,MAAM,CAAC,KAAK;4DAAC;;;;;6EAGzE,6LAAC;wDAAE,WAAU;kEACV,QAAQ,UAAU,GACf,GAAG,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,UAAU,EAAE,EAAE,EAAE,aAAa,QAAQ,UAAU,EAAE,CAAC,CAAC,GAC3E;;;;;;;;;;;;0DAMV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAM;;;;;;oDACpB,yBACC,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,GAAG;wDACnB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4DAAC;;;;;6EAGlE,6LAAC;wDAAE,WAAU;kEAAW,QAAQ,GAAG,IAAI;;;;;;;;;;;;0DAI3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;oDACtB,yBACC,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,KAAK;wDACrB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;;;;;6EAGpE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;gEAAE,WAAU;0EAAW,QAAQ,KAAK,IAAI;;;;;;;;;;;;;;;;;;0DAK/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;oDACtB,yBACC,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;;;;;6EAGpE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;gEAAE,WAAU;0EAAW,QAAQ,KAAK,IAAI;;;;;;;;;;;;;;;;;;0DAK/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;oDACxB,yBACC,6LAAC,uIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,OAAO,SAAS,OAAO;wDACvB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAC;;;;;6EAGtE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;gEAAE,WAAU;0EAAW,QAAQ,OAAO,IAAI;;;;;;;;;;;;;;;;;;0DAKjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;oDACtB,yBACC,6LAAC,uIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,OAAO,SAAS,KAAK;wDACrB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAClE,MAAM;;;;;6EAGR,6LAAC;wDAAE,WAAU;kEAAW,QAAQ,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQrD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAY,WAAU;kCACvC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;;gDACb,aAAa,MAAM;gDAAC;;;;;;;;;;;;;8CAGzB,6LAAC,mIAAA,CAAA,cAAW;8CACT,aAAa,MAAM,KAAK,kBACvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAE;;;;;;;;;;;6DAGL,6LAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC;gDAEC,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFACb,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,YAAY,UAAU;;;;;;kFAEtC,6LAAC;wEAAK,WAAU;kFACb,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,YAAY,UAAU;;;;;;;;;;;;0EAGxC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAe,YAAY,KAAK;;;;;;kFAC9C,6LAAC;wEAAE,WAAU;kFACV,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE,YAAY,IAAI;;;;;;oEAEvC,YAAY,4BAA4B,kBACvC,6LAAC;wEAAE,WAAU;kFACV,YAAY,4BAA4B;;;;;;;;;;;;;;;;;;kEAKjD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SACL,YAAY,MAAM,KAAK,gBAAgB,YACvC,YAAY,MAAM,KAAK,cAAc,cACrC,YAAY,MAAM,KAAK,cAAc,gBACrC;0EAEC,CAAA,GAAA,8HAAA,CAAA,yBAAsB,AAAD,EAAE,YAAY,MAAM;;;;;;4DAE3C,yBAAyB,CAAC,oCACzB,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,wBAAwB;;kFAEvC,6LAAC,mNAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;+CAvCzC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAoDjC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAS,WAAU;kCACpC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,mIAAA,CAAA,kBAAe;;4DACb,YAAY,MAAM;4DAAC;;;;;;;;;;;;;0DAGxB,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAM;gDAAkB,cAAc;;kEAC5C,6LAAC,qIAAA,CAAA,gBAAa;wDAAC,OAAO;kEACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,MAAK;;8EACX,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;kEAIrC,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,eAAY;;kFACX,6LAAC,qIAAA,CAAA,cAAW;kFAAC;;;;;;kFACb,6LAAC,qIAAA,CAAA,oBAAiB;kFAAC;;;;;;;;;;;;0EAIrB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAO;;;;;;sFACtB,6LAAC,oIAAA,CAAA,QAAK;4EACJ,IAAG;4EACH,MAAK;4EACL,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;;;;;;;;;;;;;;;;;0EAI9D,6LAAC,qIAAA,CAAA,eAAY;;kFACX,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAU,SAAS,IAAM,oBAAoB;kFAAQ;;;;;;kFAGrE,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAS;wEAAkB,UAAU,CAAC;;0FAC5C,6LAAC,yMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ/C,6LAAC,mIAAA,CAAA,cAAW;8CACT,YAAY,MAAM,KAAK,kBACtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAE;;;;;;;;;;;6DAGL,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC;gDAEC,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAe,WAAW,SAAS;;;;;;kFACjD,6LAAC;wEAAE,WAAU;;4EACV,eAAe,WAAW,SAAS;4EAAE;4EAAI,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,UAAU;;;;;;;;;;;;;;;;;;;kEAItF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,yBAAyB,WAAW,EAAE,EAAE,WAAW,SAAS;;kFAE3E,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGvC,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,uBAAuB,WAAW,EAAE;0EAEnD,cAAA,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;+CA1BjB,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAuClC,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAmB,cAAc;0BAC7C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,qBAAqB;kDAEpC,cAAA,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;;0DACC,6LAAC,qIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,6LAAC,qIAAA,CAAA,oBAAiB;0DACf,qCACC;;wDACG,oBAAoB,KAAK;wDAAC;wDAAI,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,oBAAoB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ1F,6LAAC;4BAAI,WAAU;;gCAEZ,qCACC,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;;;;;;sDAEjC,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC,oIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAsB;;;;;;8EACvC,6LAAC;oEAAE,WAAU;8EAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB,UAAU;;;;;;;;;;;;sEAErE,6LAAC;;8EACC,6LAAC,oIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAsB;;;;;;8EACvC,6LAAC;oEAAE,WAAU;;wEAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB,UAAU;wEAAE;wEAAI,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB,QAAQ;;;;;;;;;;;;;sEAEpH,6LAAC;;8EACC,6LAAC,oIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAsB;;;;;;8EACvC,6LAAC;oEAAE,WAAU;8EAAW,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE,oBAAoB,IAAI;;;;;;;;;;;;sEAEvE,6LAAC;;8EACC,6LAAC,oIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAsB;;;;;;8EACvC,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SACL,oBAAoB,MAAM,KAAK,cAAc,YAC7C,oBAAoB,MAAM,KAAK,cAAc,cAC7C,oBAAoB,MAAM,KAAK,cAAc,gBAC7C,oBAAoB,MAAM,KAAK,gBAAgB,YAC/C;8EAEC,CAAA,GAAA,8HAAA,CAAA,yBAAsB,AAAD,EAAE,oBAAoB,MAAM;;;;;;;;;;;;;;;;;;gDAIvD,oBAAoB,4BAA4B,kBAC/C,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAsB;;;;;;sEACvC,6LAAC;4DAAE,WAAU;sEAAW,oBAAoB,4BAA4B;;;;;;;;;;;;gDAG3E,oBAAoB,WAAW,kBAC9B,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAsB;;;;;;sEACvC,6LAAC;4DAAE,WAAU;sEAAW,oBAAoB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;gCAQhE,uBAAuB,oBAAoB,MAAM,KAAK,6BACrD,6LAAC,mIAAA,CAAA,OAAI;8CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAsB,WAAU;;8DAC/C,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;8CAQ1C,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;8DAC/B,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;sDACT,eAAe,MAAM,KAAK,kBACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;kEAAE;;;;;;;;;;;qEAGL,6LAAC;gDAAI,WAAU;0DACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFACb,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,UAAU;;;;;;kFAErC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;kFAChC,OAAO,eAAe,IAAI;;;;;;;;;;;;0EAG/B,6LAAC;gEAAE,WAAU;0EACV,OAAO,KAAK;;;;;;;uDAVP;;;;;;;;;;;;;;;;;;;;;gCAoBnB,uBAAuB,CAAC,oBAAoB,MAAM,KAAK,iBAAiB,oBAAoB,MAAM,KAAK,WAAW,mBACjH,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;8DAC/B,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC,uIAAA,CAAA,WAAQ;oDACP,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC5C,MAAM;;;;;;8DAER,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU,CAAC,UAAU,IAAI;oDACzB,WAAU;;sEAEV,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD;GA32BM;;QACW,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QACN,+HAAA,CAAA,WAAQ;QAEqC,kIAAA,CAAA,iBAAc;;;KALzE;uCA62BS", "debugId": null}}]}