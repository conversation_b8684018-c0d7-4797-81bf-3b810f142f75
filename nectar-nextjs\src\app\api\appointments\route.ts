import { NextRequest } from 'next/server'
import { withAuth, withAuthAndPermission, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type Appointment = Tables<'appointments'>
type AppointmentInsert = TablesInsert<'appointments'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const date = searchParams.get('date')
      const patientId = searchParams.get('patient_id')

      // Get accessible users first
      const { data: accessibleUsers, error: accessError } = await supabase
        .rpc('get_accessible_users', { current_user_id: userId })

      if (accessError) {
        return handleApiError(accessError)
      }

      const accessibleUserIds = accessibleUsers?.map((user: any) => user.user_id) || []

      if (accessibleUserIds.length === 0) {
        return createApiResponse([])
      }

      // Check if current user is a doctor by looking for healthcare_professional record
      const { data: currentUserProfessional, error: profError } = await supabase
        .from('healthcare_professionals')
        .select('id')
        .eq('user_id', userId)
        .single()

      console.log('🏥 Current user professional:', { currentUserProfessional, profError, userId })

      let appointments: any[] = []

      if (currentUserProfessional && !profError) {
        // For doctors: get appointments assigned to them via healthcare_professional_id
        console.log('📋 Fetching appointments for doctor - professionalId:', currentUserProfessional.id)

        const { data, error } = await supabase
          .from('appointments')
          .select(`
            *,
            patients!inner(name),
            healthcare_professionals(name, specialty)
          `)
          .eq('healthcare_professional_id', currentUserProfessional.id)

        if (error) {
          console.error('Error fetching doctor appointments:', error)
          return handleApiError(error)
        }

        appointments = data || []
        console.log('📋 Doctor appointments found:', appointments.length)
      } else {
        // For non-doctors (secretaries, admins): show appointments they have access to via user_id
        console.log('📋 Using user_id filter for non-doctor:', accessibleUserIds)
        const { data, error } = await supabase
          .from('appointments')
          .select(`
            *,
            patients!inner(name),
            healthcare_professionals(name, specialty)
          `)
          .in('user_id', accessibleUserIds)

        if (error) {
          console.error('Error fetching user appointments:', error)
          return handleApiError(error)
        }

        appointments = data || []
        console.log('📋 User appointments found:', appointments.length)
      }

      // Apply date filter if provided
      if (date) {
        // Parse date in local timezone to avoid UTC conversion issues
        const [year, month, day] = date.split('-').map(Number)
        const startDate = new Date(year, month - 1, day, 0, 0, 0, 0)
        const endDate = new Date(year, month - 1, day + 1, 0, 0, 0, 0)

        // Filter appointments by date
        appointments = appointments.filter(appointment => {
          const appointmentDate = new Date(appointment.start_time)
          return appointmentDate >= startDate && appointmentDate < endDate
        })
      }

      if (patientId) {
        appointments = appointments.filter(appointment => appointment.patient_id === patientId)
      }

      // Sort appointments - ascending for date-specific queries (agenda), descending for patient history
      const orderDirection = date ? 1 : -1
      appointments.sort((a, b) => {
        const dateA = new Date(a.start_time)
        const dateB = new Date(b.start_time)
        return orderDirection * (dateA.getTime() - dateB.getTime())
      })

      console.log('📅 Appointments found:', appointments?.length || 0, 'for user:', userId)
      if (appointments && appointments.length > 0) {
        console.log('📋 First appointment sample:', {
          id: appointments[0].id,
          user_id: appointments[0].user_id,
          healthcare_professional_id: appointments[0].healthcare_professional_id,
          title: appointments[0].title
        })
      }

      return createApiResponse(appointments)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      const { procedures = [], ...appointmentBody } = body

      // Get accessible users to validate target user
      const { data: accessibleUsers, error: accessError } = await supabase
        .rpc('get_accessible_users', { current_user_id: userId })

      if (accessError) {
        return handleApiError(accessError)
      }

      const accessibleUserIds = accessibleUsers?.map((user: any) => user.user_id) || []

      // Determine the target user_id for the appointment
      let targetUserId = userId // Default to current user

      // If a healthcare professional is specified, find their user_id
      if (body.healthcare_professional_id) {
        const { data: professional, error: profError } = await supabase
          .from('healthcare_professionals')
          .select('user_id')
          .eq('id', body.healthcare_professional_id)
          .single()

        if (!profError && professional && accessibleUserIds.includes(professional.user_id)) {
          targetUserId = professional.user_id
          console.log('🎯 Setting appointment user_id to professional:', professional.user_id)
        }
      }

      // Legacy support: If a specific doctor_id is specified and user has access to them, use that
      if (body.doctor_id && accessibleUserIds.includes(body.doctor_id)) {
        targetUserId = body.doctor_id
        console.log('🎯 Setting appointment user_id to doctor_id:', body.doctor_id)
      }

      console.log('📝 Creating appointment with user_id:', targetUserId, 'for professional:', body.healthcare_professional_id)

      const appointmentData: AppointmentInsert = {
        ...appointmentBody,
        user_id: targetUserId,
        status: body.status || 'scheduled',
        start_time: new Date(body.start_time).toISOString(),
        end_time: new Date(body.end_time).toISOString(),
        healthcare_professional_id: body.healthcare_professional_id || null,
        total_price: body.total_price || null,
        recurrence_rule: body.has_recurrence ? JSON.stringify({
          type: body.recurrence_type,
          interval: body.recurrence_interval,
          days: body.recurrence_days,
          end_type: body.recurrence_end_type,
          end_date: body.recurrence_end_date,
          count: body.recurrence_count
        }) : null,
        recurrence_end_date: body.recurrence_end_date || null
      }

      // Remove doctor_id from appointment data as it's not a column in appointments table
      delete (appointmentData as any).doctor_id

      // Create appointment
      const { data: appointment, error: appointmentError } = await supabase
        .from('appointments')
        .insert(appointmentData)
        .select(`
          *,
          patients!inner(name),
          healthcare_professionals(name, specialty)
        `)
        .single()

      if (appointmentError) {
        return handleApiError(appointmentError)
      }

      // Create appointment procedures if any
      if (procedures.length > 0) {
        const procedureInserts = procedures.map((proc: any) => ({
          appointment_id: appointment.id,
          procedure_id: proc.procedure_id,
          quantity: parseInt(proc.quantity),
          unit_price: parseFloat(proc.unit_price),
          total_price: parseFloat(proc.total_price)
        }))

        const { error: proceduresError } = await supabase
          .from('appointment_procedures')
          .insert(procedureInserts)

        if (proceduresError) {
          // Rollback appointment if procedures fail
          await supabase.from('appointments').delete().eq('id', appointment.id)
          return handleApiError(proceduresError)
        }
      }

      return createApiResponse(appointment, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
