import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesUpdate } from '@/types/supabase'

type Patient = Tables<'patients'>
type PatientUpdate = TablesUpdate<'patients'>

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params

  return withAuth(request, async (userId, supabase) => {
    try {
      // Use the new get_accessible_patients function to check access
      const { data: accessiblePatients, error } = await supabase
        .rpc('get_accessible_patients', { current_user_id: userId })

      if (error) {
        return handleApiError(error)
      }

      // Find the specific patient in the accessible patients list
      const patient = accessiblePatients?.find((p: any) => p.id === id)

      if (!patient) {
        return createApiResponse(null, 'Patient not found or access denied', 404)
      }

      return createApiResponse(patient)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params

  return withAuth(request, async (userId, supabase) => {
    try {
      // Check if user has access to this patient
      const { data: accessiblePatients, error: accessError } = await supabase
        .rpc('get_accessible_patients', { current_user_id: userId })

      if (accessError) {
        return handleApiError(accessError)
      }

      const hasAccess = accessiblePatients?.some((p: any) => p.id === id)
      if (!hasAccess) {
        return createApiResponse(null, 'Patient not found or access denied', 404)
      }

      const body = await request.json()

      const updateData: PatientUpdate = {
        ...body,
        birth_date: body.birth_date || null
      }

      const { data: patient, error } = await supabase
        .from('patients')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(patient)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  return withAuth(request, async (userId, supabase) => {
    try {
      const { error } = await supabase
        .from('patients')
        .delete()
        .eq('id', id)
        .eq('user_id', userId)

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse({ success: true })
    } catch (error) {
      return handleApiError(error)
    }
  })
}
