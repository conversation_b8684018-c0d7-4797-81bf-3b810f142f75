import { NextRequest } from 'next/server'
import { withAuth, withAuthAndPermission, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type Patient = Tables<'patients'>
type PatientInsert = TablesInsert<'patients'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      console.log('Fetching patients for user:', userId);

      // Use the new get_accessible_patients function that includes associations
      const { data: patients, error } = await supabase
        .rpc('get_accessible_patients', { current_user_id: userId })

      if (error) {
        console.error('Error fetching accessible patients:', error);
        return handleApiError(error)
      }

      // Sort patients by name
      const sortedPatients = (patients || []).sort((a: any, b: any) =>
        (a.name || '').localeCompare(b.name || '')
      )

      console.log('Found patients:', sortedPatients.length, 'for user:', userId);
      if (sortedPatients.length > 0) {
        console.log('Patient access types:', sortedPatients.reduce((acc: any, p: any) => {
          acc[p.access_type] = (acc[p.access_type] || 0) + 1;
          return acc;
        }, {}));
      }

      return createApiResponse(sortedPatients)
    } catch (error) {
      console.error('API error:', error);
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()

      // Get accessible users to validate target user
      const { data: accessibleUsers, error: accessError } = await supabase
        .rpc('get_accessible_users', { current_user_id: userId })

      if (accessError) {
        return handleApiError(accessError)
      }

      const accessibleUserIds = accessibleUsers?.map((user: any) => user.user_id) || []

      // Determine the target user_id for the patient
      let targetUserId = userId // Default to current user
      let associatedDoctorId: string | null = null

      // If a specific doctor is specified and user has access to them, use that
      if (body.doctor_id && accessibleUserIds.includes(body.doctor_id)) {
        targetUserId = body.doctor_id
      }

      // If healthcare_professional_id is specified, we'll create an association
      if (body.healthcare_professional_id) {
        // Validate that the healthcare professional exists and is accessible
        const { data: healthcareProfessional, error: hpError } = await supabase
          .from('healthcare_professionals')
          .select('id, user_id')
          .eq('id', body.healthcare_professional_id)
          .single()

        if (!hpError && healthcareProfessional && accessibleUserIds.includes(healthcareProfessional.user_id)) {
          associatedDoctorId = body.healthcare_professional_id
          console.log('🔗 Will create association with healthcare professional:', associatedDoctorId)
        }
      }

      const patientData: PatientInsert = {
        ...body,
        user_id: targetUserId,
        birth_date: body.birth_date || null
      }

      // Remove fields that are not columns in patients table
      delete (patientData as any).doctor_id
      delete (patientData as any).healthcare_professional_id

      const { data: patient, error } = await supabase
        .from('patients')
        .insert(patientData)
        .select()
        .single()

      if (error) {
        return handleApiError(error)
      }

      // Create association if healthcare professional was specified
      if (associatedDoctorId && patient) {
        const { error: associationError } = await supabase
          .from('patient_healthcare_professional_associations')
          .insert({
            patient_id: patient.id,
            healthcare_professional_id: associatedDoctorId,
            created_by: userId
          })

        if (associationError) {
          console.error('Error creating patient-healthcare professional association:', associationError)
          // Don't fail the patient creation, just log the error
        } else {
          console.log('✅ Created patient-healthcare professional association')
        }
      }

      return createApiResponse(patient, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
