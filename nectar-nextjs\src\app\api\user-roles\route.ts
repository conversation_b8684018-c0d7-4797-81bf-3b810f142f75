import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type UserRole = Tables<'user_roles'>
type UserRoleInsert = TablesInsert<'user_roles'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      // Get actual user roles from the database
      const { data: userRoles, error } = await supabase
        .from('user_roles')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching user roles:', error)
        return handleApiError(error)
      }

      return createApiResponse(userRoles || [])
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      const { role_name } = body

      if (!role_name) {
        return createApiResponse(null, 'role_name is required', 400)
      }

      // Check if user is admin (only admins can assign roles)
      const { data: currentUserRoles, error: rolesError } = await supabase
        .from('user_roles')
        .select('role_name')
        .eq('user_id', userId)

      if (rolesError) {
        return handleApiError(rolesError)
      }

      const isAdmin = currentUserRoles?.some(role => role.role_name === 'admin')
      if (!isAdmin) {
        return createApiResponse(null, 'Only admins can assign roles', 403)
      }

      // Insert the new role
      const { data: newRole, error } = await supabase
        .from('user_roles')
        .insert({
          user_id: body.user_id || userId,
          role_name
        })
        .select()
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(newRole, 'Role assigned successfully', 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
