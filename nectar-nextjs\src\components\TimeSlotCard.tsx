import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Clock, Lock, Plus, User, Calendar, CheckCircle, Play, X } from 'lucide-react';
import { formatSlotTime, type TimeSlot } from '@/lib/time-slots';
import { getStatusBadgeVariant, getStatusTextBR, type AppointmentStatus } from '@/lib/status-colors';

interface TimeSlotCardProps {
  slot: TimeSlot;
  onSlotClick: (slot: TimeSlot) => void;
  onBlockSlot: (slot: TimeSlot, event: React.MouseEvent) => void;
  onUnblockSlot?: (slot: TimeSlot, event: React.MouseEvent) => void;
  onConfirmAppointment?: (appointment: any, event: React.MouseEvent) => void;
  onStartConsultation?: (appointment: any, event: React.MouseEvent) => void;
  onCancelAppointment?: (appointment: any, event: React.MouseEvent) => void;
  canBlockSlots: boolean;
  canStartConsultation: boolean;
  className?: string;
}

const TimeSlotCard: React.FC<TimeSlotCardProps> = ({
  slot,
  onSlotClick,
  onBlockSlot,
  onUnblockSlot,
  onConfirmAppointment,
  onStartConsultation,
  onCancelAppointment,
  canBlockSlots,
  canStartConsultation,
  className = ''
}) => {
  const handleCardClick = () => {
    if (slot.isAvailable) {
      onSlotClick(slot);
    } else if (slot.appointment) {
      // Para consultas "em atendimento" ou "concluído", redirecionar para prontuário se possível
      if (['in_progress', 'completed'].includes(slot.appointment.status) && canStartConsultation) {
        // O redirecionamento será tratado no onSlotClick da página pai
        onSlotClick(slot);
      } else {
        // Para outros status, abrir formulário de edição
        onSlotClick(slot);
      }
    }
  };

  const handleBlockClick = (event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent triggering the card click
    onBlockSlot(slot, event);
  };

  const handleUnblockClick = (event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent triggering the card click
    if (onUnblockSlot) {
      onUnblockSlot(slot, event);
    }
  };

  const handleConfirmClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (onConfirmAppointment && slot.appointment) {
      onConfirmAppointment(slot.appointment, event);
    }
  };

  const handleStartClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (onStartConsultation && slot.appointment) {
      onStartConsultation(slot.appointment, event);
    }
  };

  const handleCancelClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (onCancelAppointment && slot.appointment) {
      onCancelAppointment(slot.appointment, event);
    }
  };

  const getCardClasses = () => {
    const baseClasses = "transition-all duration-200 hover:shadow-md cursor-pointer";
    
    if (slot.type === 'available') {
      return `${baseClasses} border-2 border-dashed border-green-300 hover:border-green-400 hover:bg-green-50 ${className}`;
    } else if (slot.type === 'appointment') {
      return `${baseClasses} border-blue-300 bg-blue-50 hover:bg-blue-100 ${className}`;
    } else if (slot.type === 'blocked') {
      return `${baseClasses} border-red-300 bg-red-50 hover:bg-red-100 ${className}`;
    }
    
    return baseClasses;
  };

  const getSlotIcon = () => {
    if (slot.type === 'available') {
      return <Plus className="h-4 w-4 text-green-600" />;
    } else if (slot.type === 'appointment') {
      return <User className="h-4 w-4 text-blue-600" />;
    } else if (slot.type === 'blocked') {
      return <Lock className="h-4 w-4 text-red-600" />;
    }
    return <Clock className="h-4 w-4" />;
  };

  const getSlotTitle = () => {
    if (slot.type === 'available') {
      return 'Horário Disponível';
    } else if (slot.type === 'appointment' && slot.appointment) {
      return slot.appointment.title || 'Consulta Agendada';
    } else if (slot.type === 'blocked' && slot.block) {
      return slot.block.reason || 'Horário Bloqueado';
    }
    return 'Slot';
  };

  const getSlotSubtitle = () => {
    if (slot.type === 'appointment' && slot.appointment) {
      return slot.appointment.patient_name || 'Paciente';
    } else if (slot.type === 'blocked') {
      return 'Indisponível';
    }
    return 'Clique para agendar';
  };

  const getAppointmentActions = () => {
    if (slot.type !== 'appointment' || !slot.appointment) {
      return null;
    }

    const appointment = slot.appointment;
    const status = appointment.status;

    const actions = [];

    // Botão "Confirmar" para consultas agendadas (todos os usuários)
    if (status === 'scheduled') {
      actions.push(
        <Button
          key="confirm"
          size="sm"
          variant="outline"
          className="h-6 px-2 text-xs text-green-600 hover:text-green-700 hover:bg-green-50"
          onClick={handleConfirmClick}
          title="Confirmar consulta"
        >
          <CheckCircle className="h-3 w-3 mr-1" />
          Confirmar
        </Button>
      );
    }

    // Botão "Iniciar" para consultas confirmadas (só médicos)
    if (status === 'confirmed' && canStartConsultation) {
      actions.push(
        <Button
          key="start"
          size="sm"
          variant="outline"
          className="h-6 px-2 text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50"
          onClick={handleStartClick}
          title="Iniciar atendimento"
        >
          <Play className="h-3 w-3 mr-1" />
          Iniciar
        </Button>
      );
    }

    // Botão "Cancelar" para consultas que ainda não foram iniciadas
    if (['scheduled', 'confirmed'].includes(status)) {
      actions.push(
        <Button
          key="cancel"
          size="sm"
          variant="outline"
          className="h-6 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
          onClick={handleCancelClick}
          title="Cancelar consulta"
        >
          <X className="h-3 w-3" />
        </Button>
      );
    }

    return actions.length > 0 ? (
      <div className="flex flex-col space-y-1">
        {actions}
      </div>
    ) : null;
  };

  return (
    <Card className={getCardClasses()} onClick={handleCardClick}>
      <CardContent className="p-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2 flex-1 min-w-0">
            {getSlotIcon()}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">
                  {formatSlotTime(slot.start_time)} - {formatSlotTime(slot.end_time)}
                </span>
                {slot.type === 'appointment' && slot.appointment && (
                  <Badge
                    variant={getStatusBadgeVariant(slot.appointment.status as AppointmentStatus)}
                    className="text-xs"
                  >
                    {getStatusTextBR(slot.appointment.status as AppointmentStatus)}
                  </Badge>
                )}
              </div>
              <p className="text-xs text-muted-foreground truncate mt-1">
                {getSlotTitle()}
              </p>
              {(slot.type === 'appointment' || slot.type === 'blocked') && (
                <p className="text-xs text-muted-foreground truncate">
                  {getSlotSubtitle()}
                </p>
              )}
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="flex flex-col space-y-1 ml-2">
            {/* Appointment status actions */}
            {getAppointmentActions()}
            
            {/* Block/Unblock actions for available and blocked slots */}
            {slot.type === 'available' && canBlockSlots && (
              <Button
                size="sm"
                variant="outline"
                className="h-6 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
                onClick={handleBlockClick}
                title="Bloquear horário"
              >
                <Lock className="h-3 w-3" />
              </Button>
            )}
            
            {slot.type === 'blocked' && canBlockSlots && slot.block && onUnblockSlot && (
              <Button
                size="sm"
                variant="outline"
                className="h-6 px-2 text-xs text-green-600 hover:text-green-700 hover:bg-green-50"
                onClick={handleUnblockClick}
                title="Desbloquear horário"
              >
                <Lock className="h-3 w-3" />
              </Button>
            )}
            
            {slot.type === 'available' && (
              <div className="text-xs text-center text-green-600 font-medium">
                Livre
              </div>
            )}
          </div>
        </div>

        {/* Additional info for appointments */}
        {slot.type === 'appointment' && slot.appointment && slot.appointment.healthcare_professional_name && (
          <div className="mt-2 pt-2 border-t border-blue-200">
            <p className="text-xs text-blue-600 truncate">
              Dr(a). {slot.appointment.healthcare_professional_name}
            </p>
          </div>
        )}

        {/* Special indicator for in-progress and completed appointments */}
        {slot.type === 'appointment' && slot.appointment && 
         ['in_progress', 'completed'].includes(slot.appointment.status) && canStartConsultation && (
          <div className="mt-2 pt-2 border-t border-purple-200">
            <p className="text-xs text-purple-600 text-center font-medium">
              {slot.appointment.status === 'in_progress' ? 
                '🩺 Clique para acessar o prontuário' : 
                '📋 Clique para ver o prontuário'
              }
            </p>
          </div>
        )}

        {/* Click hint for available slots */}
        {slot.type === 'available' && (
          <div className="mt-2 pt-2 border-t border-green-200">
            <p className="text-xs text-green-600 text-center">
              Clique para agendar consulta
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TimeSlotCard;
