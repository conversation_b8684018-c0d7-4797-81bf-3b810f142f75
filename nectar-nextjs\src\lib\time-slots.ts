import { addMinutes, format, isSameDay, isWithinInterval, startOfDay, endOfDay } from 'date-fns';

export type TimeSlot = {
  id: string;
  start_time: Date;
  end_time: Date;
  isAvailable: boolean;
  isBlocked: boolean;
  type: 'available' | 'appointment' | 'blocked';
  appointment?: any;
  block?: any;
};

export type WorkingDay = {
  enabled: boolean;
  working_hours_start: string;
  working_hours_end: string;
  break_intervals: { start: string; end: string; }[];
};

export type ClinicSettings = {
  appointment_duration_minutes: number;
  timezone: string;
  weekly_schedule: {
    monday: WorkingDay;
    tuesday: WorkingDay;
    wednesday: WorkingDay;
    thursday: WorkingDay;
    friday: WorkingDay;
    saturday: WorkingDay;
    sunday: WorkingDay;
  };
};

// Legacy format for backward compatibility
export type LegacyClinicSettings = {
  working_hours_start: string;
  working_hours_end: string;
  working_days: number[];
  appointment_duration_minutes: number;
  allow_weekend_appointments: boolean;
};

/**
 * Generate time slots for a specific date based on clinic settings
 */
export function generateTimeSlots(
  date: Date,
  clinicSettings: ClinicSettings | LegacyClinicSettings,
  appointments: any[] = [],
  blocks: any[] = []
): TimeSlot[] {
  const slots: TimeSlot[] = [];
  
  // Ensure appointments and blocks are arrays
  const validAppointments = Array.isArray(appointments) ? appointments : [];
  const validBlocks = Array.isArray(blocks) ? blocks : [];
  
  // Check if we're using the new format or legacy format
  const isNewFormat = 'weekly_schedule' in clinicSettings;
  
  let dayConfig: WorkingDay | null = null;
  let appointmentDuration = 30;
  
  if (isNewFormat) {
    const newSettings = clinicSettings as ClinicSettings;
    appointmentDuration = newSettings.appointment_duration_minutes || 30;
    
    // Get day configuration
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const dayOfWeek = date.getDay();
    const dayName = dayNames[dayOfWeek] as keyof typeof newSettings.weekly_schedule;
    
    dayConfig = newSettings.weekly_schedule[dayName];
    
    // If day is not enabled, return empty slots
    if (!dayConfig || !dayConfig.enabled) {
      return slots;
    }
  } else {
    // Legacy format
    const legacySettings = clinicSettings as LegacyClinicSettings;
    appointmentDuration = legacySettings.appointment_duration_minutes || 30;
    
    // Check if the day is within working days (legacy format)
    const dayOfWeek = date.getDay();
    const workingDays = Array.isArray(legacySettings.working_days) ? legacySettings.working_days : [1, 2, 3, 4, 5];
    
    if (!legacySettings.allow_weekend_appointments && !workingDays.includes(dayOfWeek)) {
      return slots;
    }
    
    // Convert legacy format to day config
    dayConfig = {
      enabled: true,
      working_hours_start: legacySettings.working_hours_start || '08:00',
      working_hours_end: legacySettings.working_hours_end || '18:00',
      break_intervals: []
    };
  }
  
  if (!dayConfig) return slots;

  // Parse working hours
  const [startHour, startMinute] = dayConfig.working_hours_start.split(':').map(Number);
  const [endHour, endMinute] = dayConfig.working_hours_end.split(':').map(Number);

  // Create start and end times for the day
  const workingStart = new Date(date);
  workingStart.setHours(startHour, startMinute, 0, 0);
  
  const workingEnd = new Date(date);
  workingEnd.setHours(endHour, endMinute, 0, 0);

  // Generate slots based on appointment duration
  let currentTime = new Date(workingStart);
  
  while (currentTime < workingEnd) {
    const slotEnd = addMinutes(currentTime, appointmentDuration);
    
    // Don't create slot if it would extend beyond working hours
    if (slotEnd > workingEnd) {
      break;
    }

    // Check if this slot is within a break interval
    const isInBreakInterval = dayConfig.break_intervals.some(breakInterval => {
      const [breakStartHour, breakStartMinute] = breakInterval.start.split(':').map(Number);
      const [breakEndHour, breakEndMinute] = breakInterval.end.split(':').map(Number);
      
      const breakStart = new Date(date);
      breakStart.setHours(breakStartHour, breakStartMinute, 0, 0);
      
      const breakEnd = new Date(date);
      breakEnd.setHours(breakEndHour, breakEndMinute, 0, 0);
      
      return (
        (currentTime >= breakStart && currentTime < breakEnd) ||
        (slotEnd > breakStart && slotEnd <= breakEnd) ||
        (currentTime <= breakStart && slotEnd >= breakEnd)
      );
    });

    // Skip this slot if it's within a break interval
    if (isInBreakInterval) {
      currentTime = addMinutes(currentTime, appointmentDuration);
      continue;
    }

    // Check if this slot overlaps with any existing appointment
    const overlappingAppointment = validAppointments.find(apt => {
      if (apt.status === 'cancelled') return false;
      
      const aptStart = new Date(apt.start_time);
      const aptEnd = new Date(apt.end_time);
      
      return (
        (currentTime >= aptStart && currentTime < aptEnd) ||
        (slotEnd > aptStart && slotEnd <= aptEnd) ||
        (currentTime <= aptStart && slotEnd >= aptEnd)
      );
    });

    // Check if this slot overlaps with any blocked time
    const overlappingBlock = validBlocks.find(block => {
      const blockStart = new Date(block.start_time);
      const blockEnd = new Date(block.end_time);
      
      return (
        (currentTime >= blockStart && currentTime < blockEnd) ||
        (slotEnd > blockStart && slotEnd <= blockEnd) ||
        (currentTime <= blockStart && slotEnd >= blockEnd)
      );
    });

    // Determine slot type and availability
    let type: 'available' | 'appointment' | 'blocked' = 'available';
    let isAvailable = true;
    let isBlocked = false;
    let appointment = undefined;
    let block = undefined;

    if (overlappingAppointment) {
      type = 'appointment';
      isAvailable = false;
      appointment = overlappingAppointment;
    } else if (overlappingBlock) {
      type = 'blocked';
      isAvailable = false;
      isBlocked = true;
      block = overlappingBlock;
    }

    // Create slot
    const slot: TimeSlot = {
      id: `slot-${currentTime.getTime()}`,
      start_time: new Date(currentTime),
      end_time: new Date(slotEnd),
      isAvailable,
      isBlocked,
      type,
      appointment,
      block
    };

    slots.push(slot);

    // Move to next slot
    currentTime = addMinutes(currentTime, appointmentDuration);
  }

  return slots;
}

/**
 * Check if a time slot is available for booking
 */
export function isSlotAvailable(
  startTime: Date,
  endTime: Date,
  appointments: any[] = [],
  blocks: any[] = [],
  clinicSettings?: ClinicSettings | LegacyClinicSettings
): boolean {
  // Ensure appointments and blocks are arrays
  const validAppointments = Array.isArray(appointments) ? appointments : [];
  const validBlocks = Array.isArray(blocks) ? blocks : [];
  
  // Check if the time is within working hours and not in break intervals
  if (clinicSettings && 'weekly_schedule' in clinicSettings) {
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const dayOfWeek = startTime.getDay();
    const dayName = dayNames[dayOfWeek] as keyof typeof clinicSettings.weekly_schedule;
    const dayConfig = clinicSettings.weekly_schedule[dayName];
    
    // Check if day is enabled
    if (!dayConfig || !dayConfig.enabled) {
      return false;
    }
    
    // Check if time is within working hours
    const [startHour, startMinute] = dayConfig.working_hours_start.split(':').map(Number);
    const [endHour, endMinute] = dayConfig.working_hours_end.split(':').map(Number);
    
    const workingStart = new Date(startTime);
    workingStart.setHours(startHour, startMinute, 0, 0);
    
    const workingEnd = new Date(startTime);
    workingEnd.setHours(endHour, endMinute, 0, 0);
    
    if (startTime < workingStart || endTime > workingEnd) {
      return false;
    }
    
    // Check if time overlaps with break intervals
    const isInBreakInterval = dayConfig.break_intervals.some(breakInterval => {
      const [breakStartHour, breakStartMinute] = breakInterval.start.split(':').map(Number);
      const [breakEndHour, breakEndMinute] = breakInterval.end.split(':').map(Number);
      
      const breakStart = new Date(startTime);
      breakStart.setHours(breakStartHour, breakStartMinute, 0, 0);
      
      const breakEnd = new Date(startTime);
      breakEnd.setHours(breakEndHour, breakEndMinute, 0, 0);
      
      return (
        (startTime >= breakStart && startTime < breakEnd) ||
        (endTime > breakStart && endTime <= breakEnd) ||
        (startTime <= breakStart && endTime >= breakEnd)
      );
    });
    
    if (isInBreakInterval) {
      return false;
    }
  }
  
  // Check appointments
  const hasAppointmentConflict = validAppointments.some(apt => {
    if (apt.status === 'cancelled') return false;
    
    const aptStart = new Date(apt.start_time);
    const aptEnd = new Date(apt.end_time);
    
    return (
      (startTime >= aptStart && startTime < aptEnd) ||
      (endTime > aptStart && endTime <= aptEnd) ||
      (startTime <= aptStart && endTime >= aptEnd)
    );
  });

  // Check blocks
  const hasBlockConflict = validBlocks.some(block => {
    const blockStart = new Date(block.start_time);
    const blockEnd = new Date(block.end_time);
    
    return (
      (startTime >= blockStart && startTime < blockEnd) ||
      (endTime > blockStart && endTime <= blockEnd) ||
      (startTime <= blockStart && endTime >= blockEnd)
    );
  });

  return !hasAppointmentConflict && !hasBlockConflict;
}

/**
 * Format time for display in time slots
 */
export function formatSlotTime(date: Date): string {
  return format(date, 'HH:mm');
}

/**
 * Get slot duration in minutes
 */
export function getSlotDuration(slot: TimeSlot): number {
  return Math.round((slot.end_time.getTime() - slot.start_time.getTime()) / (1000 * 60));
}
