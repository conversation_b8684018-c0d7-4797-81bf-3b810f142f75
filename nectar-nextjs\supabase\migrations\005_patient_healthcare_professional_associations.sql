-- Migration: Patient-Healthcare Professional Associations
-- This migration creates a system to associate patients with multiple healthcare professionals
-- allowing secretaries to register patients that become visible to their associated doctors

-- 1. Create patient_healthcare_professional_associations table
CREATE TABLE IF NOT EXISTS patient_healthcare_professional_associations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  patient_id UUID NOT NULL REFERENCES patients(id) ON DELETE CASCADE,
  healthcare_professional_id UUID NOT NULL REFERENCES healthcare_professionals(id) ON DELETE CASCADE,
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Prevent duplicate associations
  CONSTRAINT unique_patient_professional UNIQUE (patient_id, healthcare_professional_id)
);

-- Enable RLS on patient_healthcare_professional_associations table
ALTER TABLE patient_healthcare_professional_associations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for patient_healthcare_professional_associations table
CREATE POLICY "Users can view associations for accessible patients" ON patient_healthcare_professional_associations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM patients p
      WHERE p.id = patient_healthcare_professional_associations.patient_id
      AND p.user_id IN (
        SELECT user_id FROM get_accessible_users(auth.uid())
      )
    )
  );

CREATE POLICY "Users can create associations for accessible patients" ON patient_healthcare_professional_associations
  FOR INSERT WITH CHECK (
    auth.uid() = created_by
    AND EXISTS (
      SELECT 1 FROM patients p
      WHERE p.id = patient_healthcare_professional_associations.patient_id
      AND p.user_id IN (
        SELECT user_id FROM get_accessible_users(auth.uid())
      )
    )
  );

CREATE POLICY "Users can update associations they created" ON patient_healthcare_professional_associations
  FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "Users can delete associations they created" ON patient_healthcare_professional_associations
  FOR DELETE USING (auth.uid() = created_by);

-- 2. Create function to get patients accessible to a user (including associated patients)
CREATE OR REPLACE FUNCTION get_accessible_patients(current_user_id UUID)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  name TEXT,
  email TEXT,
  phone TEXT,
  birth_date DATE,
  cpf TEXT,
  address TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  access_type TEXT -- 'direct' or 'associated'
) AS $$
BEGIN
  -- Get accessible user IDs
  CREATE TEMP TABLE temp_accessible_users AS
  SELECT user_id FROM get_accessible_users(current_user_id);
  
  -- Return patients directly accessible via user_id
  RETURN QUERY
  SELECT 
    p.id, p.user_id, p.name, p.email, p.phone, p.birth_date, 
    p.cpf, p.address, p.notes, p.created_at, p.updated_at,
    'direct'::TEXT as access_type
  FROM patients p
  WHERE p.user_id IN (SELECT user_id FROM temp_accessible_users);
  
  -- Return patients accessible via healthcare professional associations
  -- (only if current user is a healthcare professional)
  IF EXISTS (
    SELECT 1 FROM healthcare_professionals hp 
    WHERE hp.user_id = current_user_id
  ) THEN
    RETURN QUERY
    SELECT 
      p.id, p.user_id, p.name, p.email, p.phone, p.birth_date, 
      p.cpf, p.address, p.notes, p.created_at, p.updated_at,
      'associated'::TEXT as access_type
    FROM patients p
    INNER JOIN patient_healthcare_professional_associations phpa ON p.id = phpa.patient_id
    INNER JOIN healthcare_professionals hp ON phpa.healthcare_professional_id = hp.id
    WHERE hp.user_id = current_user_id
    AND p.id NOT IN (
      -- Exclude patients already returned in direct access
      SELECT p2.id FROM patients p2 
      WHERE p2.user_id IN (SELECT user_id FROM temp_accessible_users)
    );
  END IF;
  
  DROP TABLE temp_accessible_users;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_patient_healthcare_professional_associations_patient_id 
  ON patient_healthcare_professional_associations(patient_id);

CREATE INDEX IF NOT EXISTS idx_patient_healthcare_professional_associations_healthcare_professional_id 
  ON patient_healthcare_professional_associations(healthcare_professional_id);

CREATE INDEX IF NOT EXISTS idx_patient_healthcare_professional_associations_created_by 
  ON patient_healthcare_professional_associations(created_by);

-- 4. Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_patient_healthcare_professional_associations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_patient_healthcare_professional_associations_updated_at
  BEFORE UPDATE ON patient_healthcare_professional_associations
  FOR EACH ROW
  EXECUTE FUNCTION update_patient_healthcare_professional_associations_updated_at();
